#!/usr/bin/env python3
"""
Test Settings Loading.

Author: inkbytefo
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings

def test_settings():
    """Test settings loading."""
    print("⚙️  Settings Test")
    print("=" * 20)
    
    settings = Settings()
    
    print(f"✅ OpenAI Model: {settings.ai.openai_model}")
    print(f"✅ Sentiment Model: {settings.ai.sentiment_model}")
    print(f"✅ Prediction Model: {settings.ai.prediction_model}")
    print(f"✅ Analysis Model: {settings.ai.analysis_model}")
    print(f"✅ General Model: {settings.ai.general_model}")
    print(f"✅ Base URL: {settings.ai.openai_base_url}")
    
    print("\n🎉 Settings loaded successfully!")

if __name__ == "__main__":
    test_settings()
