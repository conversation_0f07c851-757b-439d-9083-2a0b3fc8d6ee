"""
AI Provider Manager for Multi-Provider Support.

This module manages multiple AI providers with automatic fallback,
load balancing, and intelligent routing based on request type and provider health.

Author: inkbytefo
"""

import asyncio
import logging
import random
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from .base_provider import BaseAIProvider, AIRequest, AIResponse, ProviderConfig
from .openai_compatible_provider import ProviderFactory
from ...config.settings import Settings


class RoutingStrategy(Enum):
    """Provider routing strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_LATENCY = "least_latency"
    LEAST_COST = "least_cost"
    HEALTH_BASED = "health_based"
    RANDOM = "random"
    PRIORITY = "priority"


@dataclass
class ProviderPriority:
    """Provider priority configuration."""
    provider_name: str
    priority: int  # Lower number = higher priority
    use_cases: List[str]  # e.g., ['sentiment', 'prediction', 'general']
    max_cost_per_request: float = 1.0


class ProviderManager:
    """
    Manages multiple AI providers with intelligent routing and fallback.
    
    Features:
    - Automatic provider fallback
    - Load balancing across providers
    - Cost optimization
    - Health monitoring
    - Request routing based on use case
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Provider storage
        self.providers: Dict[str, BaseAIProvider] = {}
        self.provider_priorities: Dict[str, ProviderPriority] = {}
        
        # Routing configuration
        self.routing_strategy = RoutingStrategy.HEALTH_BASED
        self.fallback_enabled = True
        self.max_retries = 3
        
        # Statistics
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_cost = 0.0
        
        # Round robin counter
        self._round_robin_index = 0
        
        # Health check interval
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = datetime.utcnow()
    
    async def initialize(self):
        """Initialize the provider manager."""
        try:
            # Load provider configurations from settings
            await self._load_provider_configs()
            
            # Initialize all providers
            for provider in self.providers.values():
                try:
                    await provider.initialize()
                except Exception as e:
                    self.logger.error(f"Failed to initialize provider {provider.config.name}: {e}")
            
            # Start health monitoring
            asyncio.create_task(self._health_monitor())
            
            self.logger.info(f"✅ Provider manager initialized with {len(self.providers)} providers")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize provider manager: {e}")
            raise
    
    async def close(self):
        """Close all providers and cleanup resources."""
        for provider in self.providers.values():
            try:
                await provider.close()
            except Exception as e:
                self.logger.error(f"Error closing provider {provider.config.name}: {e}")
        
        self.logger.info("Provider manager closed")
    
    async def _load_provider_configs(self):
        """Load provider configurations from settings."""
        # OpenAI-compatible provider (OpenAI, OpenRouter, etc.)
        if hasattr(self.settings.ai, 'openai_api_key') and self.settings.ai.openai_api_key:
            # Create model mapping based on settings
            model_mapping = {
                'sentiment': self.settings.ai.sentiment_model,
                'prediction': self.settings.ai.prediction_model,
                'analysis': self.settings.ai.analysis_model,
                'general': self.settings.ai.general_model,
                'gpt-4': self.settings.ai.analysis_model,
                'gpt-4-turbo': self.settings.ai.prediction_model,
                'gpt-3.5-turbo': self.settings.ai.general_model
            }

            # Determine provider name based on base URL
            base_url = self.settings.ai.openai_base_url
            if 'openrouter.ai' in base_url:
                provider_name = 'openrouter'
                max_cost = 0.0  # Free models
            elif 'openai.com' in base_url:
                provider_name = 'openai'
                max_cost = 0.50
            else:
                provider_name = 'custom_openai'
                max_cost = 0.50

            provider = ProviderFactory.create_openai_provider(
                api_key=self.settings.ai.openai_api_key,
                base_url=base_url,
                settings=self.settings,
                model_mapping=model_mapping
            )

            # Update provider config name
            provider.config.name = provider_name

            self.providers[provider_name] = provider
            self.provider_priorities[provider_name] = ProviderPriority(
                provider_name=provider_name,
                priority=1,
                use_cases=['sentiment', 'prediction', 'analysis', 'general'],
                max_cost_per_request=max_cost
            )
        
        # Azure OpenAI
        if hasattr(self.settings.ai, 'azure_openai_key') and self.settings.ai.azure_openai_key:
            provider = ProviderFactory.create_azure_provider(
                api_key=self.settings.ai.azure_openai_key,
                base_url=self.settings.ai.azure_openai_endpoint or '',
                settings=self.settings
            )
            self.providers['azure'] = provider
            self.provider_priorities['azure'] = ProviderPriority(
                provider_name='azure',
                priority=2,
                use_cases=['sentiment', 'prediction', 'analysis', 'general'],
                max_cost_per_request=0.50
            )
        
        # Local provider (Ollama, LM Studio, etc.)
        if hasattr(self.settings.ai, 'local_ai_url') and self.settings.ai.local_ai_url:
            provider = ProviderFactory.create_local_provider(
                base_url=self.settings.ai.local_ai_url,
                settings=self.settings
            )
            self.providers['local'] = provider
            self.provider_priorities['local'] = ProviderPriority(
                provider_name='local',
                priority=3,
                use_cases=['sentiment', 'prediction', 'analysis', 'general'],
                max_cost_per_request=0.0  # Free
            )
        
        # Custom providers from settings
        if hasattr(self.settings, 'custom_ai_providers'):
            for provider_config in self.settings.custom_ai_providers:
                provider = ProviderFactory.create_custom_provider(
                    name=provider_config['name'],
                    base_url=provider_config['base_url'],
                    api_key=provider_config.get('api_key', ''),
                    settings=self.settings,
                    model_mapping=provider_config.get('model_mapping', {})
                )
                self.providers[provider_config['name']] = provider
                self.provider_priorities[provider_config['name']] = ProviderPriority(
                    provider_name=provider_config['name'],
                    priority=provider_config.get('priority', 10),
                    use_cases=provider_config.get('use_cases', ['general']),
                    max_cost_per_request=provider_config.get('max_cost', 1.0)
                )
    
    async def generate(self, request: AIRequest, use_case: str = 'general', 
                      preferred_provider: Optional[str] = None) -> AIResponse:
        """
        Generate AI response with intelligent provider selection.
        
        Args:
            request: AI request object
            use_case: Use case for provider selection (sentiment, prediction, general)
            preferred_provider: Preferred provider name (optional)
            
        Returns:
            AI response object
        """
        self.total_requests += 1
        
        # Get suitable providers for this use case
        suitable_providers = self._get_suitable_providers(use_case, request)
        
        if not suitable_providers:
            raise Exception("No suitable providers available")
        
        # If preferred provider is specified and available, try it first
        if preferred_provider and preferred_provider in suitable_providers:
            suitable_providers = [preferred_provider] + [p for p in suitable_providers if p != preferred_provider]
        
        # Try providers in order
        last_exception = None
        for provider_name in suitable_providers:
            try:
                provider = self.providers[provider_name]
                
                # Check if provider is healthy
                if not provider.is_healthy():
                    self.logger.warning(f"Provider {provider_name} is unhealthy, skipping")
                    continue
                
                # Make the request
                response = await provider.generate(request)
                
                # Update statistics
                self.successful_requests += 1
                self.total_cost += response.cost_usd
                
                self.logger.debug(f"Request successful with provider {provider_name}")
                return response
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Provider {provider_name} failed: {e}")
                
                if not self.fallback_enabled:
                    break
        
        # All providers failed
        self.failed_requests += 1
        self.logger.error(f"All providers failed for request. Last error: {last_exception}")
        raise last_exception or Exception("All providers failed")
    
    def _get_suitable_providers(self, use_case: str, request: AIRequest) -> List[str]:
        """Get list of suitable providers for the use case, ordered by routing strategy."""
        # First, prioritize providers that have valid API keys and configurations
        working_providers = []
        fallback_providers = []

        for name, priority in self.provider_priorities.items():
            if use_case in priority.use_cases and name in self.providers:
                provider = self.providers[name]
                if not provider.config.enabled:
                    continue

                # Check if provider has proper configuration
                if self._is_provider_properly_configured(name, provider):
                    working_providers.append(name)
                else:
                    fallback_providers.append(name)

        # If no properly configured providers, use all enabled providers as fallback
        if not working_providers:
            working_providers = [name for name, provider in self.providers.items()
                               if provider.config.enabled]

        # Sort by routing strategy, prioritizing working providers
        sorted_working = self._sort_providers_by_strategy(working_providers, request)
        sorted_fallback = self._sort_providers_by_strategy(fallback_providers, request)

        return sorted_working + sorted_fallback

    def _is_provider_properly_configured(self, name: str, provider) -> bool:
        """Check if a provider is properly configured and likely to work."""
        try:
            # Check basic configuration
            if not provider.config.api_key and provider.config.provider_type.value != 'local':
                return False

            # Check specific provider configurations
            if name == 'openrouter':
                # OpenRouter should have API key and correct base URL
                return (provider.config.api_key and
                       'openrouter.ai' in provider.config.base_url)

            elif name == 'openai':
                # OpenAI should have API key and correct base URL
                return (provider.config.api_key and
                       'openai.com' in provider.config.base_url)

            elif name == 'azure':
                # Azure should have API key and proper endpoint
                return (provider.config.api_key and
                       provider.config.base_url != 'https://your-resource.openai.azure.com/')

            elif name == 'local':
                # Local provider doesn't need API key but needs proper URL
                return provider.config.base_url != 'http://localhost:11434'

            else:
                # For other providers, just check if API key exists
                return bool(provider.config.api_key)

        except Exception:
            return False

    def _sort_providers_by_strategy(self, provider_names: List[str], request: AIRequest) -> List[str]:
        """Sort providers based on the routing strategy."""
        if self.routing_strategy == RoutingStrategy.PRIORITY:
            return sorted(provider_names, 
                         key=lambda name: self.provider_priorities.get(name, ProviderPriority(name, 999, [])).priority)
        
        elif self.routing_strategy == RoutingStrategy.LEAST_LATENCY:
            return sorted(provider_names, 
                         key=lambda name: self.providers[name].average_latency)
        
        elif self.routing_strategy == RoutingStrategy.LEAST_COST:
            # Estimate cost for each provider
            costs = []
            for name in provider_names:
                provider = self.providers[name]
                estimated_tokens = provider._estimate_tokens(request)
                estimated_cost = provider._calculate_cost(
                    {'prompt_tokens': estimated_tokens, 'completion_tokens': estimated_tokens // 2},
                    request.model
                )
                costs.append((estimated_cost, name))
            
            return [name for _, name in sorted(costs)]
        
        elif self.routing_strategy == RoutingStrategy.HEALTH_BASED:
            # Sort by health score (lower error rate = better)
            health_scores = []
            for name in provider_names:
                provider = self.providers[name]
                error_rate = provider.error_count / max(1, provider.total_requests)
                health_scores.append((error_rate, name))
            
            return [name for _, name in sorted(health_scores)]
        
        elif self.routing_strategy == RoutingStrategy.ROUND_ROBIN:
            # Rotate the list based on round robin index
            self._round_robin_index = (self._round_robin_index + 1) % len(provider_names)
            return provider_names[self._round_robin_index:] + provider_names[:self._round_robin_index]
        
        elif self.routing_strategy == RoutingStrategy.RANDOM:
            shuffled = provider_names.copy()
            random.shuffle(shuffled)
            return shuffled
        
        else:
            return provider_names
    
    async def _health_monitor(self):
        """Background task to monitor provider health."""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                # Perform health checks
                for name, provider in self.providers.items():
                    try:
                        health_result = await provider.health_check()
                        if not health_result['healthy']:
                            self.logger.warning(f"Provider {name} health check failed: {health_result.get('error', 'Unknown')}")
                    except Exception as e:
                        self.logger.error(f"Health check error for provider {name}: {e}")
                
                self.last_health_check = datetime.utcnow()
                
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
    
    def get_provider_statistics(self) -> Dict[str, Any]:
        """Get statistics for all providers."""
        stats = {
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate': round(self.successful_requests / max(1, self.total_requests) * 100, 2),
            'total_cost_usd': round(self.total_cost, 4),
            'routing_strategy': self.routing_strategy.value,
            'providers': {}
        }
        
        for name, provider in self.providers.items():
            stats['providers'][name] = provider.get_statistics()
        
        return stats
    
    def set_routing_strategy(self, strategy: RoutingStrategy):
        """Set the provider routing strategy."""
        self.routing_strategy = strategy
        self.logger.info(f"Routing strategy changed to {strategy.value}")
    
    def enable_provider(self, provider_name: str):
        """Enable a specific provider."""
        if provider_name in self.providers:
            self.providers[provider_name].config.enabled = True
            self.logger.info(f"Provider {provider_name} enabled")
    
    def disable_provider(self, provider_name: str):
        """Disable a specific provider."""
        if provider_name in self.providers:
            self.providers[provider_name].config.enabled = False
            self.logger.info(f"Provider {provider_name} disabled")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return list(self.providers.keys())
    
    def get_healthy_providers(self) -> List[str]:
        """Get list of healthy provider names."""
        return [name for name, provider in self.providers.items() 
                if provider.config.enabled and provider.is_healthy()]


# Global provider manager instance
provider_manager: Optional[ProviderManager] = None


def get_provider_manager() -> ProviderManager:
    """Get the global provider manager instance."""
    global provider_manager
    if provider_manager is None:
        from ...config.settings import Settings
        settings = Settings()
        provider_manager = ProviderManager(settings)
    return provider_manager
