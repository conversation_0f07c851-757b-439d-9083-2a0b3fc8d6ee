"""
Market Data Collector for AI Trading Bot System.

This module collects real-time and historical market data from multiple
exchanges and stores it in the database for analysis.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from decimal import Decimal

from ..config.settings import Settings
from ..config.database import get_db_manager
from ..config.models import MarketData, TradingPair
from ..exchanges.exchange_manager import ExchangeManager
from ..monitoring.metrics import get_metrics_collector


class MarketDataCollector:
    """
    Collects and processes market data from multiple exchanges.
    
    Handles real-time data collection, historical data backfill,
    and data storage with proper normalization.
    """
    
    def __init__(self, settings: Settings, exchange_manager: ExchangeManager):
        self.settings = settings
        self.exchange_manager = exchange_manager
        self.db_manager = get_db_manager()
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Collection state
        self.is_running = False
        self.collection_tasks: Dict[str, asyncio.Task] = {}
        
        # Configuration
        self.trading_pairs = settings.trading.default_trading_pairs
        self.timeframes = settings.market_data.timeframes
        self.collection_interval = settings.market_data.collection_interval
        
        # Data cache
        self.latest_data: Dict[str, Dict[str, Any]] = {}
        self.data_quality_stats = {}
    
    async def initialize(self):
        """Initialize the market data collector."""
        try:
            self.logger.info("Initializing Market Data Collector...")

            # Initialize database connection
            try:
                self.db_manager.initialize()
                self.db_manager.create_tables()
            except Exception as e:
                self.logger.warning(f"Database initialization failed: {e}")
                # Continue without database for now

            # Ensure trading pairs exist in database (if database is available)
            try:
                await self._ensure_trading_pairs()
            except Exception as e:
                self.logger.warning(f"Failed to ensure trading pairs: {e}")

            # Initialize data quality tracking
            self._init_data_quality_tracking()

            self.logger.info("Market Data Collector initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize Market Data Collector: {e}")
            raise
    
    async def _ensure_trading_pairs(self):
        """Ensure trading pairs exist in the database."""
        try:
            with self.db_manager.session_scope() as session:
                for pair_symbol in self.trading_pairs:
                    # Parse symbol (e.g., "BTC/USDT" -> base="BTC", quote="USDT")
                    if '/' in pair_symbol:
                        base, quote = pair_symbol.split('/')
                    else:
                        continue
                    
                    # Check if pair exists for each active exchange
                    for exchange_name in self.exchange_manager.get_active_exchanges():
                        existing_pair = session.query(TradingPair).filter_by(
                            symbol=pair_symbol,
                            exchange=exchange_name
                        ).first()
                        
                        if not existing_pair:
                            trading_pair = TradingPair(
                                symbol=pair_symbol,
                                base_asset=base,
                                quote_asset=quote,
                                exchange=exchange_name,
                                is_active=True
                            )
                            session.add(trading_pair)
                            self.logger.info(f"Added trading pair: {pair_symbol} on {exchange_name}")
                
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to ensure trading pairs: {e}")
    
    def _init_data_quality_tracking(self):
        """Initialize data quality tracking."""
        self.data_quality_stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'last_update': None,
            'exchanges': {}
        }
        
        for exchange in self.exchange_manager.get_active_exchanges():
            self.data_quality_stats['exchanges'][exchange] = {
                'updates': 0,
                'errors': 0,
                'last_update': None
            }
    
    async def start(self):
        """Start market data collection."""
        try:
            if self.is_running:
                self.logger.warning("Market data collection already running")
                return
            
            self.logger.info("🚀 Starting market data collection...")
            self.is_running = True
            
            # Start collection tasks for each exchange
            for exchange_name in self.exchange_manager.get_active_exchanges():
                task = asyncio.create_task(
                    self._collection_loop(exchange_name)
                )
                self.collection_tasks[exchange_name] = task
            
            # Start data quality monitoring
            asyncio.create_task(self._quality_monitoring_loop())
            
            self.logger.info("✅ Market data collection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start market data collection: {e}")
            raise
    
    async def stop(self):
        """Stop market data collection."""
        try:
            self.logger.info("🛑 Stopping market data collection...")
            self.is_running = False
            
            # Cancel all collection tasks
            for exchange_name, task in self.collection_tasks.items():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                self.logger.info(f"Stopped collection for {exchange_name}")
            
            self.collection_tasks.clear()
            self.logger.info("✅ Market data collection stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping market data collection: {e}")
    
    async def _collection_loop(self, exchange_name: str):
        """Main collection loop for an exchange."""
        self.logger.info(f"Starting collection loop for {exchange_name}")
        
        while self.is_running:
            try:
                # Collect data for all trading pairs
                for pair_symbol in self.trading_pairs:
                    await self._collect_pair_data(exchange_name, pair_symbol)
                
                # Update exchange stats
                self.data_quality_stats['exchanges'][exchange_name]['last_update'] = datetime.utcnow()
                
                # Wait before next collection cycle
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in collection loop for {exchange_name}: {e}")
                self.data_quality_stats['exchanges'][exchange_name]['errors'] += 1
                await asyncio.sleep(10)  # Wait before retrying
    
    async def _collect_pair_data(self, exchange_name: str, pair_symbol: str):
        """Collect data for a specific trading pair."""
        try:
            start_time = datetime.utcnow()
            
            # Collect ticker data
            ticker = await self.exchange_manager.get_ticker(exchange_name, pair_symbol)
            if ticker:
                await self._store_ticker_data(exchange_name, pair_symbol, ticker)
            
            # Collect OHLCV data for different timeframes
            for timeframe in self.timeframes:
                ohlcv_data = await self.exchange_manager.get_ohlcv(
                    exchange_name, pair_symbol, timeframe, limit=1
                )
                if ohlcv_data:
                    await self._store_ohlcv_data(
                        exchange_name, pair_symbol, timeframe, ohlcv_data
                    )
            
            # Update cache
            cache_key = f"{exchange_name}:{pair_symbol}"
            self.latest_data[cache_key] = {
                'ticker': ticker,
                'timestamp': datetime.utcnow(),
                'exchange': exchange_name,
                'symbol': pair_symbol
            }
            
            # Record metrics
            collection_time = (datetime.utcnow() - start_time).total_seconds()
            self.metrics.record_market_data_update(
                exchange_name, pair_symbol, "collection", collection_time
            )
            
            # Update stats
            self.data_quality_stats['successful_updates'] += 1
            self.data_quality_stats['exchanges'][exchange_name]['updates'] += 1
            
        except Exception as e:
            self.logger.error(f"Failed to collect data for {pair_symbol} on {exchange_name}: {e}")
            self.data_quality_stats['failed_updates'] += 1
            self.data_quality_stats['exchanges'][exchange_name]['errors'] += 1
    
    async def _store_ticker_data(self, exchange_name: str, pair_symbol: str, ticker: Dict[str, Any]):
        """Store ticker data (can be used for real-time price updates)."""
        try:
            # For now, we'll just log the ticker data
            # In a full implementation, you might store this in a time-series database
            self.logger.debug(f"Ticker {pair_symbol} on {exchange_name}: {ticker.get('last', 'N/A')}")
            
        except Exception as e:
            self.logger.error(f"Failed to store ticker data: {e}")
    
    async def _store_ohlcv_data(self, exchange_name: str, pair_symbol: str,
                               timeframe: str, ohlcv_data: List[List]):
        """Store OHLCV data in the database."""
        try:
            # Check if database is initialized
            if not hasattr(self.db_manager, '_engine') or self.db_manager._engine is None:
                self.logger.debug("Database not initialized, skipping OHLCV storage")
                return

            with self.db_manager.session_scope() as session:
                # Get trading pair
                trading_pair = session.query(TradingPair).filter_by(
                    symbol=pair_symbol,
                    exchange=exchange_name
                ).first()
                
                if not trading_pair:
                    self.logger.warning(f"Trading pair not found: {pair_symbol} on {exchange_name}")
                    return
                
                # Process each OHLCV record
                for ohlcv in ohlcv_data:
                    timestamp = datetime.fromtimestamp(ohlcv[0] / 1000)  # Convert from milliseconds
                    
                    # Check if record already exists
                    existing_record = session.query(MarketData).filter_by(
                        trading_pair_id=trading_pair.id,
                        timeframe=timeframe,
                        timestamp=timestamp
                    ).first()
                    
                    if existing_record:
                        # Update existing record
                        existing_record.open_price = Decimal(str(ohlcv[1]))
                        existing_record.high_price = Decimal(str(ohlcv[2]))
                        existing_record.low_price = Decimal(str(ohlcv[3]))
                        existing_record.close_price = Decimal(str(ohlcv[4]))
                        existing_record.volume = Decimal(str(ohlcv[5]))
                        existing_record.updated_at = datetime.utcnow()
                    else:
                        # Create new record
                        market_data = MarketData(
                            trading_pair_id=trading_pair.id,
                            timeframe=timeframe,
                            timestamp=timestamp,
                            open_price=Decimal(str(ohlcv[1])),
                            high_price=Decimal(str(ohlcv[2])),
                            low_price=Decimal(str(ohlcv[3])),
                            close_price=Decimal(str(ohlcv[4])),
                            volume=Decimal(str(ohlcv[5]))
                        )
                        session.add(market_data)
                
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to store OHLCV data: {e}")
    
    async def _quality_monitoring_loop(self):
        """Monitor data quality and report metrics."""
        while self.is_running:
            try:
                # Calculate quality metrics
                total_updates = self.data_quality_stats['successful_updates'] + self.data_quality_stats['failed_updates']
                if total_updates > 0:
                    success_rate = self.data_quality_stats['successful_updates'] / total_updates
                    
                    # Record quality metrics
                    self.metrics.record_custom_metric(
                        "market_data_success_rate", success_rate * 100
                    )
                
                # Log quality report
                if total_updates > 0:
                    self.logger.info(
                        f"📊 Data Quality Report: "
                        f"{self.data_quality_stats['successful_updates']}/{total_updates} "
                        f"({success_rate:.1%} success rate)"
                    )
                
                # Reset counters periodically
                if total_updates > 1000:
                    self.data_quality_stats['successful_updates'] = 0
                    self.data_quality_stats['failed_updates'] = 0
                
                await asyncio.sleep(300)  # Report every 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in quality monitoring: {e}")
                await asyncio.sleep(60)
    
    async def get_latest_data(self) -> Dict[str, Any]:
        """Get latest collected market data."""
        return {
            'data': self.latest_data,
            'stats': self.data_quality_stats,
            'timestamp': datetime.utcnow()
        }
    
    async def get_historical_data(self, exchange_name: str, pair_symbol: str, 
                                 timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get historical market data from database."""
        try:
            with self.db_manager.session_scope() as session:
                # Get trading pair
                trading_pair = session.query(TradingPair).filter_by(
                    symbol=pair_symbol,
                    exchange=exchange_name
                ).first()
                
                if not trading_pair:
                    return []
                
                # Query historical data
                market_data = session.query(MarketData).filter_by(
                    trading_pair_id=trading_pair.id,
                    timeframe=timeframe
                ).order_by(MarketData.timestamp.desc()).limit(limit).all()
                
                # Convert to list of dictionaries
                result = []
                for data in market_data:
                    result.append({
                        'timestamp': data.timestamp,
                        'open': float(data.open_price),
                        'high': float(data.high_price),
                        'low': float(data.low_price),
                        'close': float(data.close_price),
                        'volume': float(data.volume)
                    })
                
                return result
                
        except Exception as e:
            self.logger.error(f"Failed to get historical data: {e}")
            return []
    
    def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status."""
        return {
            'is_running': self.is_running,
            'active_exchanges': list(self.collection_tasks.keys()),
            'trading_pairs': self.trading_pairs,
            'timeframes': self.timeframes,
            'collection_interval': self.collection_interval,
            'quality_stats': self.data_quality_stats
        }
