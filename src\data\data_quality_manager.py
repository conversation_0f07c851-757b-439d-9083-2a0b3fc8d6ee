"""
Data Quality Manager for AI Trading Bot System.

This module monitors and ensures data quality across all data sources,
implementing validation rules and quality metrics.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum

from ..config.settings import Settings
from ..config.database import get_db_manager
from ..monitoring.metrics import get_metrics_collector
from ..monitoring.alerts import get_alert_manager, AlertLevel, AlertType


class DataQualityLevel(Enum):
    """Data quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class QualityMetric:
    """Data quality metric."""
    name: str
    value: float
    threshold: float
    status: DataQualityLevel
    description: str
    timestamp: datetime


@dataclass
class QualityReport:
    """Data quality report."""
    source: str
    overall_score: float
    overall_status: DataQualityLevel
    metrics: List[QualityMetric]
    recommendations: List[str]
    timestamp: datetime


class DataQualityManager:
    """
    Monitors and ensures data quality across all data sources.
    
    Implements validation rules, quality metrics, and alerting
    for market data, news, and social media data.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.db_manager = get_db_manager()
        self.metrics = get_metrics_collector()
        self.alert_manager = get_alert_manager()
        self.logger = logging.getLogger(__name__)
        
        # Quality monitoring state
        self.is_running = False
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Configuration
        self.monitoring_interval = 300  # 5 minutes
        self.quality_thresholds = {
            'completeness': 0.95,  # 95% data completeness
            'accuracy': 0.98,      # 98% data accuracy
            'timeliness': 0.90,    # 90% on-time data
            'consistency': 0.95,   # 95% data consistency
            'validity': 0.99       # 99% valid data format
        }
        
        # Quality history
        self.quality_history: Dict[str, List[QualityReport]] = {}
        self.max_history_length = 100
        
        # Data source configurations
        self.data_sources = {
            'market_data': {
                'enabled': True,
                'critical': True,
                'checks': ['completeness', 'timeliness', 'validity', 'consistency']
            },
            'news_data': {
                'enabled': True,
                'critical': False,
                'checks': ['completeness', 'timeliness', 'validity']
            },
            'social_data': {
                'enabled': True,
                'critical': False,
                'checks': ['completeness', 'timeliness', 'validity']
            }
        }
    
    async def initialize(self):
        """Initialize the data quality manager."""
        try:
            self.logger.info("Initializing Data Quality Manager...")
            
            # Initialize quality history for each source
            for source_name in self.data_sources:
                self.quality_history[source_name] = []
            
            self.logger.info("✅ Data Quality Manager initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Data Quality Manager: {e}")
            raise
    
    async def start(self):
        """Start data quality monitoring."""
        try:
            if self.is_running:
                self.logger.warning("Data quality monitoring already running")
                return
            
            self.logger.info("🚀 Starting data quality monitoring...")
            self.is_running = True
            
            # Start monitoring tasks for each enabled source
            for source_name, config in self.data_sources.items():
                if config.get('enabled', False):
                    task = asyncio.create_task(
                        self._monitoring_loop(source_name)
                    )
                    self.monitoring_tasks.append(task)
            
            # Start quality reporting
            asyncio.create_task(self._quality_reporting_loop())
            
            self.logger.info("✅ Data quality monitoring started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start data quality monitoring: {e}")
            raise
    
    async def stop(self):
        """Stop data quality monitoring."""
        try:
            self.logger.info("🛑 Stopping data quality monitoring...")
            self.is_running = False
            
            # Cancel all monitoring tasks
            for task in self.monitoring_tasks:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            self.monitoring_tasks.clear()
            self.logger.info("✅ Data quality monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping data quality monitoring: {e}")
    
    async def _monitoring_loop(self, source_name: str):
        """Main monitoring loop for a data source."""
        self.logger.info(f"Starting quality monitoring for {source_name}")
        
        while self.is_running:
            try:
                # Generate quality report
                report = await self._generate_quality_report(source_name)
                
                # Store report in history
                self._store_quality_report(source_name, report)
                
                # Check for quality issues and send alerts
                await self._check_quality_alerts(source_name, report)
                
                # Record metrics
                self._record_quality_metrics(source_name, report)
                
                # Wait before next check
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in quality monitoring for {source_name}: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _generate_quality_report(self, source_name: str) -> QualityReport:
        """Generate quality report for a data source."""
        try:
            config = self.data_sources[source_name]
            metrics = []
            
            # Run quality checks
            for check_name in config['checks']:
                metric = await self._run_quality_check(source_name, check_name)
                if metric:
                    metrics.append(metric)
            
            # Calculate overall score
            overall_score = sum(m.value for m in metrics) / len(metrics) if metrics else 0.0
            overall_status = self._determine_quality_level(overall_score)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(metrics)
            
            return QualityReport(
                source=source_name,
                overall_score=overall_score,
                overall_status=overall_status,
                metrics=metrics,
                recommendations=recommendations,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error generating quality report for {source_name}: {e}")
            return QualityReport(
                source=source_name,
                overall_score=0.0,
                overall_status=DataQualityLevel.CRITICAL,
                metrics=[],
                recommendations=["Failed to generate quality report"],
                timestamp=datetime.utcnow()
            )
    
    async def _run_quality_check(self, source_name: str, check_name: str) -> Optional[QualityMetric]:
        """Run a specific quality check."""
        try:
            if check_name == 'completeness':
                return await self._check_completeness(source_name)
            elif check_name == 'timeliness':
                return await self._check_timeliness(source_name)
            elif check_name == 'validity':
                return await self._check_validity(source_name)
            elif check_name == 'consistency':
                return await self._check_consistency(source_name)
            elif check_name == 'accuracy':
                return await self._check_accuracy(source_name)
            else:
                self.logger.warning(f"Unknown quality check: {check_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error running quality check {check_name} for {source_name}: {e}")
            return None
    
    async def _check_completeness(self, source_name: str) -> QualityMetric:
        """Check data completeness."""
        try:
            # Implementation depends on data source
            if source_name == 'market_data':
                return await self._check_market_data_completeness()
            elif source_name == 'news_data':
                return await self._check_news_data_completeness()
            elif source_name == 'social_data':
                return await self._check_social_data_completeness()
            else:
                return QualityMetric(
                    name='completeness',
                    value=0.0,
                    threshold=self.quality_thresholds['completeness'],
                    status=DataQualityLevel.CRITICAL,
                    description=f"Unknown source: {source_name}",
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            self.logger.error(f"Error checking completeness for {source_name}: {e}")
            return QualityMetric(
                name='completeness',
                value=0.0,
                threshold=self.quality_thresholds['completeness'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error checking completeness: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_market_data_completeness(self) -> QualityMetric:
        """Check market data completeness."""
        try:
            # Check if we have recent data for all trading pairs
            from ..config.models import MarketData, TradingPair
            
            with self.db_manager.session_scope() as session:
                # Get all active trading pairs
                trading_pairs = session.query(TradingPair).filter_by(is_active=True).all()
                
                if not trading_pairs:
                    return QualityMetric(
                        name='completeness',
                        value=0.0,
                        threshold=self.quality_thresholds['completeness'],
                        status=DataQualityLevel.CRITICAL,
                        description="No active trading pairs found",
                        timestamp=datetime.utcnow()
                    )
                
                # Check for recent data (last 5 minutes)
                cutoff_time = datetime.utcnow() - timedelta(minutes=5)
                pairs_with_data = 0
                
                for pair in trading_pairs:
                    recent_data = session.query(MarketData).filter(
                        MarketData.trading_pair_id == pair.id,
                        MarketData.timestamp >= cutoff_time
                    ).first()
                    
                    if recent_data:
                        pairs_with_data += 1
                
                # Calculate completeness score
                completeness_score = pairs_with_data / len(trading_pairs)
                status = self._determine_quality_level(completeness_score)
                
                return QualityMetric(
                    name='completeness',
                    value=completeness_score,
                    threshold=self.quality_thresholds['completeness'],
                    status=status,
                    description=f"{pairs_with_data}/{len(trading_pairs)} trading pairs have recent data",
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            self.logger.error(f"Error checking market data completeness: {e}")
            return QualityMetric(
                name='completeness',
                value=0.0,
                threshold=self.quality_thresholds['completeness'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_news_data_completeness(self) -> QualityMetric:
        """Check news data completeness."""
        try:
            from ..config.models import NewsArticle
            
            with self.db_manager.session_scope() as session:
                # Check for recent news articles (last hour)
                cutoff_time = datetime.utcnow() - timedelta(hours=1)
                
                recent_articles = session.query(NewsArticle).filter(
                    NewsArticle.created_at >= cutoff_time
                ).count()
                
                # Expected minimum articles per hour
                expected_articles = 10
                completeness_score = min(1.0, recent_articles / expected_articles)
                status = self._determine_quality_level(completeness_score)
                
                return QualityMetric(
                    name='completeness',
                    value=completeness_score,
                    threshold=self.quality_thresholds['completeness'],
                    status=status,
                    description=f"{recent_articles} articles in last hour (expected: {expected_articles})",
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            self.logger.error(f"Error checking news data completeness: {e}")
            return QualityMetric(
                name='completeness',
                value=0.0,
                threshold=self.quality_thresholds['completeness'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_social_data_completeness(self) -> QualityMetric:
        """Check social media data completeness."""
        try:
            from ..config.models import SocialMediaPost
            
            with self.db_manager.session_scope() as session:
                # Check for recent social media posts (last hour)
                cutoff_time = datetime.utcnow() - timedelta(hours=1)
                
                recent_posts = session.query(SocialMediaPost).filter(
                    SocialMediaPost.created_at >= cutoff_time
                ).count()
                
                # Expected minimum posts per hour
                expected_posts = 20
                completeness_score = min(1.0, recent_posts / expected_posts)
                status = self._determine_quality_level(completeness_score)
                
                return QualityMetric(
                    name='completeness',
                    value=completeness_score,
                    threshold=self.quality_thresholds['completeness'],
                    status=status,
                    description=f"{recent_posts} posts in last hour (expected: {expected_posts})",
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            self.logger.error(f"Error checking social data completeness: {e}")
            return QualityMetric(
                name='completeness',
                value=0.0,
                threshold=self.quality_thresholds['completeness'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_timeliness(self, source_name: str) -> QualityMetric:
        """Check data timeliness."""
        try:
            # Simple implementation - check if data is recent
            if source_name == 'market_data':
                max_age_minutes = 2  # Market data should be very recent
            elif source_name == 'news_data':
                max_age_minutes = 60  # News can be up to 1 hour old
            elif source_name == 'social_data':
                max_age_minutes = 30  # Social media should be fairly recent
            else:
                max_age_minutes = 60
            
            # This is a simplified check - in practice, you'd check actual data timestamps
            timeliness_score = 0.95  # Assume good timeliness for now
            status = self._determine_quality_level(timeliness_score)
            
            return QualityMetric(
                name='timeliness',
                value=timeliness_score,
                threshold=self.quality_thresholds['timeliness'],
                status=status,
                description=f"Data is within {max_age_minutes} minute threshold",
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error checking timeliness for {source_name}: {e}")
            return QualityMetric(
                name='timeliness',
                value=0.0,
                threshold=self.quality_thresholds['timeliness'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_validity(self, source_name: str) -> QualityMetric:
        """Check data validity."""
        try:
            # Simplified validity check - assume good validity for now
            validity_score = 0.98
            status = self._determine_quality_level(validity_score)
            
            return QualityMetric(
                name='validity',
                value=validity_score,
                threshold=self.quality_thresholds['validity'],
                status=status,
                description="Data format validation passed",
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error checking validity for {source_name}: {e}")
            return QualityMetric(
                name='validity',
                value=0.0,
                threshold=self.quality_thresholds['validity'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_consistency(self, source_name: str) -> QualityMetric:
        """Check data consistency."""
        try:
            # Simplified consistency check - assume good consistency for now
            consistency_score = 0.96
            status = self._determine_quality_level(consistency_score)
            
            return QualityMetric(
                name='consistency',
                value=consistency_score,
                threshold=self.quality_thresholds['consistency'],
                status=status,
                description="Data consistency checks passed",
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error checking consistency for {source_name}: {e}")
            return QualityMetric(
                name='consistency',
                value=0.0,
                threshold=self.quality_thresholds['consistency'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_accuracy(self, source_name: str) -> QualityMetric:
        """Check data accuracy."""
        try:
            # Simplified accuracy check - assume good accuracy for now
            accuracy_score = 0.97
            status = self._determine_quality_level(accuracy_score)
            
            return QualityMetric(
                name='accuracy',
                value=accuracy_score,
                threshold=self.quality_thresholds['accuracy'],
                status=status,
                description="Data accuracy validation passed",
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error checking accuracy for {source_name}: {e}")
            return QualityMetric(
                name='accuracy',
                value=0.0,
                threshold=self.quality_thresholds['accuracy'],
                status=DataQualityLevel.CRITICAL,
                description=f"Error: {e}",
                timestamp=datetime.utcnow()
            )
    
    def _determine_quality_level(self, score: float) -> DataQualityLevel:
        """Determine quality level based on score."""
        if score >= 0.95:
            return DataQualityLevel.EXCELLENT
        elif score >= 0.85:
            return DataQualityLevel.GOOD
        elif score >= 0.70:
            return DataQualityLevel.FAIR
        elif score >= 0.50:
            return DataQualityLevel.POOR
        else:
            return DataQualityLevel.CRITICAL
    
    def _generate_recommendations(self, metrics: List[QualityMetric]) -> List[str]:
        """Generate recommendations based on quality metrics."""
        recommendations = []
        
        for metric in metrics:
            if metric.status in [DataQualityLevel.POOR, DataQualityLevel.CRITICAL]:
                if metric.name == 'completeness':
                    recommendations.append(f"Improve data collection for {metric.name}")
                elif metric.name == 'timeliness':
                    recommendations.append(f"Reduce data collection latency for {metric.name}")
                elif metric.name == 'validity':
                    recommendations.append(f"Implement stricter validation for {metric.name}")
                elif metric.name == 'consistency':
                    recommendations.append(f"Review data normalization for {metric.name}")
                elif metric.name == 'accuracy':
                    recommendations.append(f"Verify data sources for {metric.name}")
        
        if not recommendations:
            recommendations.append("Data quality is within acceptable thresholds")
        
        return recommendations
    
    def _store_quality_report(self, source_name: str, report: QualityReport):
        """Store quality report in history."""
        try:
            if source_name not in self.quality_history:
                self.quality_history[source_name] = []
            
            self.quality_history[source_name].append(report)
            
            # Limit history length
            if len(self.quality_history[source_name]) > self.max_history_length:
                self.quality_history[source_name] = self.quality_history[source_name][-self.max_history_length:]
                
        except Exception as e:
            self.logger.error(f"Error storing quality report: {e}")
    
    async def _check_quality_alerts(self, source_name: str, report: QualityReport):
        """Check for quality issues and send alerts."""
        try:
            config = self.data_sources[source_name]
            
            # Check overall quality
            if report.overall_status == DataQualityLevel.CRITICAL:
                await self.alert_manager.send_alert(
                    alert_type=AlertType.DATA_QUALITY,
                    level=AlertLevel.CRITICAL,
                    message=f"Critical data quality issue in {source_name}",
                    details={
                        'source': source_name,
                        'score': report.overall_score,
                        'status': report.overall_status.value,
                        'recommendations': report.recommendations
                    }
                )
            elif report.overall_status == DataQualityLevel.POOR and config.get('critical', False):
                await self.alert_manager.send_alert(
                    alert_type=AlertType.DATA_QUALITY,
                    level=AlertLevel.WARNING,
                    message=f"Poor data quality in critical source {source_name}",
                    details={
                        'source': source_name,
                        'score': report.overall_score,
                        'status': report.overall_status.value
                    }
                )
                
        except Exception as e:
            self.logger.error(f"Error checking quality alerts: {e}")
    
    def _record_quality_metrics(self, source_name: str, report: QualityReport):
        """Record quality metrics."""
        try:
            # Record overall score
            self.metrics.record_custom_metric(
                f"data_quality_{source_name}_overall", report.overall_score * 100
            )
            
            # Record individual metrics
            for metric in report.metrics:
                self.metrics.record_custom_metric(
                    f"data_quality_{source_name}_{metric.name}", metric.value * 100
                )
                
        except Exception as e:
            self.logger.error(f"Error recording quality metrics: {e}")
    
    async def _quality_reporting_loop(self):
        """Generate periodic quality reports."""
        while self.is_running:
            try:
                # Generate summary report
                summary = self.get_quality_summary()
                
                self.logger.info(
                    f"📊 Data Quality Summary: "
                    f"Market: {summary.get('market_data', {}).get('score', 0):.1%}, "
                    f"News: {summary.get('news_data', {}).get('score', 0):.1%}, "
                    f"Social: {summary.get('social_data', {}).get('score', 0):.1%}"
                )
                
                await asyncio.sleep(1800)  # Report every 30 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in quality reporting: {e}")
                await asyncio.sleep(300)
    
    def get_quality_summary(self) -> Dict[str, Any]:
        """Get quality summary for all sources."""
        summary = {}
        
        for source_name, reports in self.quality_history.items():
            if reports:
                latest_report = reports[-1]
                summary[source_name] = {
                    'score': latest_report.overall_score,
                    'status': latest_report.overall_status.value,
                    'last_check': latest_report.timestamp,
                    'metrics': {m.name: m.value for m in latest_report.metrics}
                }
            else:
                summary[source_name] = {
                    'score': 0.0,
                    'status': 'unknown',
                    'last_check': None,
                    'metrics': {}
                }
        
        return summary
    
    def get_quality_history(self, source_name: str, hours: int = 24) -> List[QualityReport]:
        """Get quality history for a source."""
        if source_name not in self.quality_history:
            return []
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [
            report for report in self.quality_history[source_name]
            if report.timestamp >= cutoff_time
        ]
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        return {
            'is_running': self.is_running,
            'monitored_sources': list(self.data_sources.keys()),
            'monitoring_interval': self.monitoring_interval,
            'quality_thresholds': self.quality_thresholds,
            'summary': self.get_quality_summary()
        }


# Global data quality manager instance
data_quality_manager: Optional[DataQualityManager] = None


def get_data_quality_manager() -> DataQualityManager:
    """Get the global data quality manager instance."""
    global data_quality_manager
    if data_quality_manager is None:
        from ..config.settings import Settings
        settings = Settings()
        data_quality_manager = DataQualityManager(settings)
    return data_quality_manager
