#!/usr/bin/env python3
"""
Hızlı Binance API Test
API anahtarı düzeltmelerinden sonra hızlı test için.
"""

import ccxt.async_support as ccxt  # type: ignore
import asyncio
from dotenv import load_dotenv
import os

load_dotenv()

async def quick_test():
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    print(f"[KEY] API Key: {api_key[:10]}...{api_key[-10:]}")
    print("[TEST] Hızlı test başlatılıyor...")
    
    try:
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': False,
            'enableRateLimit': True,
            'options': {
                'adjustForTimeDifference': True,  # Zaman farkını otomatik ayarla
                'recvWindow': 60000,  # <PERSON>aman toleransını çok artır (60 saniye)
                'timeDifference': 6000,  # <PERSON> zaman farkı ayarı (6 saniye ileri)
                'synchronizeTime': True,  # <PERSON>aman senkronizasyonu
            }
        })

        # Zaman senkronizasyonu için server time'ı al
        try:
            # Önce markets'ı yükle
            await exchange.load_markets()

            # Server time'ı al
            server_time = await exchange.fetch_time()
            local_time = exchange.milliseconds()
            time_diff = server_time - local_time

            # Zaman farkını ayarla (biraz daha toleranslı)
            exchange.options['timeDifference'] = time_diff - 1000  # 1 saniye daha erken
            print(f"[SYNC] Binance time difference set to: {time_diff}ms (adjusted: {time_diff - 1000}ms)")

        except Exception as time_error:
            print(f"[WARN] Could not sync time with Binance: {time_error}")
            # Fallback: manuel zaman farkı
            exchange.options['timeDifference'] = -2000  # 2 saniye erken
        
        # Sadece hesap bilgilerini al
        balance = await exchange.fetch_balance()
        print("[OK] API anahtarı çalışıyor!")
        print(f"[INFO] Hesap bilgileri alındı: {len(balance)} varlık")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"[ERROR] Hata: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    if success:
        print("\n[SUCCESS] API anahtarı hazır! Trading bot'u çalıştırabilirsiniz.")
    else:
        print("\n[FAIL] API anahtarı hala çalışmıyor. Binance ayarlarını kontrol edin.")
