#!/usr/bin/env python3
"""
Database migration script for AI Trading Bot System.

This script handles database initialization, migrations, and schema updates.

Author: inkbytefo
"""

import os
import sys
import subprocess
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.settings import Settings
from src.config.database import DatabaseManager
from src.config.models import *  # Import all models


def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"📋 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def check_database_connection():
    """Check if database connection is working."""
    try:
        settings = Settings()
        db_manager = DatabaseManager(settings)
        db_manager.initialize()
        
        if db_manager.health_check():
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


def create_initial_migration():
    """Create initial migration if none exists."""
    versions_dir = Path("alembic/versions")

    # Check if any migration files exist
    if not any(versions_dir.glob("*.py")):
        print("📋 Creating initial migration...")
        return run_command(
            'alembic revision --autogenerate -m "Initial migration"',
            "Creating initial migration"
        )
    else:
        print("✅ Migration files already exist")
        return True


def run_migrations():
    """Run database migrations."""
    return run_command(
        "alembic upgrade head",
        "Running database migrations"
    )


def create_migration(message):
    """Create a new migration."""
    return run_command(
        f'alembic revision --autogenerate -m "{message}"',
        f"Creating migration: {message}"
    )


def downgrade_migration(revision="base"):
    """Downgrade to a specific revision."""
    return run_command(
        f"alembic downgrade {revision}",
        f"Downgrading to revision: {revision}"
    )


def show_migration_history():
    """Show migration history."""
    return run_command(
        "alembic history --verbose",
        "Showing migration history"
    )


def show_current_revision():
    """Show current database revision."""
    return run_command(
        "alembic current",
        "Showing current revision"
    )


def initialize_database():
    """Initialize database with tables and initial data."""
    try:
        print("🚀 Initializing AI Trading Bot Database")
        print("=" * 50)
        
        # Check database connection
        if not check_database_connection():
            print("❌ Cannot proceed without database connection")
            return False
        
        # Create initial migration if needed
        if not create_initial_migration():
            print("❌ Failed to create initial migration")
            return False
        
        # Run migrations
        if not run_migrations():
            print("❌ Failed to run migrations")
            return False
        
        # Insert initial data
        if not insert_initial_data():
            print("⚠️  Failed to insert initial data, but database is ready")
        
        print("\n" + "=" * 50)
        print("✅ Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Configure your exchange API keys in .env")
        print("2. Start the trading bot: python main.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False


def insert_initial_data():
    """Insert initial data into the database."""
    try:
        from src.config.database import get_db_manager
        
        print("📋 Inserting initial data...")
        
        db_manager = get_db_manager()
        
        with db_manager.session_scope() as session:
            # Insert default trading pairs
            default_pairs = [
                {"symbol": "BTC/USDT", "base_asset": "BTC", "quote_asset": "USDT", "exchange": "binance"},
                {"symbol": "ETH/USDT", "base_asset": "ETH", "quote_asset": "USDT", "exchange": "binance"},
                {"symbol": "ADA/USDT", "base_asset": "ADA", "quote_asset": "USDT", "exchange": "binance"},
                {"symbol": "DOT/USDT", "base_asset": "DOT", "quote_asset": "USDT", "exchange": "binance"},
                {"symbol": "LINK/USDT", "base_asset": "LINK", "quote_asset": "USDT", "exchange": "binance"},
            ]
            
            for pair_data in default_pairs:
                # Check if pair already exists
                existing_pair = session.query(TradingPair).filter_by(
                    symbol=pair_data["symbol"],
                    exchange=pair_data["exchange"]
                ).first()
                
                if not existing_pair:
                    trading_pair = TradingPair(**pair_data)
                    session.add(trading_pair)
            
            session.commit()
            print("✅ Initial data inserted successfully")
            return True
            
    except Exception as e:
        print(f"❌ Failed to insert initial data: {e}")
        return False


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Trading Bot Database Migration Tool")
    parser.add_argument("command", choices=[
        "init", "migrate", "create", "downgrade", "history", "current", "check"
    ], help="Migration command to run")
    parser.add_argument("-m", "--message", help="Migration message (for create command)")
    parser.add_argument("-r", "--revision", default="base", help="Revision for downgrade command")
    
    args = parser.parse_args()
    
    # Change to project root directory
    os.chdir(Path(__file__).parent.parent)
    
    if args.command == "init":
        success = initialize_database()
        sys.exit(0 if success else 1)
    
    elif args.command == "migrate":
        success = run_migrations()
        sys.exit(0 if success else 1)
    
    elif args.command == "create":
        if not args.message:
            print("❌ Migration message is required for create command")
            sys.exit(1)
        success = create_migration(args.message)
        sys.exit(0 if success else 1)
    
    elif args.command == "downgrade":
        success = downgrade_migration(args.revision)
        sys.exit(0 if success else 1)
    
    elif args.command == "history":
        success = show_migration_history()
        sys.exit(0 if success else 1)
    
    elif args.command == "current":
        success = show_current_revision()
        sys.exit(0 if success else 1)
    
    elif args.command == "check":
        success = check_database_connection()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
