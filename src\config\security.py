"""
Security utilities and encryption for AI Trading Bot System.

This module provides encryption, decryption, and secure storage utilities
for sensitive data like API keys and trading credentials.

Author: inkbytefo
"""

import os
import base64
import hashlib
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

from .settings import Settings


class SecurityManager:
    """
    Security manager for handling encryption, decryption, and secure storage.
    
    Provides methods for encrypting sensitive data like API keys and
    storing them securely.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self._cipher_suite = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption cipher suite."""
        try:
            # Use the encryption key from settings
            key = self.settings.ENCRYPTION_KEY.encode()
            
            # Derive a proper encryption key using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'trading_bot_salt',  # In production, use a random salt
                iterations=100000,
            )
            derived_key = base64.urlsafe_b64encode(kdf.derive(key))
            
            self._cipher_suite = Fernet(derived_key)
            self.logger.info("Encryption system initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption: {e}")
            raise
    
    def encrypt_data(self, data: str) -> str:
        """
        Encrypt sensitive data.
        
        Args:
            data: Plain text data to encrypt
            
        Returns:
            Base64 encoded encrypted data
        """
        try:
            if not self._cipher_suite:
                raise RuntimeError("Encryption not initialized")
            
            encrypted_data = self._cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """
        Decrypt sensitive data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Decrypted plain text data
        """
        try:
            if not self._cipher_suite:
                raise RuntimeError("Encryption not initialized")
            
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
            
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """
        Hash a password with salt.
        
        Args:
            password: Plain text password
            salt: Optional salt (generated if not provided)
            
        Returns:
            Tuple of (hashed_password, salt)
        """
        if salt is None:
            salt = base64.urlsafe_b64encode(os.urandom(32)).decode()
        
        # Create hash
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode(),
            salt.encode(),
            100000
        )
        
        hashed_password = base64.urlsafe_b64encode(password_hash).decode()
        return hashed_password, salt
    
    def verify_password(self, password: str, hashed_password: str, salt: str) -> bool:
        """
        Verify a password against its hash.
        
        Args:
            password: Plain text password to verify
            hashed_password: Stored hashed password
            salt: Salt used for hashing
            
        Returns:
            True if password matches, False otherwise
        """
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return computed_hash == hashed_password
        except Exception as e:
            self.logger.error(f"Password verification failed: {e}")
            return False
    
    def generate_api_key(self, length: int = 32) -> str:
        """
        Generate a secure API key.
        
        Args:
            length: Length of the API key
            
        Returns:
            Generated API key
        """
        return base64.urlsafe_b64encode(os.urandom(length)).decode()
    
    def validate_api_credentials(self, credentials: Dict[str, Any]) -> bool:
        """
        Validate API credentials format and requirements.
        
        Args:
            credentials: Dictionary containing API credentials
            
        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            required_fields = {
                'binance': ['api_key', 'secret_key'],
                'coinbase': ['api_key', 'secret_key', 'passphrase'],
                'kraken': ['api_key', 'secret_key']
            }
            
            for exchange, fields in required_fields.items():
                if exchange in credentials:
                    for field in fields:
                        if field not in credentials[exchange] or not credentials[exchange][field]:
                            self.logger.warning(f"Missing {field} for {exchange}")
                            return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Credential validation failed: {e}")
            return False
    
    def secure_store_credentials(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Securely store API credentials by encrypting sensitive fields.
        
        Args:
            credentials: Dictionary containing API credentials
            
        Returns:
            Dictionary with encrypted credentials
        """
        try:
            encrypted_credentials = {}
            
            for exchange, creds in credentials.items():
                encrypted_credentials[exchange] = {}
                
                for key, value in creds.items():
                    if key in ['api_key', 'secret_key', 'passphrase']:
                        # Encrypt sensitive fields
                        encrypted_credentials[exchange][key] = self.encrypt_data(str(value))
                    else:
                        # Keep non-sensitive fields as-is
                        encrypted_credentials[exchange][key] = value
            
            return encrypted_credentials
            
        except Exception as e:
            self.logger.error(f"Failed to store credentials securely: {e}")
            raise
    
    def retrieve_credentials(self, encrypted_credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve and decrypt API credentials.
        
        Args:
            encrypted_credentials: Dictionary containing encrypted credentials
            
        Returns:
            Dictionary with decrypted credentials
        """
        try:
            decrypted_credentials = {}
            
            for exchange, creds in encrypted_credentials.items():
                decrypted_credentials[exchange] = {}
                
                for key, value in creds.items():
                    if key in ['api_key', 'secret_key', 'passphrase']:
                        # Decrypt sensitive fields
                        decrypted_credentials[exchange][key] = self.decrypt_data(str(value))
                    else:
                        # Keep non-sensitive fields as-is
                        decrypted_credentials[exchange][key] = value
            
            return decrypted_credentials
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve credentials: {e}")
            raise
    
    def sanitize_logs(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize log data by removing or masking sensitive information.
        
        Args:
            log_data: Dictionary containing log data
            
        Returns:
            Sanitized log data
        """
        sensitive_keys = [
            'api_key', 'secret_key', 'passphrase', 'password',
            'token', 'authorization', 'secret', 'private_key'
        ]
        
        sanitized_data = {}
        
        for key, value in log_data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                # Mask sensitive data
                if isinstance(value, str) and len(value) > 8:
                    sanitized_data[key] = value[:4] + '*' * (len(value) - 8) + value[-4:]
                else:
                    sanitized_data[key] = '***'
            else:
                sanitized_data[key] = value
        
        return sanitized_data
    
    def check_security_requirements(self) -> Dict[str, bool]:
        """
        Check if security requirements are met.
        
        Returns:
            Dictionary with security check results
        """
        checks = {
            'encryption_key_set': bool(self.settings.ENCRYPTION_KEY),
            'secret_key_set': bool(self.settings.SECRET_KEY),
            'encryption_initialized': self._cipher_suite is not None,
            'secure_environment': not self.settings.DEBUG,
            'https_enabled': False,  # Would check actual HTTPS configuration
        }
        
        # Log security status
        failed_checks = [check for check, passed in checks.items() if not passed]
        if failed_checks:
            self.logger.warning(f"Security checks failed: {failed_checks}")
        else:
            self.logger.info("All security checks passed")
        
        return checks
