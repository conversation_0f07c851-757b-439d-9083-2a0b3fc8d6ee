#!/usr/bin/env python3
"""
Binance API Test Script
Bu script Binance API anahtarınızın çalışıp çalışmadığını test eder.
"""

import ccxt
import asyncio
from dotenv import load_dotenv
import os

# .env dosyasını yükle
load_dotenv()

async def test_binance_api():
    """Binance API anahtarını test et."""
    
    # API anahtarlarını al
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    testnet = os.getenv('BINANCE_TESTNET', 'false').lower() == 'true'
    
    print("🔍 Binance API Test Başlatılıyor...")
    print(f"API Key: {api_key[:10]}...{api_key[-10:] if api_key else 'None'}")
    print(f"Secret Key: {'✓ Mevcut' if secret_key else '✗ Eksik'}")
    print(f"Testnet: {testnet}")
    print("-" * 50)
    
    if not api_key or not secret_key:
        print("❌ API anahtarları eksik!")
        return False
    
    try:
        # Binance exchange instance oluştur
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': testnet,
            'enableRateLimit': True,
            'timeout': 30000,
        })
        
        print("1️⃣ Exchange instance oluşturuldu")
        
        # API bağlantısını test et
        print("2️⃣ API bağlantısı test ediliyor...")
        
        # Hesap bilgilerini al (bu API anahtarı gerektirir)
        account_info = await exchange.fetch_balance()
        print("✅ API anahtarı geçerli!")
        print(f"Hesap durumu: {len(account_info)} varlık bilgisi alındı")
        
        # Bazı temel bilgileri göster
        if 'USDT' in account_info:
            usdt_balance = account_info['USDT']
            print(f"USDT Bakiye: {usdt_balance['total']} (Serbest: {usdt_balance['free']}, Kilitli: {usdt_balance['used']})")
        
        # Market verilerini test et (API anahtarı gerektirmez ama bağlantıyı test eder)
        print("3️⃣ Market verileri test ediliyor...")
        ticker = await exchange.fetch_ticker('BTC/USDT')
        print(f"✅ BTC/USDT fiyatı: ${ticker['last']}")
        
        await exchange.close()
        return True
        
    except ccxt.AuthenticationError as e:
        print(f"❌ Kimlik doğrulama hatası: {e}")
        print("🔧 Çözüm önerileri:")
        print("   - API anahtarının doğru kopyalandığından emin olun")
        print("   - API anahtarının aktif olduğunu kontrol edin")
        print("   - Spot trading izninin verildiğini kontrol edin")
        return False
        
    except ccxt.PermissionDenied as e:
        print(f"❌ İzin hatası: {e}")
        print("🔧 Çözüm önerileri:")
        print("   - API anahtarına spot trading izni verin")
        print("   - IP kısıtlaması varsa mevcut IP'nizi ekleyin")
        return False
        
    except ccxt.NetworkError as e:
        print(f"❌ Ağ hatası: {e}")
        print("🔧 Çözüm önerileri:")
        print("   - İnternet bağlantınızı kontrol edin")
        print("   - Firewall ayarlarını kontrol edin")
        return False
        
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_binance_api())
    
    if success:
        print("\n🎉 Binance API testi başarılı!")
        print("✅ API anahtarınız çalışıyor ve trading bot'ta kullanılabilir.")
    else:
        print("\n💥 Binance API testi başarısız!")
        print("❌ API anahtarınızı kontrol edin ve tekrar deneyin.")
