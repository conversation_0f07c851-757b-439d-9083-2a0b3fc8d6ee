"""
Configuration manager for AI Trading Bot System.

This module provides dynamic configuration management, validation,
and hot-reload capabilities for the trading bot.

Author: inkbytefo
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging
from datetime import datetime

from .settings import Settings
from .security import SecurityManager


class ConfigManager:
    """
    Configuration manager for dynamic configuration handling.
    
    Provides methods for loading, validating, and updating configuration
    settings at runtime.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.security_manager = SecurityManager(settings)
        self.logger = logging.getLogger(__name__)
        
        # Configuration cache
        self._config_cache: Dict[str, Any] = {}
        self._last_modified: Dict[str, datetime] = {}
        
        # Configuration file paths
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_files = {
            "trading": self.config_dir / "trading.yml",
            "exchanges": self.config_dir / "exchanges.yml",
            "strategies": self.config_dir / "strategies.yml",
            "risk": self.config_dir / "risk.yml",
            "alerts": self.config_dir / "alerts.yml"
        }
    
    def initialize(self):
        """Initialize configuration manager and create default configs."""
        try:
            self.logger.info("Initializing Configuration Manager...")
            
            # Create default configuration files if they don't exist
            self._create_default_configs()
            
            # Load all configurations
            self._load_all_configs()
            
            # Validate configurations
            self._validate_all_configs()
            
            self.logger.info("✅ Configuration Manager initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Configuration Manager: {e}")
            raise
    
    def _create_default_configs(self):
        """Create default configuration files."""
        default_configs = {
            "trading": {
                "enabled": True,
                "paper_trading": self.settings.PAPER_TRADING,
                "default_pairs": self.settings.trading.default_trading_pairs,
                "base_currency": self.settings.trading.default_base_currency,
                "max_positions": 10,  # Increased for more coins
                "position_size": {
                    "default": self.settings.trading.max_position_size,
                    "max": 0.2,
                    "min": 0.01
                },
                "timeframes": self.settings.market_data.timeframes,
                "update_interval": self.settings.market_data.collection_interval
            },
            
            "exchanges": {
                "binance_spot": {
                    "enabled": bool(self.settings.exchanges.binance_api_key),
                    "testnet": self.settings.exchanges.binance_testnet,
                    "rate_limit": 1200,  # 2025 updated limit
                    "timeout": self.settings.REQUEST_TIMEOUT,
                    "retry_attempts": self.settings.MAX_RETRIES
                },
                "binance_futures": {
                    "enabled": bool(self.settings.exchanges.binance_api_key),
                    "testnet": self.settings.exchanges.binance_testnet,
                    "rate_limit": 2400,  # 2025 updated limit for futures
                    "timeout": self.settings.REQUEST_TIMEOUT,
                    "retry_attempts": self.settings.MAX_RETRIES
                },
                "mexc": {
                    "enabled": bool(self.settings.exchanges.mexc_api_key),
                    "testnet": getattr(self.settings.exchanges, 'mexc_testnet', False),
                    "rate_limit": 1000,  # 2025 updated limit
                    "timeout": self.settings.REQUEST_TIMEOUT,
                    "retry_attempts": self.settings.MAX_RETRIES
                }
            },
            
            "strategies": {
                "enabled_strategies": ["momentum", "mean_reversion"],
                "strategy_weights": {
                    "momentum": 0.6,
                    "mean_reversion": 0.4
                },
                "rebalance_interval": 3600,
                "min_confidence": 0.7
            },
            
            "risk": {
                "max_daily_loss": 0.02,
                "max_drawdown": 0.10,
                "stop_loss": 0.05,
                "take_profit": 0.15,
                "position_sizing": "kelly",
                "risk_per_trade": 0.01,
                "correlation_limit": 0.7,
                "volatility_adjustment": True
            },
            
            "alerts": {
                "email": {
                    "enabled": False,
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "recipients": []
                },
                "slack": {
                    "enabled": False,
                    "channels": ["#trading-alerts"]
                },
                "discord": {
                    "enabled": False,
                    "channels": []
                },
                "alert_levels": ["critical", "warning", "info"],
                "rate_limit": 300
            }
        }
        
        for config_name, config_data in default_configs.items():
            config_file = self.config_files[config_name]
            
            if not config_file.exists():
                with open(config_file, 'w') as f:
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
                self.logger.info(f"Created default config: {config_file}")
    
    def _load_all_configs(self):
        """Load all configuration files."""
        for config_name, config_file in self.config_files.items():
            try:
                self._load_config(config_name, config_file)
            except Exception as e:
                self.logger.error(f"Failed to load config {config_name}: {e}")
    
    def _load_config(self, config_name: str, config_file: Path):
        """Load a specific configuration file."""
        try:
            if config_file.exists():
                with open(config_file, 'r') as f:
                    if config_file.suffix.lower() in ['.yml', '.yaml']:
                        config_data = yaml.safe_load(f)
                    else:
                        config_data = json.load(f)
                
                self._config_cache[config_name] = config_data
                self._last_modified[config_name] = datetime.fromtimestamp(
                    config_file.stat().st_mtime
                )
                
                self.logger.debug(f"Loaded config: {config_name}")
            else:
                self.logger.warning(f"Config file not found: {config_file}")
                
        except Exception as e:
            self.logger.error(f"Error loading config {config_name}: {e}")
            raise
    
    def _validate_all_configs(self):
        """Validate all loaded configurations."""
        validation_rules = {
            "trading": self._validate_trading_config,
            "exchanges": self._validate_exchange_config,
            "strategies": self._validate_strategy_config,
            "risk": self._validate_risk_config,
            "alerts": self._validate_alert_config
        }
        
        for config_name, validator in validation_rules.items():
            if config_name in self._config_cache:
                try:
                    validator(self._config_cache[config_name])
                    self.logger.debug(f"Validated config: {config_name}")
                except Exception as e:
                    self.logger.error(f"Validation failed for {config_name}: {e}")
                    raise
    
    def _validate_trading_config(self, config: Dict[str, Any]):
        """Validate trading configuration."""
        required_fields = ["enabled", "default_pairs", "base_currency"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate position size
        if "position_size" in config:
            pos_size = config["position_size"]
            if pos_size.get("max", 0) > 1.0:
                raise ValueError("Maximum position size cannot exceed 100%")
            if pos_size.get("min", 0) <= 0:
                raise ValueError("Minimum position size must be positive")
    
    def _validate_exchange_config(self, config: Dict[str, Any]):
        """Validate exchange configuration."""
        supported_exchanges = ["binance", "coinbase", "kraken"]
        
        for exchange in config:
            if exchange not in supported_exchanges:
                self.logger.warning(f"Unsupported exchange: {exchange}")
            
            exchange_config = config[exchange]
            if "rate_limit" in exchange_config:
                if exchange_config["rate_limit"] <= 0:
                    raise ValueError(f"Invalid rate limit for {exchange}")
    
    def _validate_strategy_config(self, config: Dict[str, Any]):
        """Validate strategy configuration."""
        if "strategy_weights" in config:
            weights = config["strategy_weights"]
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.01:
                raise ValueError(f"Strategy weights must sum to 1.0, got {total_weight}")
    
    def _validate_risk_config(self, config: Dict[str, Any]):
        """Validate risk management configuration."""
        risk_fields = ["max_daily_loss", "max_drawdown", "stop_loss"]
        
        for field in risk_fields:
            if field in config:
                value = config[field]
                if not (0 < value < 1):
                    raise ValueError(f"{field} must be between 0 and 1")
    
    def _validate_alert_config(self, config: Dict[str, Any]):
        """Validate alert configuration."""
        if "alert_levels" in config:
            valid_levels = ["critical", "warning", "info", "debug"]
            for level in config["alert_levels"]:
                if level not in valid_levels:
                    raise ValueError(f"Invalid alert level: {level}")
    
    def get_config(self, config_name: str, reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get configuration by name.
        
        Args:
            config_name: Name of the configuration
            reload: Whether to reload from file
            
        Returns:
            Configuration dictionary or None if not found
        """
        if reload or config_name not in self._config_cache:
            if config_name in self.config_files:
                self._load_config(config_name, self.config_files[config_name])
        
        return self._config_cache.get(config_name)
    
    def update_config(self, config_name: str, updates: Dict[str, Any], 
                     persist: bool = True) -> bool:
        """
        Update configuration with new values.
        
        Args:
            config_name: Name of the configuration to update
            updates: Dictionary of updates to apply
            persist: Whether to save changes to file
            
        Returns:
            True if update was successful
        """
        try:
            if config_name not in self._config_cache:
                self.logger.error(f"Config not found: {config_name}")
                return False
            
            # Apply updates
            config = self._config_cache[config_name].copy()
            self._deep_update(config, updates)
            
            # Validate updated config
            validation_methods = {
                "trading": self._validate_trading_config,
                "exchanges": self._validate_exchange_config,
                "strategies": self._validate_strategy_config,
                "risk": self._validate_risk_config,
                "alerts": self._validate_alert_config
            }
            
            if config_name in validation_methods:
                validation_methods[config_name](config)
            
            # Update cache
            self._config_cache[config_name] = config
            
            # Persist to file if requested
            if persist:
                self._save_config(config_name, config)
            
            self.logger.info(f"Updated config: {config_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update config {config_name}: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """Recursively update nested dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _save_config(self, config_name: str, config_data: Dict[str, Any]):
        """Save configuration to file."""
        try:
            config_file = self.config_files[config_name]
            
            with open(config_file, 'w') as f:
                if config_file.suffix.lower() in ['.yml', '.yaml']:
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
                else:
                    json.dump(config_data, f, indent=2)
            
            self._last_modified[config_name] = datetime.now()
            self.logger.debug(f"Saved config: {config_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to save config {config_name}: {e}")
            raise
    
    def reload_all_configs(self):
        """Reload all configuration files."""
        self.logger.info("Reloading all configurations...")
        self._load_all_configs()
        self._validate_all_configs()
        self.logger.info("✅ All configurations reloaded")
    
    def get_all_configs(self) -> Dict[str, Any]:
        """Get all loaded configurations."""
        return self._config_cache.copy()
    
    def export_config(self, config_name: str, file_path: str):
        """Export configuration to a file."""
        try:
            config = self.get_config(config_name)
            if config is None:
                raise ValueError(f"Config not found: {config_name}")
            
            with open(file_path, 'w') as f:
                if file_path.endswith(('.yml', '.yaml')):
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                else:
                    json.dump(config, f, indent=2)
            
            self.logger.info(f"Exported config {config_name} to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export config: {e}")
            raise
