"""
WebSocket Data Collector for AI Trading Bot System.

This module handles real-time data collection via WebSocket connections
for ultra-low latency market data feeds.

Author: inkbytefo
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from decimal import Decimal

from ..config.settings import Settings
from ..monitoring.metrics import get_metrics_collector


class WebSocketCollector:
    """
    Real-time WebSocket data collector for multiple exchanges.
    
    Provides ultra-low latency market data feeds via WebSocket
    connections to exchange APIs.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # WebSocket connections
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.connection_tasks: Dict[str, asyncio.Task] = {}
        
        # Data handlers
        self.data_handlers: Dict[str, List[Callable]] = {
            'ticker': [],
            'orderbook': [],
            'trades': [],
            'kline': []
        }
        
        # Connection state
        self.is_running = False
        self.reconnect_attempts = {}
        self.max_reconnect_attempts = 5
        
        # Exchange WebSocket URLs
        self.websocket_urls = {
            'binance': {
                'base': 'wss://stream.binance.com:9443/ws/',
                'testnet': 'wss://testnet.binance.vision/ws/'
            },
            'coinbasepro': {
                'base': 'wss://ws-feed.pro.coinbase.com',
                'sandbox': 'wss://ws-feed-public.sandbox.pro.coinbase.com'
            },
            'kraken': {
                'base': 'wss://ws.kraken.com'
            }
        }
    
    async def start(self):
        """Start WebSocket data collection."""
        try:
            if self.is_running:
                self.logger.warning("WebSocket collector already running")
                return
            
            self.logger.info("🚀 Starting WebSocket data collection...")
            self.is_running = True
            
            # Start connections for configured exchanges
            if self.settings.exchanges.binance_api_key:
                await self._start_binance_connection()
            
            if self.settings.exchanges.coinbase_api_key:
                await self._start_coinbase_connection()
            
            if self.settings.exchanges.kraken_api_key:
                await self._start_kraken_connection()
            
            self.logger.info("✅ WebSocket data collection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start WebSocket collection: {e}")
            raise
    
    async def stop(self):
        """Stop WebSocket data collection."""
        try:
            self.logger.info("🛑 Stopping WebSocket data collection...")
            self.is_running = False
            
            # Close all connections
            for exchange_name, task in self.connection_tasks.items():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                self.logger.info(f"Stopped WebSocket for {exchange_name}")
            
            # Close WebSocket connections
            for exchange_name, ws in self.connections.items():
                if not ws.closed:
                    await ws.close()
            
            self.connections.clear()
            self.connection_tasks.clear()
            
            self.logger.info("✅ WebSocket data collection stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping WebSocket collection: {e}")
    
    async def _start_binance_connection(self):
        """Start Binance WebSocket connection."""
        try:
            # Determine URL based on testnet setting
            if self.settings.exchanges.binance_testnet:
                base_url = self.websocket_urls['binance']['testnet']
            else:
                base_url = self.websocket_urls['binance']['base']
            
            # Create stream names for trading pairs
            streams = []
            for pair in self.settings.trading.default_trading_pairs:
                symbol = pair.replace('/', '').lower()  # BTC/USDT -> btcusdt
                streams.extend([
                    f"{symbol}@ticker",
                    f"{symbol}@depth20@100ms",
                    f"{symbol}@kline_1m"
                ])
            
            stream_url = f"{base_url}{'/'.join(streams)}"
            
            # Start connection task
            task = asyncio.create_task(
                self._binance_connection_loop(stream_url)
            )
            self.connection_tasks['binance'] = task
            
            self.logger.info("✅ Binance WebSocket connection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Binance WebSocket: {e}")
    
    async def _binance_connection_loop(self, url: str):
        """Binance WebSocket connection loop with reconnection."""
        exchange_name = 'binance'
        reconnect_count = 0
        
        while self.is_running and reconnect_count < self.max_reconnect_attempts:
            try:
                self.logger.info(f"Connecting to Binance WebSocket: {url}")
                
                async with websockets.connect(url) as websocket:
                    self.connections[exchange_name] = websocket
                    reconnect_count = 0  # Reset on successful connection
                    
                    self.logger.info("✅ Binance WebSocket connected")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        try:
                            data = json.loads(message)
                            await self._handle_binance_message(data)
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse Binance message: {e}")
                        except Exception as e:
                            self.logger.error(f"Error handling Binance message: {e}")
            
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning(f"Binance WebSocket connection closed")
            except Exception as e:
                self.logger.error(f"Binance WebSocket error: {e}")
            
            if self.is_running:
                reconnect_count += 1
                wait_time = min(2 ** reconnect_count, 60)  # Exponential backoff
                self.logger.info(f"Reconnecting to Binance in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
        
        if reconnect_count >= self.max_reconnect_attempts:
            self.logger.error("Max reconnection attempts reached for Binance")
    
    async def _handle_binance_message(self, data: Dict[str, Any]):
        """Handle incoming Binance WebSocket message."""
        try:
            if 'stream' in data and 'data' in data:
                stream = data['stream']
                message_data = data['data']
                
                if '@ticker' in stream:
                    await self._handle_ticker_data('binance', message_data)
                elif '@depth' in stream:
                    await self._handle_orderbook_data('binance', message_data)
                elif '@kline' in stream:
                    await self._handle_kline_data('binance', message_data)
            
            # Record metrics
            self.metrics.record_market_data_update('binance', 'websocket', 'message', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling Binance message: {e}")
    
    async def _start_coinbase_connection(self):
        """Start Coinbase Pro WebSocket connection."""
        try:
            # Determine URL based on sandbox setting
            if self.settings.exchanges.coinbase_sandbox:
                url = self.websocket_urls['coinbasepro']['sandbox']
            else:
                url = self.websocket_urls['coinbasepro']['base']
            
            # Start connection task
            task = asyncio.create_task(
                self._coinbase_connection_loop(url)
            )
            self.connection_tasks['coinbasepro'] = task
            
            self.logger.info("✅ Coinbase Pro WebSocket connection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Coinbase Pro WebSocket: {e}")
    
    async def _coinbase_connection_loop(self, url: str):
        """Coinbase Pro WebSocket connection loop."""
        exchange_name = 'coinbasepro'
        reconnect_count = 0
        
        while self.is_running and reconnect_count < self.max_reconnect_attempts:
            try:
                self.logger.info(f"Connecting to Coinbase Pro WebSocket: {url}")
                
                async with websockets.connect(url) as websocket:
                    self.connections[exchange_name] = websocket
                    reconnect_count = 0
                    
                    # Subscribe to channels
                    subscribe_message = {
                        "type": "subscribe",
                        "product_ids": self.settings.trading.default_trading_pairs,
                        "channels": ["ticker", "level2", "matches"]
                    }
                    await websocket.send(json.dumps(subscribe_message))
                    
                    self.logger.info("✅ Coinbase Pro WebSocket connected and subscribed")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        try:
                            data = json.loads(message)
                            await self._handle_coinbase_message(data)
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse Coinbase message: {e}")
                        except Exception as e:
                            self.logger.error(f"Error handling Coinbase message: {e}")
            
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning(f"Coinbase Pro WebSocket connection closed")
            except Exception as e:
                self.logger.error(f"Coinbase Pro WebSocket error: {e}")
            
            if self.is_running:
                reconnect_count += 1
                wait_time = min(2 ** reconnect_count, 60)
                self.logger.info(f"Reconnecting to Coinbase Pro in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
    
    async def _handle_coinbase_message(self, data: Dict[str, Any]):
        """Handle incoming Coinbase Pro WebSocket message."""
        try:
            message_type = data.get('type')
            
            if message_type == 'ticker':
                await self._handle_ticker_data('coinbasepro', data)
            elif message_type == 'l2update':
                await self._handle_orderbook_data('coinbasepro', data)
            elif message_type == 'match':
                await self._handle_trade_data('coinbasepro', data)
            
            # Record metrics
            self.metrics.record_market_data_update('coinbasepro', 'websocket', 'message', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling Coinbase message: {e}")
    
    async def _start_kraken_connection(self):
        """Start Kraken WebSocket connection."""
        try:
            url = self.websocket_urls['kraken']['base']
            
            # Start connection task
            task = asyncio.create_task(
                self._kraken_connection_loop(url)
            )
            self.connection_tasks['kraken'] = task
            
            self.logger.info("✅ Kraken WebSocket connection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Kraken WebSocket: {e}")
    
    async def _kraken_connection_loop(self, url: str):
        """Kraken WebSocket connection loop."""
        exchange_name = 'kraken'
        reconnect_count = 0
        
        while self.is_running and reconnect_count < self.max_reconnect_attempts:
            try:
                self.logger.info(f"Connecting to Kraken WebSocket: {url}")
                
                async with websockets.connect(url) as websocket:
                    self.connections[exchange_name] = websocket
                    reconnect_count = 0
                    
                    # Subscribe to channels
                    subscribe_message = {
                        "event": "subscribe",
                        "pair": self.settings.trading.default_trading_pairs,
                        "subscription": {"name": "ticker"}
                    }
                    await websocket.send(json.dumps(subscribe_message))
                    
                    self.logger.info("✅ Kraken WebSocket connected and subscribed")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        try:
                            data = json.loads(message)
                            await self._handle_kraken_message(data)
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse Kraken message: {e}")
                        except Exception as e:
                            self.logger.error(f"Error handling Kraken message: {e}")
            
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning(f"Kraken WebSocket connection closed")
            except Exception as e:
                self.logger.error(f"Kraken WebSocket error: {e}")
            
            if self.is_running:
                reconnect_count += 1
                wait_time = min(2 ** reconnect_count, 60)
                self.logger.info(f"Reconnecting to Kraken in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
    
    async def _handle_kraken_message(self, data: Any):
        """Handle incoming Kraken WebSocket message."""
        try:
            # Kraken messages can be arrays or objects
            if isinstance(data, list) and len(data) > 1:
                # This is likely ticker data
                await self._handle_ticker_data('kraken', data)
            
            # Record metrics
            self.metrics.record_market_data_update('kraken', 'websocket', 'message', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling Kraken message: {e}")
    
    async def _handle_ticker_data(self, exchange: str, data: Dict[str, Any]):
        """Handle ticker data from WebSocket."""
        try:
            # Normalize ticker data and call registered handlers
            for handler in self.data_handlers['ticker']:
                await handler(exchange, 'ticker', data)
                
        except Exception as e:
            self.logger.error(f"Error handling ticker data: {e}")
    
    async def _handle_orderbook_data(self, exchange: str, data: Dict[str, Any]):
        """Handle order book data from WebSocket."""
        try:
            # Normalize order book data and call registered handlers
            for handler in self.data_handlers['orderbook']:
                await handler(exchange, 'orderbook', data)
                
        except Exception as e:
            self.logger.error(f"Error handling orderbook data: {e}")
    
    async def _handle_trade_data(self, exchange: str, data: Dict[str, Any]):
        """Handle trade data from WebSocket."""
        try:
            # Normalize trade data and call registered handlers
            for handler in self.data_handlers['trades']:
                await handler(exchange, 'trades', data)
                
        except Exception as e:
            self.logger.error(f"Error handling trade data: {e}")
    
    async def _handle_kline_data(self, exchange: str, data: Dict[str, Any]):
        """Handle kline/candlestick data from WebSocket."""
        try:
            # Normalize kline data and call registered handlers
            for handler in self.data_handlers['kline']:
                await handler(exchange, 'kline', data)
                
        except Exception as e:
            self.logger.error(f"Error handling kline data: {e}")
    
    def register_handler(self, data_type: str, handler: Callable):
        """Register a data handler for specific data types."""
        if data_type in self.data_handlers:
            self.data_handlers[data_type].append(handler)
            self.logger.info(f"Registered handler for {data_type} data")
        else:
            self.logger.error(f"Unknown data type: {data_type}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current WebSocket connection status."""
        status = {
            'is_running': self.is_running,
            'connections': {},
            'handlers': {k: len(v) for k, v in self.data_handlers.items()}
        }
        
        for exchange_name in self.connection_tasks:
            ws = self.connections.get(exchange_name)
            status['connections'][exchange_name] = {
                'connected': ws is not None and not ws.closed if ws else False,
                'task_running': not self.connection_tasks[exchange_name].done()
            }
        
        return status
