global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'ai-trading-bot'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # AI Trading Bot main application
  - job_name: 'trading-bot'
    static_configs:
      - targets: ['trading_bot:9090']
    scrape_interval: 15s
    metrics_path: /metrics
    scheme: http

  # System metrics from node exporter (if added)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # PostgreSQL metrics (if pg_exporter is added)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis metrics (if redis_exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx metrics (if nginx_exporter is added)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Celery metrics
  - job_name: 'celery'
    static_configs:
      - targets: ['celery_worker:9540']
    scrape_interval: 30s

  # Self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
