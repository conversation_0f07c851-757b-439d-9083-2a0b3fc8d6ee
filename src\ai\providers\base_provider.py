"""
Modern OpenAI-Based AI Provider System.

This module provides a modern, OpenAI-focused provider system using the latest
OpenAI Python SDK (v1.99.1) with support for multiple providers through base_url configuration.

Author: inkbytefo
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from openai import Async<PERSON>penA<PERSON>, OpenAI
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai._exceptions import APIError, RateLimitError, APIConnectionError
from ...config.settings import Settings


class ProviderType(Enum):
    """Supported AI provider types."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    LOCAL = "local"
    CUSTOM = "custom"


@dataclass
class AIRequest:
    """Standard AI request structure."""
    messages: List[Dict[str, str]]
    model: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False
    functions: Optional[List[Dict]] = None
    function_call: Optional[Union[str, Dict]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class AIResponse:
    """Standard AI response structure."""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    success: bool = True
    error: Optional[str] = None
    function_call: Optional[Dict] = None
    metadata: Optional[Dict[str, Any]] = None
    provider: str = ""
    latency_ms: float = 0.0
    cost_usd: float = 0.0
    timestamp: Optional[datetime] = None


@dataclass
class ProviderConfig:
    """Provider configuration."""
    name: str
    provider_type: ProviderType
    api_key: str
    base_url: str
    model_mapping: Dict[str, str]
    rate_limit_rpm: int = 60
    rate_limit_tpm: int = 10000
    timeout_seconds: int = 30
    retry_attempts: int = 3
    cost_per_1k_tokens: Dict[str, float] = None
    enabled: bool = True


class BaseAIProvider(ABC):
    """
    Base class for all AI providers.
    
    Provides a unified interface for OpenAI-compatible AI services,
    with automatic fallback, rate limiting, and cost tracking.
    """
    
    def __init__(self, config: ProviderConfig, settings: Settings):
        self.config = config
        self.settings = settings
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
        
        # Rate limiting
        self.request_count = 0
        self.token_count = 0
        self.last_reset = datetime.utcnow()
        
        # Performance tracking
        self.total_requests = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        self.average_latency = 0.0
        self.error_count = 0
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """Initialize the provider."""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout_seconds),
                headers=self._get_default_headers()
            )
            
            # Test connection
            await self._test_connection()
            
            self.logger.info(f"✅ {self.config.name} provider initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize {self.config.name}: {e}")
            raise
    
    async def close(self):
        """Close the provider and cleanup resources."""
        if self.session:
            await self.session.close()
            self.logger.info(f"Closed {self.config.name} provider")
    
    @abstractmethod
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests."""
        pass
    
    @abstractmethod
    async def _test_connection(self):
        """Test the connection to the provider."""
        pass
    
    @abstractmethod
    def _map_model_name(self, model: str) -> str:
        """Map generic model name to provider-specific model name."""
        pass
    
    @abstractmethod
    def _calculate_cost(self, usage: Dict[str, int], model: str) -> float:
        """Calculate the cost of the request."""
        pass
    
    async def generate(self, request: AIRequest) -> AIResponse:
        """
        Generate AI response with automatic retry and error handling.
        
        Args:
            request: AI request object
            
        Returns:
            AI response object
        """
        if not self.config.enabled:
            raise ValueError(f"Provider {self.config.name} is disabled")
        
        # Check rate limits
        await self._check_rate_limits(request)
        
        # Retry logic
        last_exception = None
        for attempt in range(self.config.retry_attempts):
            try:
                start_time = datetime.utcnow()
                
                # Make the actual API call
                response = await self._make_request(request)
                
                # Calculate metrics
                latency = (datetime.utcnow() - start_time).total_seconds() * 1000
                cost = self._calculate_cost(response.usage, response.model)
                
                # Update response with metadata
                response.provider = self.config.name
                response.latency_ms = latency
                response.cost_usd = cost
                
                # Update statistics
                self._update_statistics(response)
                
                return response
                
            except Exception as e:
                last_exception = e
                self.error_count += 1
                
                if attempt < self.config.retry_attempts - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed for {self.config.name}: {e}. "
                        f"Retrying in {wait_time}s..."
                    )
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error(f"All attempts failed for {self.config.name}: {e}")
        
        raise last_exception
    
    @abstractmethod
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """Make the actual API request to the provider."""
        pass
    
    async def _check_rate_limits(self, request: AIRequest):
        """Check and enforce rate limits."""
        now = datetime.utcnow()
        
        # Reset counters if a minute has passed
        if (now - self.last_reset).total_seconds() >= 60:
            self.request_count = 0
            self.token_count = 0
            self.last_reset = now
        
        # Check request rate limit
        if self.request_count >= self.config.rate_limit_rpm:
            wait_time = 60 - (now - self.last_reset).total_seconds()
            if wait_time > 0:
                self.logger.warning(f"Rate limit reached for {self.config.name}. Waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                self.request_count = 0
                self.token_count = 0
                self.last_reset = datetime.utcnow()
        
        # Estimate tokens for the request
        estimated_tokens = self._estimate_tokens(request)
        
        # Check token rate limit
        if self.token_count + estimated_tokens > self.config.rate_limit_tpm:
            wait_time = 60 - (now - self.last_reset).total_seconds()
            if wait_time > 0:
                self.logger.warning(f"Token rate limit reached for {self.config.name}. Waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                self.request_count = 0
                self.token_count = 0
                self.last_reset = datetime.utcnow()
        
        # Update counters
        self.request_count += 1
        self.token_count += estimated_tokens
    
    def _estimate_tokens(self, request: AIRequest) -> int:
        """Estimate token count for a request."""
        # Simple estimation: ~4 characters per token
        total_chars = sum(len(msg.get('content', '')) for msg in request.messages)
        return max(1, total_chars // 4)
    
    def _update_statistics(self, response: AIResponse):
        """Update provider statistics."""
        self.total_requests += 1
        self.total_tokens += response.usage.get('total_tokens', 0)
        self.total_cost += response.cost_usd
        
        # Update average latency
        self.average_latency = (
            (self.average_latency * (self.total_requests - 1) + response.latency_ms) 
            / self.total_requests
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get provider statistics."""
        return {
            'name': self.config.name,
            'provider_type': self.config.provider_type.value,
            'enabled': self.config.enabled,
            'total_requests': self.total_requests,
            'total_tokens': self.total_tokens,
            'total_cost_usd': round(self.total_cost, 4),
            'average_latency_ms': round(self.average_latency, 2),
            'error_count': self.error_count,
            'error_rate': round(self.error_count / max(1, self.total_requests) * 100, 2),
            'current_rpm': self.request_count,
            'current_tpm': self.token_count,
            'rate_limit_rpm': self.config.rate_limit_rpm,
            'rate_limit_tpm': self.config.rate_limit_tpm
        }
    
    def is_healthy(self) -> bool:
        """Check if the provider is healthy."""
        if not self.config.enabled:
            return False
        
        # Check error rate
        if self.total_requests > 10:
            error_rate = self.error_count / self.total_requests
            if error_rate > 0.1:  # More than 10% error rate
                return False
        
        # Check if we're hitting rate limits too often
        if self.request_count >= self.config.rate_limit_rpm * 0.9:
            return False
        
        return True
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        try:
            # Simple test request
            test_request = AIRequest(
                messages=[{"role": "user", "content": "Hello"}],
                model="gpt-3.5-turbo",
                max_tokens=5
            )
            
            start_time = datetime.utcnow()
            await self._make_request(test_request)
            latency = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            return {
                'healthy': True,
                'latency_ms': round(latency, 2),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
