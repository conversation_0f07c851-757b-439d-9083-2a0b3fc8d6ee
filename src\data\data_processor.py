"""
Data Processor for AI Trading Bot System.

This module processes and normalizes market data from different exchanges,
ensuring data quality and consistency across the system.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
from dataclasses import dataclass

from ..config.settings import Settings
from ..monitoring.metrics import get_metrics_collector


@dataclass
class NormalizedTicker:
    """Normalized ticker data structure."""
    exchange: str
    symbol: str
    timestamp: datetime
    last_price: Decimal
    bid_price: Optional[Decimal]
    ask_price: Optional[Decimal]
    volume_24h: Optional[Decimal]
    price_change_24h: Optional[Decimal]
    price_change_percent_24h: Optional[float]


@dataclass
class NormalizedOrderBook:
    """Normalized order book data structure."""
    exchange: str
    symbol: str
    timestamp: datetime
    bids: List[tuple[Decimal, Decimal]]  # [(price, quantity), ...]
    asks: List[tuple[Decimal, Decimal]]  # [(price, quantity), ...]
    
    
@dataclass
class NormalizedTrade:
    """Normalized trade data structure."""
    exchange: str
    symbol: str
    timestamp: datetime
    price: Decimal
    quantity: Decimal
    side: str  # 'buy' or 'sell'
    trade_id: Optional[str] = None


@dataclass
class NormalizedOHLCV:
    """Normalized OHLCV data structure."""
    exchange: str
    symbol: str
    timeframe: str
    timestamp: datetime
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: Decimal


class DataProcessor:
    """
    Processes and normalizes market data from multiple exchanges.
    
    Ensures data quality, consistency, and provides unified data structures
    for the trading system.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Data validation settings
        self.max_price_deviation = 0.1  # 10% max price deviation
        self.min_volume_threshold = Decimal('0.001')
        self.max_spread_percentage = 0.05  # 5% max spread
        
        # Data quality tracking
        self.quality_stats = {
            'processed_records': 0,
            'valid_records': 0,
            'invalid_records': 0,
            'last_processed': None
        }
    
    async def process_ticker_data(self, exchange: str, raw_data: Dict[str, Any]) -> Optional[NormalizedTicker]:
        """Process and normalize ticker data."""
        try:
            # Exchange-specific processing
            if exchange == 'binance':
                return await self._process_binance_ticker(raw_data)
            elif exchange == 'coinbasepro':
                return await self._process_coinbase_ticker(raw_data)
            elif exchange == 'kraken':
                return await self._process_kraken_ticker(raw_data)
            else:
                self.logger.warning(f"Unknown exchange for ticker processing: {exchange}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error processing ticker data from {exchange}: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_binance_ticker(self, data: Dict[str, Any]) -> Optional[NormalizedTicker]:
        """Process Binance ticker data."""
        try:
            symbol = data.get('s', '').replace('USDT', '/USDT').replace('BTC', '/BTC')
            if not symbol or '/' not in symbol:
                symbol = self._normalize_symbol(data.get('s', ''))
            
            ticker = NormalizedTicker(
                exchange='binance',
                symbol=symbol,
                timestamp=datetime.fromtimestamp(data.get('E', 0) / 1000),
                last_price=Decimal(str(data.get('c', 0))),
                bid_price=Decimal(str(data.get('b', 0))) if data.get('b') else None,
                ask_price=Decimal(str(data.get('a', 0))) if data.get('a') else None,
                volume_24h=Decimal(str(data.get('v', 0))) if data.get('v') else None,
                price_change_24h=Decimal(str(data.get('P', 0))) if data.get('P') else None,
                price_change_percent_24h=float(data.get('P', 0)) if data.get('P') else None
            )
            
            if await self._validate_ticker(ticker):
                self.quality_stats['valid_records'] += 1
                return ticker
            else:
                self.quality_stats['invalid_records'] += 1
                return None
                
        except (ValueError, InvalidOperation, KeyError) as e:
            self.logger.error(f"Error processing Binance ticker: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_coinbase_ticker(self, data: Dict[str, Any]) -> Optional[NormalizedTicker]:
        """Process Coinbase Pro ticker data."""
        try:
            ticker = NormalizedTicker(
                exchange='coinbasepro',
                symbol=data.get('product_id', ''),
                timestamp=datetime.fromisoformat(data.get('time', '').replace('Z', '+00:00')),
                last_price=Decimal(str(data.get('price', 0))),
                bid_price=Decimal(str(data.get('best_bid', 0))) if data.get('best_bid') else None,
                ask_price=Decimal(str(data.get('best_ask', 0))) if data.get('best_ask') else None,
                volume_24h=Decimal(str(data.get('volume_24h', 0))) if data.get('volume_24h') else None,
                price_change_24h=None,  # Not provided in Coinbase ticker
                price_change_percent_24h=None
            )
            
            if await self._validate_ticker(ticker):
                self.quality_stats['valid_records'] += 1
                return ticker
            else:
                self.quality_stats['invalid_records'] += 1
                return None
                
        except (ValueError, InvalidOperation, KeyError) as e:
            self.logger.error(f"Error processing Coinbase ticker: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_kraken_ticker(self, data: Any) -> Optional[NormalizedTicker]:
        """Process Kraken ticker data."""
        try:
            # Kraken sends ticker data as arrays
            if isinstance(data, list) and len(data) > 1:
                ticker_data = data[1]
                symbol = data[3] if len(data) > 3 else 'UNKNOWN'
                
                ticker = NormalizedTicker(
                    exchange='kraken',
                    symbol=self._normalize_kraken_symbol(symbol),
                    timestamp=datetime.utcnow(),  # Kraken doesn't provide timestamp in ticker
                    last_price=Decimal(str(ticker_data.get('c', [0])[0])) if ticker_data.get('c') else Decimal('0'),
                    bid_price=Decimal(str(ticker_data.get('b', [0])[0])) if ticker_data.get('b') else None,
                    ask_price=Decimal(str(ticker_data.get('a', [0])[0])) if ticker_data.get('a') else None,
                    volume_24h=Decimal(str(ticker_data.get('v', [0])[1])) if ticker_data.get('v') else None,
                    price_change_24h=None,
                    price_change_percent_24h=None
                )
                
                if await self._validate_ticker(ticker):
                    self.quality_stats['valid_records'] += 1
                    return ticker
                else:
                    self.quality_stats['invalid_records'] += 1
                    return None
            
            return None
                
        except (ValueError, InvalidOperation, KeyError, IndexError) as e:
            self.logger.error(f"Error processing Kraken ticker: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def process_orderbook_data(self, exchange: str, raw_data: Dict[str, Any]) -> Optional[NormalizedOrderBook]:
        """Process and normalize order book data."""
        try:
            # Exchange-specific processing
            if exchange == 'binance':
                return await self._process_binance_orderbook(raw_data)
            elif exchange == 'coinbasepro':
                return await self._process_coinbase_orderbook(raw_data)
            elif exchange == 'kraken':
                return await self._process_kraken_orderbook(raw_data)
            else:
                self.logger.warning(f"Unknown exchange for orderbook processing: {exchange}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error processing orderbook data from {exchange}: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_binance_orderbook(self, data: Dict[str, Any]) -> Optional[NormalizedOrderBook]:
        """Process Binance order book data."""
        try:
            symbol = self._normalize_symbol(data.get('s', ''))
            
            # Process bids and asks
            bids = [(Decimal(str(bid[0])), Decimal(str(bid[1]))) 
                   for bid in data.get('b', [])]
            asks = [(Decimal(str(ask[0])), Decimal(str(ask[1]))) 
                   for ask in data.get('a', [])]
            
            orderbook = NormalizedOrderBook(
                exchange='binance',
                symbol=symbol,
                timestamp=datetime.fromtimestamp(data.get('E', 0) / 1000),
                bids=bids,
                asks=asks
            )
            
            if await self._validate_orderbook(orderbook):
                self.quality_stats['valid_records'] += 1
                return orderbook
            else:
                self.quality_stats['invalid_records'] += 1
                return None
                
        except (ValueError, InvalidOperation, KeyError) as e:
            self.logger.error(f"Error processing Binance orderbook: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_coinbase_orderbook(self, data: Dict[str, Any]) -> Optional[NormalizedOrderBook]:
        """Process Coinbase Pro order book data."""
        try:
            # Coinbase sends l2update messages
            if data.get('type') == 'l2update':
                symbol = data.get('product_id', '')
                
                # Process changes (this would need to be applied to existing orderbook)
                changes = data.get('changes', [])
                bids = []
                asks = []
                
                for change in changes:
                    side, price, size = change
                    price_decimal = Decimal(str(price))
                    size_decimal = Decimal(str(size))
                    
                    if side == 'buy':
                        bids.append((price_decimal, size_decimal))
                    else:
                        asks.append((price_decimal, size_decimal))
                
                orderbook = NormalizedOrderBook(
                    exchange='coinbasepro',
                    symbol=symbol,
                    timestamp=datetime.fromisoformat(data.get('time', '').replace('Z', '+00:00')),
                    bids=bids,
                    asks=asks
                )
                
                if await self._validate_orderbook(orderbook):
                    self.quality_stats['valid_records'] += 1
                    return orderbook
                else:
                    self.quality_stats['invalid_records'] += 1
                    return None
            
            return None
                
        except (ValueError, InvalidOperation, KeyError) as e:
            self.logger.error(f"Error processing Coinbase orderbook: {e}")
            self.quality_stats['invalid_records'] += 1
            return None
    
    async def _process_kraken_orderbook(self, data: Any) -> Optional[NormalizedOrderBook]:
        """Process Kraken order book data."""
        # Kraken orderbook processing would be implemented here
        # For now, return None as it's complex to implement without full specification
        return None
    
    async def process_ohlcv_data(self, exchange: str, symbol: str, timeframe: str, 
                                raw_data: List[List]) -> List[NormalizedOHLCV]:
        """Process and normalize OHLCV data."""
        try:
            normalized_data = []
            
            for ohlcv in raw_data:
                try:
                    normalized_ohlcv = NormalizedOHLCV(
                        exchange=exchange,
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=datetime.fromtimestamp(ohlcv[0] / 1000),
                        open_price=Decimal(str(ohlcv[1])),
                        high_price=Decimal(str(ohlcv[2])),
                        low_price=Decimal(str(ohlcv[3])),
                        close_price=Decimal(str(ohlcv[4])),
                        volume=Decimal(str(ohlcv[5]))
                    )
                    
                    if await self._validate_ohlcv(normalized_ohlcv):
                        normalized_data.append(normalized_ohlcv)
                        self.quality_stats['valid_records'] += 1
                    else:
                        self.quality_stats['invalid_records'] += 1
                        
                except (ValueError, InvalidOperation, IndexError) as e:
                    self.logger.error(f"Error processing OHLCV record: {e}")
                    self.quality_stats['invalid_records'] += 1
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error processing OHLCV data: {e}")
            return []
    
    async def _validate_ticker(self, ticker: NormalizedTicker) -> bool:
        """Validate ticker data quality."""
        try:
            # Check for required fields
            if not ticker.symbol or ticker.last_price <= 0:
                return False
            
            # Check for reasonable price values
            if ticker.last_price > Decimal('1000000'):  # Sanity check
                return False
            
            # Check spread if both bid and ask are available
            if ticker.bid_price and ticker.ask_price:
                spread = (ticker.ask_price - ticker.bid_price) / ticker.bid_price
                if spread > self.max_spread_percentage:
                    self.logger.warning(f"High spread detected: {spread:.2%} for {ticker.symbol}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating ticker: {e}")
            return False
    
    async def _validate_orderbook(self, orderbook: NormalizedOrderBook) -> bool:
        """Validate order book data quality."""
        try:
            # Check for required fields
            if not orderbook.symbol or not orderbook.bids or not orderbook.asks:
                return False
            
            # Check that bids are sorted descending and asks ascending
            if len(orderbook.bids) > 1:
                for i in range(len(orderbook.bids) - 1):
                    if orderbook.bids[i][0] < orderbook.bids[i + 1][0]:
                        return False
            
            if len(orderbook.asks) > 1:
                for i in range(len(orderbook.asks) - 1):
                    if orderbook.asks[i][0] > orderbook.asks[i + 1][0]:
                        return False
            
            # Check that best bid < best ask
            if orderbook.bids and orderbook.asks:
                best_bid = orderbook.bids[0][0]
                best_ask = orderbook.asks[0][0]
                if best_bid >= best_ask:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating orderbook: {e}")
            return False
    
    async def _validate_ohlcv(self, ohlcv: NormalizedOHLCV) -> bool:
        """Validate OHLCV data quality."""
        try:
            # Check for required fields
            if not ohlcv.symbol or any(price <= 0 for price in [
                ohlcv.open_price, ohlcv.high_price, ohlcv.low_price, ohlcv.close_price
            ]):
                return False
            
            # Check OHLC relationships
            if not (ohlcv.low_price <= ohlcv.open_price <= ohlcv.high_price and
                   ohlcv.low_price <= ohlcv.close_price <= ohlcv.high_price):
                return False
            
            # Check volume
            if ohlcv.volume < 0:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating OHLCV: {e}")
            return False
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol format to standard format (e.g., BTC/USDT)."""
        # Common symbol normalizations
        symbol = symbol.upper()
        
        # Handle common patterns
        if symbol.endswith('USDT'):
            base = symbol[:-4]
            return f"{base}/USDT"
        elif symbol.endswith('BTC'):
            base = symbol[:-3]
            return f"{base}/BTC"
        elif symbol.endswith('ETH'):
            base = symbol[:-3]
            return f"{base}/ETH"
        
        # If already in correct format, return as is
        if '/' in symbol:
            return symbol
        
        # Default fallback
        return symbol
    
    def _normalize_kraken_symbol(self, symbol: str) -> str:
        """Normalize Kraken symbol format."""
        # Kraken uses different symbol formats
        symbol_map = {
            # BTC mappings
            'XBTUSD': 'BTC/USD',
            'XBTUSDT': 'BTC/USDT',
            'XXBTZUSD': 'BTC/USD',
            'XXBTZUSDT': 'BTC/USDT',

            # ETH mappings
            'ETHUSD': 'ETH/USD',
            'ETHUSDT': 'ETH/USDT',
            'XETHZUSD': 'ETH/USD',
            'XETHZUSDT': 'ETH/USDT',

            # Other major coins
            'ADAUSD': 'ADA/USD',
            'ADAUSDT': 'ADA/USDT',
            'SOLUSD': 'SOL/USD',
            'SOLUSDT': 'SOL/USDT',
            'DOTUSD': 'DOT/USD',
            'DOTUSDT': 'DOT/USDT',
            'LINKUSD': 'LINK/USD',
            'LINKUSDT': 'LINK/USDT',
            'MATICUSD': 'MATIC/USD',
            'MATICUSDT': 'MATIC/USDT',
            'DOGEUSD': 'DOGE/USD',
            'DOGEUSDT': 'DOGE/USDT',
        }

        return symbol_map.get(symbol, symbol)
    
    def get_quality_stats(self) -> Dict[str, Any]:
        """Get data quality statistics."""
        total_records = self.quality_stats['valid_records'] + self.quality_stats['invalid_records']
        
        return {
            'total_processed': total_records,
            'valid_records': self.quality_stats['valid_records'],
            'invalid_records': self.quality_stats['invalid_records'],
            'quality_rate': (self.quality_stats['valid_records'] / total_records * 100) if total_records > 0 else 0,
            'last_processed': self.quality_stats['last_processed']
        }
