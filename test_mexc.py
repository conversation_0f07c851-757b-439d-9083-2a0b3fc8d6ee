#!/usr/bin/env python3
"""
MEXC Exchange Test Script
Test MEXC integration without real API keys
"""

import asyncio
import ccxt.async_support as ccxt
from src.config.settings import Settings

async def test_mexc_connection():
    """Test MEXC connection and basic functionality."""
    print("[TEST] MEXC Exchange Test başlatılıyor...")
    
    try:
        # Test without API keys (public endpoints only)
        exchange = ccxt.mexc({
            'enableRateLimit': True,
            'sandbox': False,
            'timeout': 30000,
        })
        
        print("[INFO] MEXC exchange instance oluşturuldu")
        
        # Test 1: Load markets
        print("\n[TEST 1] Markets yükleniyor...")
        markets = await exchange.load_markets()
        print(f"[OK] {len(markets)} market yüklendi")
        
        # Test 2: Check popular trading pairs
        popular_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']
        available_pairs = []
        
        print("\n[TEST 2] Popüler trading pairs kontrol ediliyor...")
        for pair in popular_pairs:
            if pair in markets:
                available_pairs.append(pair)
                print(f"[OK] {pair} - Mevcut")
            else:
                print(f"[WARN] {pair} - Bulunamadı")
        
        # Test 3: Get ticker data
        if available_pairs:
            test_pair = available_pairs[0]
            print(f"\n[TEST 3] {test_pair} ticker verisi alınıyor...")
            ticker = await exchange.fetch_ticker(test_pair)
            print(f"[OK] {test_pair} Fiyat: ${ticker['last']:.2f}")
            print(f"[INFO] 24h Değişim: {ticker['percentage']:.2f}%")
            print(f"[INFO] Volume: {ticker['baseVolume']:.2f}")
        
        # Test 4: Get OHLCV data
        if available_pairs:
            test_pair = available_pairs[0]
            print(f"\n[TEST 4] {test_pair} OHLCV verisi alınıyor...")
            ohlcv = await exchange.fetch_ohlcv(test_pair, '1h', limit=5)
            print(f"[OK] {len(ohlcv)} adet OHLCV verisi alındı")
            if ohlcv:
                latest = ohlcv[-1]
                print(f"[INFO] Son mum: O:{latest[1]:.2f} H:{latest[2]:.2f} L:{latest[3]:.2f} C:{latest[4]:.2f}")
        
        # Test 5: Server time
        print("\n[TEST 5] Server time kontrol ediliyor...")
        try:
            server_time = await exchange.fetch_time()
            local_time = exchange.milliseconds()
            time_diff = server_time - local_time
            print(f"[OK] Server time alındı")
            print(f"[INFO] Zaman farkı: {time_diff}ms")
            if abs(time_diff) > 1000:
                print(f"[WARN] Büyük zaman farkı tespit edildi: {time_diff}ms")
            else:
                print(f"[OK] Zaman senkronizasyonu normal")
        except Exception as e:
            print(f"[WARN] Server time alınamadı: {e}")
        
        # Test 6: Exchange info
        print("\n[TEST 6] Exchange bilgileri...")
        print(f"[INFO] Exchange ID: {exchange.id}")
        print(f"[INFO] Rate limit: {exchange.rateLimit}ms")
        print(f"[INFO] Has fetch_ticker: {exchange.has['fetchTicker']}")
        print(f"[INFO] Has fetch_ohlcv: {exchange.has['fetchOHLCV']}")
        print(f"[INFO] Has fetch_order_book: {exchange.has['fetchOrderBook']}")
        
        await exchange.close()
        print("\n[SUCCESS] MEXC test başarıyla tamamlandı!")
        return True
        
    except Exception as e:
        print(f"\n[ERROR] MEXC test hatası: {e}")
        return False

async def test_mexc_with_settings():
    """Test MEXC with settings configuration."""
    print("\n" + "="*50)
    print("[TEST] Settings ile MEXC testi...")
    
    try:
        settings = Settings()
        
        # Check if MEXC settings exist
        if hasattr(settings.exchanges, 'mexc_api_key'):
            print("[OK] MEXC ayarları settings'de mevcut")
            print(f"[INFO] API Key: {'Ayarlandı' if settings.exchanges.mexc_api_key else 'Ayarlanmadı'}")
            print(f"[INFO] Secret Key: {'Ayarlandı' if settings.exchanges.mexc_secret_key else 'Ayarlanmadı'}")
            print(f"[INFO] Testnet: {settings.exchanges.mexc_testnet}")
        else:
            print("[WARN] MEXC ayarları settings'de bulunamadı")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Settings test hatası: {e}")
        return False

async def main():
    """Ana test fonksiyonu."""
    print("🚀 MEXC Exchange Integration Test")
    print("=" * 50)
    
    # Test 1: Basic MEXC connection
    success1 = await test_mexc_connection()
    
    # Test 2: Settings integration
    success2 = await test_mexc_with_settings()
    
    print("\n" + "=" * 50)
    print("📊 TEST SONUÇLARI:")
    print(f"✅ MEXC Bağlantı Testi: {'BAŞARILI' if success1 else 'BAŞARISIZ'}")
    print(f"✅ Settings Entegrasyonu: {'BAŞARILI' if success2 else 'BAŞARISIZ'}")
    
    if success1 and success2:
        print("\n🎉 MEXC entegrasyonu hazır!")
        print("💡 API anahtarlarınızı .env dosyasına ekleyerek gerçek trading'e başlayabilirsiniz.")
    else:
        print("\n❌ Bazı testler başarısız oldu. Lütfen hataları kontrol edin.")

if __name__ == "__main__":
    asyncio.run(main())
