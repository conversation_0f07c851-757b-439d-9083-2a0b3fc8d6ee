"""
Risk Management Module for AI Trading Bot System.

This module provides comprehensive risk management capabilities
including position sizing, stop losses, and portfolio risk controls.

Author: inkbytefo
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.settings import Settings


@dataclass
class RiskMetrics:
    """Risk metrics for a position or portfolio."""
    var_95: float  # Value at Risk (95% confidence)
    max_drawdown: float
    sharpe_ratio: float
    volatility: float
    beta: float
    correlation: float


@dataclass
class PositionRisk:
    """Risk assessment for a trading position."""
    symbol: str
    position_size: float
    risk_amount: float
    stop_loss: float
    take_profit: float
    risk_reward_ratio: float
    max_loss_percentage: float


class RiskManager:
    """
    Comprehensive risk management system for trading operations.
    
    Manages position sizing, stop losses, portfolio risk limits,
    and provides risk assessment for trading decisions.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Risk limits from settings
        self.max_position_size = settings.trading.max_position_size
        self.max_daily_loss = settings.risk.max_daily_loss
        self.max_drawdown = settings.risk.max_drawdown
        self.emergency_stop_loss = settings.risk.emergency_stop_loss
        
        # Risk tracking
        self.daily_pnl = 0.0
        self.total_exposure = 0.0
        self.active_positions: Dict[str, PositionRisk] = {}
        self.positions: Dict[str, PositionRisk] = {}  # Alias for compatibility
        self.risk_events: List[Dict[str, Any]] = []
        
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss: float, portfolio_value: float,
                              risk_percentage: Optional[float] = None) -> float:
        """
        Calculate optimal position size based on risk parameters.
        
        Args:
            symbol: Trading pair symbol
            entry_price: Planned entry price
            stop_loss: Stop loss price
            portfolio_value: Total portfolio value
            risk_percentage: Risk percentage (defaults to settings)
            
        Returns:
            Calculated position size
        """
        try:
            if risk_percentage is None:
                risk_percentage = self.max_position_size
            
            # Calculate risk per unit
            risk_per_unit = abs(entry_price - stop_loss)
            if risk_per_unit == 0:
                self.logger.warning(f"Zero risk per unit for {symbol}")
                return 0.0
            
            # Calculate maximum risk amount
            max_risk_amount = portfolio_value * risk_percentage
            
            # Calculate position size
            position_size = max_risk_amount / risk_per_unit
            
            # Apply maximum position size limit
            max_position_value = portfolio_value * self.max_position_size
            max_position_size = max_position_value / entry_price
            
            final_position_size = min(position_size, max_position_size)
            
            self.logger.info(f"Calculated position size for {symbol}: {final_position_size}")
            return final_position_size
            
        except Exception as e:
            self.logger.error(f"Position size calculation failed for {symbol}: {e}")
            return 0.0
    
    def validate_trade(self, symbol: str, side: str, quantity: float, 
                      price: float, portfolio_value: float) -> Dict[str, Any]:
        """
        Validate a trade against risk management rules.
        
        Args:
            symbol: Trading pair symbol
            side: 'buy' or 'sell'
            quantity: Trade quantity
            price: Trade price
            portfolio_value: Current portfolio value
            
        Returns:
            Validation result with approval status and reasons
        """
        try:
            validation_result = {
                "approved": True,
                "reasons": [],
                "warnings": [],
                "risk_score": 0.0
            }
            
            # Calculate trade value
            trade_value = quantity * price
            
            # Check position size limit
            position_percentage = trade_value / portfolio_value
            if position_percentage > self.max_position_size:
                validation_result["approved"] = False
                validation_result["reasons"].append(
                    f"Position size ({position_percentage:.2%}) exceeds limit ({self.max_position_size:.2%})"
                )
            
            # Check daily loss limit
            if self.daily_pnl < -self.max_daily_loss * portfolio_value:
                validation_result["approved"] = False
                validation_result["reasons"].append(
                    f"Daily loss limit exceeded: {self.daily_pnl:.2f}"
                )
            
            # Check total exposure
            new_exposure = self.total_exposure + trade_value
            max_exposure = portfolio_value * 0.8  # 80% max exposure
            if new_exposure > max_exposure:
                validation_result["approved"] = False
                validation_result["reasons"].append(
                    f"Total exposure would exceed limit: {new_exposure:.2f} > {max_exposure:.2f}"
                )
            
            # Check if symbol already has large position
            if symbol in self.active_positions:
                existing_risk = self.active_positions[symbol].risk_amount
                if existing_risk > portfolio_value * 0.1:  # 10% risk per symbol
                    validation_result["warnings"].append(
                        f"Large existing position in {symbol}"
                    )
            
            # Calculate risk score (0-1, higher is riskier)
            risk_factors = [
                position_percentage / self.max_position_size,
                abs(self.daily_pnl) / (self.max_daily_loss * portfolio_value),
                new_exposure / max_exposure
            ]
            validation_result["risk_score"] = min(max(risk_factors), 1.0)
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Trade validation failed: {e}")
            return {
                "approved": False,
                "reasons": [f"Validation error: {str(e)}"],
                "warnings": [],
                "risk_score": 1.0
            }
    
    def calculate_stop_loss(self, symbol: str, entry_price: float, 
                           side: str, volatility: float = None) -> float:
        """
        Calculate appropriate stop loss level.
        
        Args:
            symbol: Trading pair symbol
            entry_price: Entry price
            side: 'buy' or 'sell'
            volatility: Price volatility (optional)
            
        Returns:
            Stop loss price
        """
        try:
            # Default stop loss percentage
            stop_loss_pct = self.settings.trading.stop_loss_percentage
            
            # Adjust based on volatility if provided
            if volatility:
                # Higher volatility = wider stop loss
                volatility_adjustment = min(volatility * 2, 0.1)  # Max 10% adjustment
                stop_loss_pct = max(stop_loss_pct, volatility_adjustment)
            
            if side.lower() == 'buy':
                stop_loss = entry_price * (1 - stop_loss_pct)
            else:  # sell
                stop_loss = entry_price * (1 + stop_loss_pct)
            
            return stop_loss
            
        except Exception as e:
            self.logger.error(f"Stop loss calculation failed: {e}")
            return entry_price * 0.95 if side.lower() == 'buy' else entry_price * 1.05
    
    def calculate_take_profit(self, symbol: str, entry_price: float, 
                             side: str, risk_reward_ratio: float = None) -> float:
        """
        Calculate take profit level.
        
        Args:
            symbol: Trading pair symbol
            entry_price: Entry price
            side: 'buy' or 'sell'
            risk_reward_ratio: Desired risk/reward ratio
            
        Returns:
            Take profit price
        """
        try:
            # Default take profit percentage
            take_profit_pct = self.settings.trading.take_profit_percentage
            
            # Adjust based on risk/reward ratio if provided
            if risk_reward_ratio:
                stop_loss_pct = self.settings.trading.stop_loss_percentage
                take_profit_pct = stop_loss_pct * risk_reward_ratio
            
            if side.lower() == 'buy':
                take_profit = entry_price * (1 + take_profit_pct)
            else:  # sell
                take_profit = entry_price * (1 - take_profit_pct)
            
            return take_profit
            
        except Exception as e:
            self.logger.error(f"Take profit calculation failed: {e}")
            return entry_price * 1.15 if side.lower() == 'buy' else entry_price * 0.85
    
    def add_position(self, symbol: str, quantity: float, entry_price: float,
                    stop_loss: float, take_profit: float) -> None:
        """Add a new position to risk tracking."""
        try:
            risk_amount = abs(quantity * (entry_price - stop_loss))
            risk_reward_ratio = abs(take_profit - entry_price) / abs(entry_price - stop_loss)
            max_loss_pct = risk_amount / (quantity * entry_price)
            
            position_risk = PositionRisk(
                symbol=symbol,
                position_size=quantity,
                risk_amount=risk_amount,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                max_loss_percentage=max_loss_pct
            )
            
            self.active_positions[symbol] = position_risk
            self.total_exposure += quantity * entry_price
            
            self.logger.info(f"Added position tracking for {symbol}")
            
        except Exception as e:
            self.logger.error(f"Failed to add position tracking: {e}")
    
    def remove_position(self, symbol: str, exit_price: float) -> None:
        """Remove a position from risk tracking."""
        try:
            if symbol in self.active_positions:
                position = self.active_positions[symbol]
                
                # Calculate P&L
                pnl = position.position_size * (exit_price - (position.risk_amount / position.position_size))
                self.daily_pnl += pnl
                
                # Update exposure
                self.total_exposure -= position.position_size * exit_price
                
                # Remove position
                del self.active_positions[symbol]
                
                self.logger.info(f"Removed position tracking for {symbol}, P&L: {pnl}")
                
        except Exception as e:
            self.logger.error(f"Failed to remove position tracking: {e}")
    
    def check_emergency_stop(self, portfolio_value: float) -> bool:
        """Check if emergency stop should be triggered."""
        try:
            # Check daily loss
            daily_loss_pct = abs(self.daily_pnl) / portfolio_value
            if daily_loss_pct >= self.emergency_stop_loss:
                self.logger.critical(f"Emergency stop triggered: Daily loss {daily_loss_pct:.2%}")
                return True
            
            # Check drawdown (placeholder - would need historical data)
            # if current_drawdown >= self.max_drawdown:
            #     return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Emergency stop check failed: {e}")
            return False
    
    def get_portfolio_risk_metrics(self, portfolio_value: float) -> Dict[str, Any]:
        """Get current portfolio risk metrics."""
        try:
            total_risk = sum(pos.risk_amount for pos in self.active_positions.values())
            exposure_pct = self.total_exposure / portfolio_value if portfolio_value > 0 else 0
            risk_pct = total_risk / portfolio_value if portfolio_value > 0 else 0
            
            return {
                "total_exposure": self.total_exposure,
                "exposure_percentage": exposure_pct,
                "total_risk": total_risk,
                "risk_percentage": risk_pct,
                "daily_pnl": self.daily_pnl,
                "daily_pnl_percentage": self.daily_pnl / portfolio_value if portfolio_value > 0 else 0,
                "active_positions": len(self.active_positions),
                "emergency_stop_triggered": self.check_emergency_stop(portfolio_value)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get risk metrics: {e}")
            return {}
    
    def reset_daily_tracking(self):
        """Reset daily tracking metrics (call at start of each day)."""
        self.daily_pnl = 0.0
        self.risk_events.clear()
        self.logger.info("Daily risk tracking reset")
    
    def log_risk_event(self, event_type: str, description: str, severity: str = "info"):
        """Log a risk management event."""
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": event_type,
            "description": description,
            "severity": severity
        }
        self.risk_events.append(event)

        if severity == "critical":
            self.logger.critical(f"Risk Event: {description}")
        elif severity == "warning":
            self.logger.warning(f"Risk Event: {description}")
        else:
            self.logger.info(f"Risk Event: {description}")

    async def check_risk_limits(self, portfolio_value: float = None, current_positions: dict = None) -> bool:
        """
        Comprehensive risk limits check for trading engine health monitoring.

        Args:
            portfolio_value: Current portfolio value (optional)
            current_positions: Current open positions (optional)

        Returns:
            dict: Risk check results with status and details
        """
        try:
            risk_status = {
                "status": "healthy",
                "checks_passed": 0,
                "checks_failed": 0,
                "warnings": [],
                "errors": [],
                "details": {}
            }

            # 1. Emergency Stop Check
            if portfolio_value:
                emergency_stop = self.check_emergency_stop(portfolio_value)
                if emergency_stop:
                    risk_status["status"] = "critical"
                    risk_status["checks_failed"] += 1
                    risk_status["errors"].append("Emergency stop triggered")
                    self.log_risk_event("emergency_stop", "Emergency stop activated", "critical")
                else:
                    risk_status["checks_passed"] += 1
                    risk_status["details"]["emergency_stop"] = "OK"

            # 2. Daily Loss Limit Check
            if portfolio_value and self.daily_pnl < 0:
                daily_loss_pct = abs(self.daily_pnl) / portfolio_value
                max_daily_loss = self.settings.risk.max_daily_loss_percentage / 100

                if daily_loss_pct >= max_daily_loss:
                    risk_status["status"] = "critical"
                    risk_status["checks_failed"] += 1
                    risk_status["errors"].append(f"Daily loss limit exceeded: {daily_loss_pct:.2%}")
                    self.log_risk_event("daily_loss_limit", f"Daily loss limit exceeded: {daily_loss_pct:.2%}", "critical")
                elif daily_loss_pct >= max_daily_loss * 0.8:  # 80% of limit
                    risk_status["warnings"].append(f"Daily loss approaching limit: {daily_loss_pct:.2%}")
                    self.log_risk_event("daily_loss_warning", f"Daily loss approaching limit: {daily_loss_pct:.2%}", "warning")
                    risk_status["checks_passed"] += 1
                else:
                    risk_status["checks_passed"] += 1
                    risk_status["details"]["daily_loss"] = f"{daily_loss_pct:.2%}"

            # 3. Position Count Check
            if current_positions:
                position_count = len(current_positions)
                max_positions = self.settings.risk.max_positions

                if position_count > max_positions:
                    risk_status["status"] = "warning" if risk_status["status"] == "healthy" else risk_status["status"]
                    risk_status["warnings"].append(f"Position count ({position_count}) exceeds limit ({max_positions})")
                    self.log_risk_event("position_limit", f"Too many positions: {position_count}/{max_positions}", "warning")
                    risk_status["checks_passed"] += 1  # Warning, not failure
                else:
                    risk_status["checks_passed"] += 1
                    risk_status["details"]["position_count"] = f"{position_count}/{max_positions}"

            # 4. Portfolio Concentration Check
            if current_positions and portfolio_value:
                max_position_value = 0
                for position in current_positions.values():
                    position_value = position.get('value', 0)
                    if position_value > max_position_value:
                        max_position_value = position_value

                concentration_pct = max_position_value / portfolio_value
                max_concentration = self.settings.risk.max_position_size / 100

                if concentration_pct > max_concentration:
                    risk_status["status"] = "warning" if risk_status["status"] == "healthy" else risk_status["status"]
                    risk_status["warnings"].append(f"Portfolio concentration too high: {concentration_pct:.2%}")
                    self.log_risk_event("concentration", f"High concentration: {concentration_pct:.2%}", "warning")
                    risk_status["checks_passed"] += 1
                else:
                    risk_status["checks_passed"] += 1
                    risk_status["details"]["max_concentration"] = f"{concentration_pct:.2%}"

            # 5. Risk Manager State Check
            risk_status["checks_passed"] += 1
            risk_status["details"]["risk_manager"] = "operational"
            risk_status["details"]["tracked_positions"] = len(self.positions)
            risk_status["details"]["daily_pnl"] = self.daily_pnl

            # Set final status based on checks
            if risk_status["checks_failed"] > 0:
                risk_status["status"] = "critical"
            elif len(risk_status["warnings"]) > 0:
                risk_status["status"] = "warning"
            else:
                risk_status["status"] = "healthy"

            # Log summary
            total_checks = risk_status["checks_passed"] + risk_status["checks_failed"]
            self.logger.info(f"Risk limits check completed: {risk_status['status']} "
                           f"({risk_status['checks_passed']}/{total_checks} passed)")

            # Return True if healthy or warning, False if critical or error
            return risk_status["status"] in ["healthy", "warning"]

        except Exception as e:
            self.logger.error(f"Error in check_risk_limits: {e}")
            self.log_risk_event("system_error", f"Risk check failed: {str(e)}", "critical")
            return False
