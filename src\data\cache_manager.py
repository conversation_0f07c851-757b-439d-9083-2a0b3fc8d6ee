"""
Cache Manager for AI Trading Bot System.

This module provides Redis-based caching for market data, news, and
social media data to improve performance and reduce API calls.

Author: inkbytefo
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from decimal import Decimal

try:
    import redis.asyncio as redis
except ImportError:
    import redis
    # Fallback for older redis versions
    redis.asyncio = redis
from ..config.settings import Settings
from ..monitoring.metrics import get_metrics_collector


class CacheManager:
    """
    Redis-based cache manager for trading bot data.
    
    Provides high-performance caching for market data, news articles,
    social media posts, and other frequently accessed data.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Redis connection
        self.redis_client: Optional[redis.Redis] = None
        
        # Cache configuration
        self.cache_config = {
            'market_data': {
                'ttl': 60,  # 1 minute for real-time data
                'prefix': 'market:'
            },
            'ticker_data': {
                'ttl': 30,  # 30 seconds for ticker data
                'prefix': 'ticker:'
            },
            'orderbook_data': {
                'ttl': 10,  # 10 seconds for orderbook
                'prefix': 'orderbook:'
            },
            'news_articles': {
                'ttl': 3600,  # 1 hour for news
                'prefix': 'news:'
            },
            'social_posts': {
                'ttl': 1800,  # 30 minutes for social media
                'prefix': 'social:'
            },
            'user_sessions': {
                'ttl': 86400,  # 24 hours for user sessions
                'prefix': 'session:'
            },
            'api_rate_limits': {
                'ttl': 3600,  # 1 hour for rate limit tracking
                'prefix': 'ratelimit:'
            }
        }
        
        # Statistics
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0,
            'last_reset': datetime.utcnow()
        }
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.logger.info("Initializing Cache Manager...")
            
            # Parse Redis URL
            redis_url = self.settings.cache.url
            
            # Create Redis connection
            self.redis_client = redis.from_url(
                redis_url,
                encoding='utf-8',
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self.logger.info("✅ Cache Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Cache Manager: {e}")
            raise
    
    async def close(self):
        """Close Redis connection."""
        try:
            if self.redis_client:
                await self.redis_client.close()
                self.logger.info("Redis connection closed")
                
        except Exception as e:
            self.logger.error(f"Error closing Redis connection: {e}")
    
    async def get(self, key: str, data_type: str = 'market_data') -> Optional[Any]:
        """Get data from cache."""
        try:
            if not self.redis_client:
                return None
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_key = f"{config['prefix']}{key}"
            
            # Get data from Redis
            cached_data = await self.redis_client.get(full_key)
            
            if cached_data:
                # Parse JSON data
                data = json.loads(cached_data)
                
                # Record cache hit
                self.cache_stats['hits'] += 1
                self.metrics.record_custom_metric("cache_hits", 1)
                
                return data
            else:
                # Record cache miss
                self.cache_stats['misses'] += 1
                self.metrics.record_custom_metric("cache_misses", 1)
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting from cache: {e}")
            self.cache_stats['errors'] += 1
            return None
    
    async def set(self, key: str, data: Any, data_type: str = 'market_data', 
                 ttl: Optional[int] = None) -> bool:
        """Set data in cache."""
        try:
            if not self.redis_client:
                return False
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_key = f"{config['prefix']}{key}"
            
            # Use custom TTL or default from config
            cache_ttl = ttl or config['ttl']
            
            # Serialize data to JSON
            serialized_data = json.dumps(data, default=self._json_serializer)
            
            # Set data in Redis with TTL
            await self.redis_client.setex(full_key, cache_ttl, serialized_data)
            
            # Record cache set
            self.cache_stats['sets'] += 1
            self.metrics.record_custom_metric("cache_sets", 1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting cache: {e}")
            self.cache_stats['errors'] += 1
            return False
    
    async def delete(self, key: str, data_type: str = 'market_data') -> bool:
        """Delete data from cache."""
        try:
            if not self.redis_client:
                return False
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_key = f"{config['prefix']}{key}"
            
            # Delete from Redis
            result = await self.redis_client.delete(full_key)
            
            # Record cache delete
            self.cache_stats['deletes'] += 1
            
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Error deleting from cache: {e}")
            self.cache_stats['errors'] += 1
            return False
    
    async def exists(self, key: str, data_type: str = 'market_data') -> bool:
        """Check if key exists in cache."""
        try:
            if not self.redis_client:
                return False
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_key = f"{config['prefix']}{key}"
            
            # Check existence
            result = await self.redis_client.exists(full_key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Error checking cache existence: {e}")
            return False
    
    async def get_ttl(self, key: str, data_type: str = 'market_data') -> int:
        """Get TTL for a key."""
        try:
            if not self.redis_client:
                return -1
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_key = f"{config['prefix']}{key}"
            
            # Get TTL
            ttl = await self.redis_client.ttl(full_key)
            return ttl
            
        except Exception as e:
            self.logger.error(f"Error getting TTL: {e}")
            return -1
    
    async def increment(self, key: str, data_type: str = 'api_rate_limits', 
                       amount: int = 1, ttl: Optional[int] = None) -> int:
        """Increment a counter in cache."""
        try:
            if not self.redis_client:
                return 0
            
            # Build full key with prefix
            config = self.cache_config.get(data_type, self.cache_config['api_rate_limits'])
            full_key = f"{config['prefix']}{key}"
            
            # Increment counter
            result = await self.redis_client.incr(full_key, amount)
            
            # Set TTL if this is a new key
            if result == amount and ttl:
                await self.redis_client.expire(full_key, ttl)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error incrementing counter: {e}")
            return 0
    
    async def get_pattern(self, pattern: str, data_type: str = 'market_data') -> List[str]:
        """Get keys matching a pattern."""
        try:
            if not self.redis_client:
                return []
            
            # Build full pattern with prefix
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            full_pattern = f"{config['prefix']}{pattern}"
            
            # Get matching keys
            keys = await self.redis_client.keys(full_pattern)
            
            # Remove prefix from keys
            prefix_len = len(config['prefix'])
            return [key[prefix_len:] for key in keys]
            
        except Exception as e:
            self.logger.error(f"Error getting pattern: {e}")
            return []
    
    async def flush_data_type(self, data_type: str) -> bool:
        """Flush all data of a specific type."""
        try:
            if not self.redis_client:
                return False
            
            # Get all keys for this data type
            config = self.cache_config.get(data_type, self.cache_config['market_data'])
            pattern = f"{config['prefix']}*"
            
            keys = await self.redis_client.keys(pattern)
            
            if keys:
                # Delete all keys
                await self.redis_client.delete(*keys)
                self.logger.info(f"Flushed {len(keys)} keys for data type: {data_type}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error flushing data type {data_type}: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check Redis health."""
        try:
            if not self.redis_client:
                return False
            
            # Test ping
            await self.redis_client.ping()
            return True
            
        except Exception as e:
            self.logger.error(f"Redis health check failed: {e}")
            return False
    
    async def get_info(self) -> Dict[str, Any]:
        """Get Redis server information."""
        try:
            if not self.redis_client:
                return {}
            
            info = await self.redis_client.info()
            return info
            
        except Exception as e:
            self.logger.error(f"Error getting Redis info: {e}")
            return {}
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for special types."""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'sets': self.cache_stats['sets'],
            'deletes': self.cache_stats['deletes'],
            'errors': self.cache_stats['errors'],
            'hit_rate': hit_rate,
            'total_requests': total_requests,
            'last_reset': self.cache_stats['last_reset']
        }
    
    def reset_stats(self):
        """Reset cache statistics."""
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0,
            'last_reset': datetime.utcnow()
        }
    
    # Convenience methods for specific data types
    
    async def cache_market_data(self, exchange: str, symbol: str, timeframe: str, 
                               data: Any, ttl: Optional[int] = None) -> bool:
        """Cache market data."""
        key = f"{exchange}:{symbol}:{timeframe}"
        return await self.set(key, data, 'market_data', ttl)
    
    async def get_market_data(self, exchange: str, symbol: str, timeframe: str) -> Optional[Any]:
        """Get cached market data."""
        key = f"{exchange}:{symbol}:{timeframe}"
        return await self.get(key, 'market_data')
    
    async def cache_ticker(self, exchange: str, symbol: str, data: Any) -> bool:
        """Cache ticker data."""
        key = f"{exchange}:{symbol}"
        return await self.set(key, data, 'ticker_data')
    
    async def get_ticker(self, exchange: str, symbol: str) -> Optional[Any]:
        """Get cached ticker data."""
        key = f"{exchange}:{symbol}"
        return await self.get(key, 'ticker_data')
    
    async def cache_orderbook(self, exchange: str, symbol: str, data: Any) -> bool:
        """Cache orderbook data."""
        key = f"{exchange}:{symbol}"
        return await self.set(key, data, 'orderbook_data')
    
    async def get_orderbook(self, exchange: str, symbol: str) -> Optional[Any]:
        """Get cached orderbook data."""
        key = f"{exchange}:{symbol}"
        return await self.get(key, 'orderbook_data')
    
    async def cache_news_article(self, article_id: str, data: Any) -> bool:
        """Cache news article."""
        return await self.set(article_id, data, 'news_articles')
    
    async def get_news_article(self, article_id: str) -> Optional[Any]:
        """Get cached news article."""
        return await self.get(article_id, 'news_articles')
    
    async def cache_social_post(self, platform: str, post_id: str, data: Any) -> bool:
        """Cache social media post."""
        key = f"{platform}:{post_id}"
        return await self.set(key, data, 'social_posts')
    
    async def get_social_post(self, platform: str, post_id: str) -> Optional[Any]:
        """Get cached social media post."""
        key = f"{platform}:{post_id}"
        return await self.get(key, 'social_posts')
    
    async def track_api_rate_limit(self, api_name: str, endpoint: str, 
                                  limit_window: int = 3600) -> int:
        """Track API rate limit usage."""
        key = f"{api_name}:{endpoint}"
        return await self.increment(key, 'api_rate_limits', 1, limit_window)
    
    async def get_api_rate_limit(self, api_name: str, endpoint: str) -> int:
        """Get current API rate limit usage."""
        key = f"{api_name}:{endpoint}"
        cached_count = await self.get(key, 'api_rate_limits')
        return cached_count if cached_count is not None else 0


# Global cache manager instance
cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get the global cache manager instance."""
    global cache_manager
    if cache_manager is None:
        from ..config.settings import Settings
        settings = Settings()
        cache_manager = CacheManager(settings)
    return cache_manager
