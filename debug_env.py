#!/usr/bin/env python3
"""
Debug .env loading.

Author: inkbytefo
"""

import os
from dotenv import load_dotenv

def debug_env():
    """Debug .env loading."""
    print("🔍 Environment Debug")
    print("=" * 30)
    
    # Load .env explicitly
    load_dotenv()
    
    # Check specific variables
    variables = [
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL", 
        "OPENAI_MODEL",
        "OPENAI_TIMEOUT",
        "AI_SENTIMENT_MODEL",
        "AI_PREDICTION_MODEL", 
        "AI_ANALYSIS_MODEL",
        "AI_GENERAL_MODEL"
    ]
    
    for var in variables:
        value = os.getenv(var)
        if value:
            if "API_KEY" in var:
                print(f"✅ {var}: {value[:20]}...")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: NOT FOUND")
    
    print("\n🎉 Environment debug completed!")

if __name__ == "__main__":
    debug_env()
