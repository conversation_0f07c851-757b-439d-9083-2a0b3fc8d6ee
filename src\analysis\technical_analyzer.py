"""
Technical Analysis Module for AI Trading Bot System.

This module provides technical analysis capabilities including
various indicators and pattern recognition.

Author: inkbytefo
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

from ..config.settings import Settings


class TechnicalAnalyzer:
    """
    Technical analysis engine for cryptocurrency trading.
    
    Provides various technical indicators and analysis tools
    for making trading decisions.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Analysis cache
        self.analysis_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def analyze_symbol(self, symbol: str, timeframe: str, 
                           ohlcv_data: List[List]) -> Dict[str, Any]:
        """
        Perform comprehensive technical analysis on a symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTC/USDT')
            timeframe: Timeframe for analysis (e.g., '1h', '4h', '1d')
            ohlcv_data: OHLCV data as list of [timestamp, open, high, low, close, volume]
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            if not ohlcv_data or len(ohlcv_data) < 20:
                return {"error": "Insufficient data for analysis"}
            
            # Convert to DataFrame
            df = self._prepare_dataframe(ohlcv_data)
            
            # Calculate indicators
            indicators = self._calculate_indicators(df)
            
            # Analyze trends
            trend_analysis = self._analyze_trends(df, indicators)
            
            # Generate signals
            signals = self._generate_signals(df, indicators, trend_analysis)
            
            # Calculate support/resistance levels
            levels = self._calculate_support_resistance(df)
            
            analysis_result = {
                "symbol": symbol,
                "timeframe": timeframe,
                "timestamp": datetime.utcnow().isoformat(),
                "current_price": float(df['close'].iloc[-1]),
                "indicators": indicators,
                "trend": trend_analysis,
                "signals": signals,
                "levels": levels,
                "volume_analysis": self._analyze_volume(df),
                "volatility": self._calculate_volatility(df)
            }
            
            # Cache the result
            cache_key = f"{symbol}_{timeframe}"
            self.analysis_cache[cache_key] = {
                "data": analysis_result,
                "timestamp": datetime.utcnow()
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Technical analysis failed for {symbol}: {e}")
            return {"error": str(e)}
    
    def _prepare_dataframe(self, ohlcv_data: List[List]) -> pd.DataFrame:
        """Convert OHLCV data to pandas DataFrame."""
        df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        # Convert to float
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)
            
        return df
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate various technical indicators."""
        indicators = {}
        
        # Moving Averages
        indicators['sma_20'] = df['close'].rolling(window=20).mean().iloc[-1]
        indicators['sma_50'] = df['close'].rolling(window=50).mean().iloc[-1] if len(df) >= 50 else None
        indicators['ema_12'] = df['close'].ewm(span=12).mean().iloc[-1]
        indicators['ema_26'] = df['close'].ewm(span=26).mean().iloc[-1]
        
        # RSI
        indicators['rsi'] = self._calculate_rsi(df['close'])
        
        # MACD
        macd_data = self._calculate_macd(df['close'])
        indicators.update(macd_data)
        
        # Bollinger Bands
        bb_data = self._calculate_bollinger_bands(df['close'])
        indicators.update(bb_data)
        
        # Stochastic
        stoch_data = self._calculate_stochastic(df)
        indicators.update(stoch_data)
        
        return indicators
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi.iloc[-1])
    
    def _calculate_macd(self, prices: pd.Series) -> Dict[str, float]:
        """Calculate MACD indicator."""
        ema_12 = prices.ewm(span=12).mean()
        ema_26 = prices.ewm(span=26).mean()
        macd_line = ema_12 - ema_26
        signal_line = macd_line.ewm(span=9).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd': float(macd_line.iloc[-1]),
            'macd_signal': float(signal_line.iloc[-1]),
            'macd_histogram': float(histogram.iloc[-1])
        }
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
        """Calculate Bollinger Bands."""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        return {
            'bb_upper': float(sma.iloc[-1] + (std.iloc[-1] * std_dev)),
            'bb_middle': float(sma.iloc[-1]),
            'bb_lower': float(sma.iloc[-1] - (std.iloc[-1] * std_dev))
        }
    
    def _calculate_stochastic(self, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Dict[str, float]:
        """Calculate Stochastic Oscillator."""
        low_min = df['low'].rolling(window=k_period).min()
        high_max = df['high'].rolling(window=k_period).max()
        k_percent = 100 * ((df['close'] - low_min) / (high_max - low_min))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            'stoch_k': float(k_percent.iloc[-1]),
            'stoch_d': float(d_percent.iloc[-1])
        }
    
    def _analyze_trends(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze price trends."""
        current_price = df['close'].iloc[-1]
        
        # Short-term trend (based on EMA)
        short_trend = "bullish" if current_price > indicators['ema_12'] else "bearish"
        
        # Medium-term trend (based on SMA)
        medium_trend = "bullish" if current_price > indicators['sma_20'] else "bearish"
        
        # Long-term trend (if we have enough data)
        long_trend = "neutral"
        if indicators['sma_50'] is not None:
            long_trend = "bullish" if current_price > indicators['sma_50'] else "bearish"
        
        # Overall trend strength
        trend_strength = self._calculate_trend_strength(df)
        
        return {
            "short_term": short_trend,
            "medium_term": medium_trend,
            "long_term": long_trend,
            "strength": trend_strength,
            "direction": self._get_overall_direction(short_trend, medium_trend, long_trend)
        }
    
    def _calculate_trend_strength(self, df: pd.DataFrame) -> str:
        """Calculate trend strength based on price action."""
        if len(df) < 10:
            return "weak"
        
        recent_prices = df['close'].tail(10)
        price_change = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
        
        if abs(price_change) > 0.05:  # 5% change
            return "strong"
        elif abs(price_change) > 0.02:  # 2% change
            return "moderate"
        else:
            return "weak"
    
    def _get_overall_direction(self, short: str, medium: str, long: str) -> str:
        """Determine overall trend direction."""
        bullish_count = sum([1 for trend in [short, medium, long] if trend == "bullish"])
        bearish_count = sum([1 for trend in [short, medium, long] if trend == "bearish"])
        
        if bullish_count >= 2:
            return "bullish"
        elif bearish_count >= 2:
            return "bearish"
        else:
            return "neutral"
    
    def _generate_signals(self, df: pd.DataFrame, indicators: Dict[str, Any], 
                         trend: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading signals based on technical analysis."""
        signals = {
            "overall": "neutral",
            "strength": 0.0,
            "reasons": []
        }
        
        score = 0
        reasons = []
        
        # RSI signals
        rsi = indicators['rsi']
        if rsi < 30:
            score += 2
            reasons.append("RSI oversold")
        elif rsi > 70:
            score -= 2
            reasons.append("RSI overbought")
        
        # MACD signals
        if indicators['macd'] > indicators['macd_signal']:
            score += 1
            reasons.append("MACD bullish")
        else:
            score -= 1
            reasons.append("MACD bearish")
        
        # Trend signals
        if trend['direction'] == 'bullish':
            score += 2
            reasons.append("Bullish trend")
        elif trend['direction'] == 'bearish':
            score -= 2
            reasons.append("Bearish trend")
        
        # Bollinger Bands signals
        current_price = df['close'].iloc[-1]
        if current_price < indicators['bb_lower']:
            score += 1
            reasons.append("Price below BB lower")
        elif current_price > indicators['bb_upper']:
            score -= 1
            reasons.append("Price above BB upper")
        
        # Determine overall signal
        if score >= 3:
            signals["overall"] = "buy"
        elif score <= -3:
            signals["overall"] = "sell"
        else:
            signals["overall"] = "neutral"
        
        signals["strength"] = min(abs(score) / 5.0, 1.0)  # Normalize to 0-1
        signals["reasons"] = reasons
        
        return signals
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, List[float]]:
        """Calculate support and resistance levels."""
        if len(df) < 20:
            return {"support": [], "resistance": []}
        
        # Simple pivot point calculation
        highs = df['high'].rolling(window=5, center=True).max()
        lows = df['low'].rolling(window=5, center=True).min()
        
        resistance_levels = []
        support_levels = []
        
        for i in range(2, len(df) - 2):
            if df['high'].iloc[i] == highs.iloc[i]:
                resistance_levels.append(float(df['high'].iloc[i]))
            if df['low'].iloc[i] == lows.iloc[i]:
                support_levels.append(float(df['low'].iloc[i]))
        
        # Keep only recent and significant levels
        current_price = df['close'].iloc[-1]
        
        # Filter levels within reasonable range
        resistance_levels = [r for r in resistance_levels if current_price * 0.95 <= r <= current_price * 1.1]
        support_levels = [s for s in support_levels if current_price * 0.9 <= s <= current_price * 1.05]
        
        return {
            "support": sorted(list(set(support_levels)), reverse=True)[:3],
            "resistance": sorted(list(set(resistance_levels)))[:3]
        }
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns."""
        if len(df) < 20:
            return {"trend": "neutral", "strength": "weak"}
        
        recent_volume = df['volume'].tail(5).mean()
        avg_volume = df['volume'].tail(20).mean()
        
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio > 1.5:
            strength = "high"
        elif volume_ratio > 1.2:
            strength = "moderate"
        else:
            strength = "low"
        
        # Volume trend
        volume_trend = "increasing" if recent_volume > avg_volume else "decreasing"
        
        return {
            "trend": volume_trend,
            "strength": strength,
            "ratio": float(volume_ratio)
        }
    
    def _calculate_volatility(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate price volatility metrics."""
        if len(df) < 20:
            return {"daily": 0.0, "weekly": 0.0}
        
        # Daily volatility (standard deviation of returns)
        returns = df['close'].pct_change().dropna()
        daily_vol = float(returns.std() * np.sqrt(24))  # Assuming hourly data
        
        # Weekly volatility
        weekly_vol = float(returns.std() * np.sqrt(24 * 7))
        
        return {
            "daily": daily_vol,
            "weekly": weekly_vol
        }
    
    def get_cached_analysis(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Get cached analysis if available and not expired."""
        cache_key = f"{symbol}_{timeframe}"
        
        if cache_key in self.analysis_cache:
            cached_data = self.analysis_cache[cache_key]
            age = (datetime.utcnow() - cached_data["timestamp"]).total_seconds()
            
            if age < self.cache_ttl:
                return cached_data["data"]
            else:
                # Remove expired cache
                del self.analysis_cache[cache_key]
        
        return None
    
    def clear_cache(self):
        """Clear analysis cache."""
        self.analysis_cache.clear()
        self.logger.info("Technical analysis cache cleared")

    async def analyze(self, symbol: str, timeframe: str = "1h",
                     ohlcv_data: List[List] = None) -> Dict[str, Any]:
        """
        Main analysis method - wrapper for analyze_symbol.

        Args:
            symbol: Trading pair symbol
            timeframe: Analysis timeframe (default: 1h)
            ohlcv_data: Optional OHLCV data, if None will fetch from exchange

        Returns:
            Dict containing technical analysis results
        """
        try:
            if ohlcv_data is None:
                # If no data provided, try to get from exchange manager
                # This is a fallback - normally data should be provided
                self.logger.warning(f"No OHLCV data provided for {symbol}, returning basic analysis")
                return {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "timestamp": pd.Timestamp.now().isoformat(),
                    "indicators": {},
                    "signals": {"overall": "neutral", "strength": "weak"},
                    "trends": {"short_term": "neutral", "medium_term": "neutral", "long_term": "neutral"},
                    "support_resistance": {"support": [], "resistance": []},
                    "volume_analysis": {"trend": "neutral", "strength": "weak"},
                    "error": "No OHLCV data available"
                }

            # Use the existing analyze_symbol method
            return await self.analyze_symbol(symbol, timeframe, ohlcv_data)

        except Exception as e:
            self.logger.error(f"Analysis failed for {symbol}: {e}")
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "timestamp": pd.Timestamp.now().isoformat(),
                "indicators": {},
                "signals": {"overall": "neutral", "strength": "weak"},
                "trends": {"short_term": "neutral", "medium_term": "neutral", "long_term": "neutral"},
                "support_resistance": {"support": [], "resistance": []},
                "volume_analysis": {"trend": "neutral", "strength": "weak"},
                "error": str(e)
            }
