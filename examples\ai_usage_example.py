#!/usr/bin/env python3
"""
AI Usage Example for Trading Bot.

This example demonstrates how to use the AI system with different providers
for sentiment analysis, price prediction, and market analysis.

Author: inkbytefo
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.settings import Settings
from src.ai import get_ai_service, get_provider_manager
from src.ai.providers import ProviderFactory, ProviderType


async def main():
    """Main example function."""
    print("🤖 AI Trading Bot - Usage Example")
    print("=" * 50)
    
    # Initialize settings
    settings = Settings()
    
    # Example 1: Provider Manager Usage
    print("\n📋 Example 1: Provider Manager")
    print("-" * 30)
    
    provider_manager = get_provider_manager()
    
    # Add a mock local provider for demonstration
    mock_provider = ProviderFactory.create_local_provider(
        base_url="http://localhost:11434",
        settings=settings
    )
    provider_manager.providers['mock_local'] = mock_provider
    
    print(f"Available providers: {provider_manager.get_available_providers()}")
    print(f"Routing strategy: {provider_manager.routing_strategy.value}")
    
    # Example 2: AI Service Usage
    print("\n📋 Example 2: AI Service")
    print("-" * 30)
    
    ai_service = get_ai_service()
    
    # Note: These will fail without real API keys, but show the interface
    try:
        await ai_service.initialize()
        
        # Sentiment Analysis Example
        print("\n🔍 Sentiment Analysis Example:")
        news_text = "Bitcoin reaches new all-time high as institutional adoption increases"
        
        # This will fail without real API keys, but shows the interface
        try:
            sentiment = await ai_service.analyze_sentiment(news_text, context="news")
            print(f"Text: {news_text}")
            print(f"Sentiment: {sentiment.sentiment}")
            print(f"Confidence: {sentiment.confidence}")
            print(f"Score: {sentiment.score}")
        except Exception as e:
            print(f"Sentiment analysis failed (expected without API keys): {e}")
        
        # Price Prediction Example
        print("\n📈 Price Prediction Example:")
        try:
            prediction = await ai_service.predict_price("BTC/USDT", "1h")
            print(f"Symbol: {prediction.symbol}")
            print(f"Direction: {prediction.direction}")
            print(f"Confidence: {prediction.confidence}")
        except Exception as e:
            print(f"Price prediction failed (expected without API keys): {e}")
        
        # Market Analysis Example
        print("\n📊 Market Analysis Example:")
        try:
            analysis = await ai_service.analyze_market(["BTC/USDT", "ETH/USDT"])
            print(f"Market condition: {analysis.market_condition}")
            print(f"Trend strength: {analysis.trend_strength}")
            print(f"Volatility: {analysis.volatility_level}")
        except Exception as e:
            print(f"Market analysis failed (expected without API keys): {e}")
        
        await ai_service.close()
        
    except Exception as e:
        print(f"AI service initialization failed (expected without API keys): {e}")
    
    # Example 3: Configuration Examples
    print("\n📋 Example 3: Configuration")
    print("-" * 30)
    
    print("To use real AI providers, add these to your .env file:")
    print()
    print("# OpenAI")
    print("OPENAI_API_KEY=sk-your-openai-key-here")
    print("OPENAI_MODEL=gpt-3.5-turbo")
    print()
    print("# Local AI (Ollama)")
    print("LOCAL_AI_URL=http://localhost:11434")
    print("LOCAL_AI_MODEL=llama2:7b")
    print()
    print("# Azure OpenAI")
    print("AZURE_OPENAI_KEY=your-azure-key")
    print("AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/")
    print()
    
    # Example 4: Provider Statistics
    print("\n📋 Example 4: Statistics")
    print("-" * 30)
    
    try:
        stats = provider_manager.get_provider_statistics()
        print(f"Total requests: {stats['total_requests']}")
        print(f"Success rate: {stats['success_rate']}%")
        print(f"Routing strategy: {stats['routing_strategy']}")
        print(f"Available providers: {len(stats['providers'])}")
    except Exception as e:
        print(f"Statistics error: {e}")
    
    print("\n✅ Example completed!")
    print("\n📝 Next steps:")
    print("1. Add real API keys to .env file")
    print("2. Test with actual providers")
    print("3. Integrate with trading strategies")
    print("4. Monitor costs and performance")


if __name__ == "__main__":
    asyncio.run(main())
