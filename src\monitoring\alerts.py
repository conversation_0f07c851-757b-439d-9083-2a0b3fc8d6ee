"""
Alert system for AI Trading Bot System.

This module provides comprehensive alerting capabilities including
email, Slack, Discord notifications for critical events and thresholds.

Author: inkbytefo
"""

import asyncio
import logging
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from enum import Enum

import aiohttp
from ..config.settings import Settings


class AlertLevel(Enum):
    """Alert severity levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class AlertType(Enum):
    """Types of alerts."""
    SYSTEM = "system"
    TRADING = "trading"
    RISK = "risk"
    AI = "ai"
    PERFORMANCE = "performance"


@dataclass
class Alert:
    """Alert data structure."""
    id: str
    level: AlertLevel
    alert_type: AlertType
    title: str
    message: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    acknowledged: bool = False
    resolved: bool = False


class AlertManager:
    """
    Comprehensive alert management system.
    
    Handles alert generation, routing, and delivery through multiple channels
    including email, Slack, Discord, and webhook notifications.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Alert storage and rate limiting
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: List[Alert] = []
        self._rate_limits: Dict[str, datetime] = {}
        
        # Configuration
        self.rate_limit_seconds = 300  # Default rate limit in seconds
        self.max_history_size = 1000
        
        # Initialize notification channels
        self._init_notification_channels()
    
    def _init_notification_channels(self):
        """Initialize notification channels based on configuration."""
        self.notification_channels = {
            "email": self._send_email_alert,
            "slack": self._send_slack_alert,
            "discord": self._send_discord_alert,
            "webhook": self._send_webhook_alert
        }
        
        # Check which channels are enabled
        self.enabled_channels = []
        
        if self.settings.monitoring.alert_email:
            self.enabled_channels.append("email")
        
        if (self.settings.monitoring.slack_webhook_url):
            self.enabled_channels.append("slack")
        
        if (self.settings.monitoring.discord_webhook_url):
            self.enabled_channels.append("discord")
        
        self.logger.info(f"Enabled alert channels: {self.enabled_channels}")
    
    async def send_alert(self, level: AlertLevel, alert_type: AlertType, 
                        title: str, message: str, metadata: Dict[str, Any] = None) -> str:
        """
        Send an alert through configured channels.
        
        Args:
            level: Alert severity level
            alert_type: Type of alert
            title: Alert title
            message: Alert message
            metadata: Additional metadata
            
        Returns:
            Alert ID
        """
        try:
            # Create alert
            alert_id = f"{alert_type.value}_{int(datetime.utcnow().timestamp())}"
            alert = Alert(
                id=alert_id,
                level=level,
                alert_type=alert_type,
                title=title,
                message=message,
                timestamp=datetime.utcnow(),
                metadata=metadata or {}
            )
            
            # Check rate limiting
            if self._is_rate_limited(alert):
                self.logger.debug(f"Alert rate limited: {alert_id}")
                return alert_id
            
            # Store alert
            self._active_alerts[alert_id] = alert
            self._alert_history.append(alert)
            
            # Trim history if needed
            if len(self._alert_history) > self.max_history_size:
                self._alert_history = self._alert_history[-self.max_history_size:]
            
            # Send through enabled channels
            await self._route_alert(alert)
            
            self.logger.info(f"Alert sent: {alert_id} - {title}")
            return alert_id
            
        except Exception as e:
            self.logger.error(f"Failed to send alert: {e}")
            return ""
    
    def _is_rate_limited(self, alert: Alert) -> bool:
        """Check if alert is rate limited."""
        rate_key = f"{alert.alert_type.value}_{alert.title}"
        
        if rate_key in self._rate_limits:
            time_diff = (datetime.utcnow() - self._rate_limits[rate_key]).total_seconds()
            if time_diff < self.rate_limit_seconds:
                return True
        
        self._rate_limits[rate_key] = datetime.utcnow()
        return False
    
    async def _route_alert(self, alert: Alert):
        """Route alert to appropriate channels based on level and type."""
        try:
            # Determine which channels to use based on alert level
            channels_to_use = []
            
            if alert.level in [AlertLevel.CRITICAL, AlertLevel.WARNING]:
                channels_to_use = self.enabled_channels
            elif alert.level == AlertLevel.INFO:
                # Only send info alerts to less intrusive channels
                channels_to_use = [ch for ch in self.enabled_channels if ch != "email"]
            
            # Send to selected channels
            tasks = []
            for channel in channels_to_use:
                if channel in self.notification_channels:
                    task = self.notification_channels[channel](alert)
                    tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            self.logger.error(f"Failed to route alert {alert.id}: {e}")
    
    async def _send_email_alert(self, alert: Alert):
        """Send alert via email."""
        try:
            email_config = self.settings.monitoring.alert_email
            if not email_config or not email_config.get("enabled", False):
                return
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = email_config.get("from_email", "<EMAIL>")
            msg['Subject'] = f"[{alert.level.value.upper()}] {alert.title}"
            
            # Email body
            body = f"""
Alert Details:
--------------
Level: {alert.level.value.upper()}
Type: {alert.alert_type.value}
Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}
Title: {alert.title}

Message:
{alert.message}

Metadata:
{alert.metadata if alert.metadata else 'None'}

---
AI Trading Bot System
            """.strip()
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(
                email_config.get("smtp_server", "localhost"),
                email_config.get("smtp_port", 587)
            )
            
            if email_config.get("use_tls", True):
                server.starttls()
            
            if email_config.get("username") and email_config.get("password"):
                server.login(email_config["username"], email_config["password"])
            
            recipients = email_config.get("recipients", [])
            for recipient in recipients:
                msg['To'] = recipient
                server.send_message(msg)
                del msg['To']
            
            server.quit()
            self.logger.debug(f"Email alert sent: {alert.id}")
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
    
    async def _send_slack_alert(self, alert: Alert):
        """Send alert via Slack webhook."""
        try:
            webhook_url = self.settings.monitoring.slack_webhook_url
            if not webhook_url:
                return
            
            # Determine color based on alert level
            color_map = {
                AlertLevel.DEBUG: "#36a64f",      # Green
                AlertLevel.INFO: "#36a64f",       # Green
                AlertLevel.WARNING: "#ff9500",    # Orange
                AlertLevel.CRITICAL: "#ff0000"    # Red
            }
            
            # Create Slack message
            payload = {
                "attachments": [{
                    "color": color_map.get(alert.level, "#36a64f"),
                    "title": f"{alert.level.value.upper()}: {alert.title}",
                    "text": alert.message,
                    "fields": [
                        {
                            "title": "Type",
                            "value": alert.alert_type.value,
                            "short": True
                        },
                        {
                            "title": "Time",
                            "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC'),
                            "short": True
                        }
                    ],
                    "footer": "AI Trading Bot",
                    "ts": int(alert.timestamp.timestamp())
                }]
            }
            
            # Add metadata if present
            if alert.metadata:
                payload["attachments"][0]["fields"].append({
                    "title": "Metadata",
                    "value": str(alert.metadata),
                    "short": False
                })
            
            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        self.logger.debug(f"Slack alert sent: {alert.id}")
                    else:
                        self.logger.error(f"Slack webhook failed: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Failed to send Slack alert: {e}")
    
    async def _send_discord_alert(self, alert: Alert):
        """Send alert via Discord webhook."""
        try:
            webhook_url = self.settings.monitoring.discord_webhook_url
            if not webhook_url:
                return
            
            # Determine color based on alert level
            color_map = {
                AlertLevel.DEBUG: 0x36a64f,      # Green
                AlertLevel.INFO: 0x36a64f,       # Green
                AlertLevel.WARNING: 0xff9500,    # Orange
                AlertLevel.CRITICAL: 0xff0000    # Red
            }
            
            # Create Discord embed
            embed = {
                "title": f"{alert.level.value.upper()}: {alert.title}",
                "description": alert.message,
                "color": color_map.get(alert.level, 0x36a64f),
                "timestamp": alert.timestamp.isoformat(),
                "fields": [
                    {
                        "name": "Type",
                        "value": alert.alert_type.value,
                        "inline": True
                    }
                ],
                "footer": {
                    "text": "AI Trading Bot"
                }
            }
            
            # Add metadata if present
            if alert.metadata:
                embed["fields"].append({
                    "name": "Metadata",
                    "value": f"```json\n{alert.metadata}\n```",
                    "inline": False
                })
            
            payload = {"embeds": [embed]}
            
            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 204:
                        self.logger.debug(f"Discord alert sent: {alert.id}")
                    else:
                        self.logger.error(f"Discord webhook failed: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Failed to send Discord alert: {e}")
    
    async def _send_webhook_alert(self, alert: Alert):
        """Send alert via custom webhook."""
        try:
            # This would be implemented for custom webhook endpoints
            # For now, just log the alert
            self.logger.debug(f"Webhook alert (not implemented): {alert.id}")
            
        except Exception as e:
            self.logger.error(f"Failed to send webhook alert: {e}")
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert."""
        if alert_id in self._active_alerts:
            self._active_alerts[alert_id].acknowledged = True
            self.logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an alert."""
        if alert_id in self._active_alerts:
            self._active_alerts[alert_id].resolved = True
            self.logger.info(f"Alert resolved: {alert_id}")
            return True
        return False
    
    def get_active_alerts(self, level: Optional[AlertLevel] = None, 
                         alert_type: Optional[AlertType] = None) -> List[Alert]:
        """Get active alerts with optional filtering."""
        alerts = [
            alert for alert in self._active_alerts.values()
            if not alert.resolved
        ]
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        if alert_type:
            alerts = [alert for alert in alerts if alert.alert_type == alert_type]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """Get alert history for the specified time period."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return [
            alert for alert in self._alert_history
            if alert.timestamp >= cutoff_time
        ]
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """Get alert statistics."""
        active_alerts = self.get_active_alerts()
        recent_alerts = self.get_alert_history(24)
        
        stats = {
            "active_alerts": len(active_alerts),
            "recent_alerts_24h": len(recent_alerts),
            "by_level": {},
            "by_type": {},
            "acknowledged": len([a for a in active_alerts if a.acknowledged]),
            "unacknowledged": len([a for a in active_alerts if not a.acknowledged])
        }
        
        # Count by level
        for level in AlertLevel:
            stats["by_level"][level.value] = len([
                a for a in recent_alerts if a.level == level
            ])
        
        # Count by type
        for alert_type in AlertType:
            stats["by_type"][alert_type.value] = len([
                a for a in recent_alerts if a.alert_type == alert_type
            ])
        
        return stats


# Convenience functions for common alerts
async def send_system_alert(level: AlertLevel, title: str, message: str, 
                           metadata: Dict[str, Any] = None):
    """Send a system alert."""
    from ..config.settings import Settings
    settings = Settings()
    alert_manager = AlertManager(settings)
    
    return await alert_manager.send_alert(
        level=level,
        alert_type=AlertType.SYSTEM,
        title=title,
        message=message,
        metadata=metadata
    )


async def send_trading_alert(level: AlertLevel, title: str, message: str, 
                            metadata: Dict[str, Any] = None):
    """Send a trading alert."""
    from ..config.settings import Settings
    settings = Settings()
    alert_manager = AlertManager(settings)
    
    return await alert_manager.send_alert(
        level=level,
        alert_type=AlertType.TRADING,
        title=title,
        message=message,
        metadata=metadata
    )


async def send_risk_alert(level: AlertLevel, title: str, message: str, 
                         metadata: Dict[str, Any] = None):
    """Send a risk management alert."""
    from ..config.settings import Settings
    settings = Settings()
    alert_manager = AlertManager(settings)
    
    return await alert_manager.send_alert(
        level=level,
        alert_type=AlertType.RISK,
        title=title,
        message=message,
        metadata=metadata
    )


# Global alert manager instance
alert_manager: Optional[AlertManager] = None


def get_alert_manager() -> AlertManager:
    """Get the global alert manager instance."""
    global alert_manager
    if alert_manager is None:
        from ..config.settings import Settings
        settings = Settings()
        alert_manager = AlertManager(settings)
    return alert_manager
