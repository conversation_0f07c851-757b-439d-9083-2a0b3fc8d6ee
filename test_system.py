#!/usr/bin/env python3
"""
Comprehensive System Test for AI Trading Bot.

This script tests all major components of the trading bot system
to ensure everything is working correctly before production use.

Author: inkbytefo
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.config.database import get_db_manager
from src.exchanges.exchange_manager import ExchangeManager
from src.data.data_service import DataService
from src.data.cache_manager import CacheManager
from src.monitoring.metrics import get_metrics_collector
from src.monitoring.alerts import get_alert_manager


class SystemTester:
    """Comprehensive system tester."""
    
    def __init__(self):
        self.settings = Settings()
        self.test_results = {}
        self.failed_tests = []
        
    async def run_all_tests(self):
        """Run all system tests."""
        print("🚀 Starting Comprehensive System Test")
        print("=" * 60)
        
        tests = [
            ("Settings Configuration", self.test_settings),
            ("Database Connection", self.test_database),
            ("Redis Cache", self.test_redis_cache),
            ("Exchange Manager", self.test_exchange_manager),
            ("Metrics Collection", self.test_metrics),
            ("Alert System", self.test_alerts),
            ("Data Service", self.test_data_service),
            ("System Integration", self.test_integration)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 Testing: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = result
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
                    self.failed_tests.append(test_name)
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
                self.failed_tests.append(test_name)
        
        # Print summary
        self.print_summary()
        
        return len(self.failed_tests) == 0
    
    async def test_settings(self):
        """Test settings configuration."""
        try:
            # Test basic settings loading
            assert self.settings.app_name == "AI Trading Bot"
            assert self.settings.environment in ["development", "production", "testing"]
            
            # Test database settings
            assert self.settings.database.url.startswith("postgresql://")
            
            # Test Redis settings
            assert self.settings.redis.url.startswith("redis://")
            
            print("  ✓ Settings loaded correctly")
            print(f"  ✓ Environment: {self.settings.environment}")
            print(f"  ✓ Database URL configured")
            print(f"  ✓ Redis URL configured")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Settings error: {e}")
            return False
    
    async def test_database(self):
        """Test database connection and operations."""
        try:
            db_manager = get_db_manager()
            
            # Initialize database
            db_manager.initialize()
            
            # Test health check
            health = db_manager.health_check()
            assert health, "Database health check failed"
            
            print("  ✓ Database connection successful")
            print("  ✓ Database health check passed")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Database error: {e}")
            return False
    
    async def test_redis_cache(self):
        """Test Redis cache operations."""
        try:
            cache_manager = CacheManager(self.settings)
            
            # Initialize cache
            await cache_manager.initialize()
            
            # Test health check
            health = await cache_manager.health_check()
            assert health, "Redis health check failed"
            
            # Test basic operations
            test_key = "system_test"
            test_data = {"test": "data", "timestamp": datetime.utcnow().isoformat()}
            
            # Set data
            success = await cache_manager.set(test_key, test_data)
            assert success, "Cache set operation failed"
            
            # Get data
            retrieved_data = await cache_manager.get(test_key)
            assert retrieved_data is not None, "Cache get operation failed"
            assert retrieved_data["test"] == "data", "Cache data mismatch"
            
            # Clean up
            await cache_manager.delete(test_key)
            await cache_manager.close()
            
            print("  ✓ Redis connection successful")
            print("  ✓ Cache operations working")
            print("  ✓ Data integrity verified")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Redis cache error: {e}")
            return False
    
    async def test_exchange_manager(self):
        """Test exchange manager functionality."""
        try:
            exchange_manager = ExchangeManager(self.settings)
            
            # Initialize exchange manager
            await exchange_manager.initialize()
            
            # Test supported exchanges
            supported = exchange_manager.get_supported_exchanges()
            assert len(supported) > 0, "No supported exchanges found"
            
            # Test active exchanges (may be empty without API keys)
            active = exchange_manager.get_active_exchanges()
            
            # Test health check
            health = await exchange_manager.health_check()
            # Health check may fail without API keys, that's OK
            
            await exchange_manager.close()
            
            print(f"  ✓ Supported exchanges: {supported}")
            print(f"  ✓ Active exchanges: {active}")
            print("  ✓ Exchange manager initialized")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Exchange manager error: {e}")
            return False
    
    async def test_metrics(self):
        """Test metrics collection system."""
        try:
            metrics = get_metrics_collector()
            
            # Test metric recording
            metrics.record_custom_metric("test_metric", 42.0)
            metrics.record_system_metric("cpu_usage", 25.5)
            
            # Test metric retrieval
            custom_metrics = metrics.get_custom_metrics()
            system_metrics = metrics.get_system_metrics()
            
            print("  ✓ Metrics collection working")
            print(f"  ✓ Custom metrics: {len(custom_metrics)}")
            print(f"  ✓ System metrics: {len(system_metrics)}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Metrics error: {e}")
            return False
    
    async def test_alerts(self):
        """Test alert system."""
        try:
            alert_manager = get_alert_manager()
            
            # Test alert creation (won't actually send without proper config)
            from src.monitoring.alerts import AlertLevel, AlertType
            
            # This will create an alert but not send it (no notification config)
            await alert_manager.send_alert(
                level=AlertLevel.INFO,
                alert_type=AlertType.SYSTEM,
                title="System Test Alert",
                message="This is a test alert from system testing",
                metadata={"test": True}
            )
            
            print("  ✓ Alert system initialized")
            print("  ✓ Alert creation working")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Alert system error: {e}")
            return False
    
    async def test_data_service(self):
        """Test data service integration."""
        try:
            exchange_manager = ExchangeManager(self.settings)
            data_service = DataService(self.settings, exchange_manager)
            
            # Initialize data service
            await data_service.initialize()
            
            # Test service status
            status = data_service.get_service_status()
            assert status["components_initialized"], "Data service components not initialized"
            
            # Test health check
            health = await data_service.health_check()
            # Health may be partial without full setup
            
            # Test data summary
            summary = data_service.get_data_summary()
            assert "timestamp" in summary, "Data summary missing timestamp"
            
            print("  ✓ Data service initialized")
            print("  ✓ Component status checked")
            print("  ✓ Health check completed")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Data service error: {e}")
            return False
    
    async def test_integration(self):
        """Test system integration."""
        try:
            # Test that all components can work together
            db_manager = get_db_manager()
            cache_manager = CacheManager(self.settings)
            exchange_manager = ExchangeManager(self.settings)
            
            # Initialize all components
            db_manager.initialize()
            await cache_manager.initialize()
            await exchange_manager.initialize()
            
            # Test cross-component functionality
            # Store some test data in cache
            test_data = {
                "integration_test": True,
                "timestamp": datetime.utcnow().isoformat(),
                "components": ["database", "cache", "exchange"]
            }
            
            await cache_manager.set("integration_test", test_data)
            retrieved = await cache_manager.get("integration_test")
            
            assert retrieved is not None, "Integration test data not retrieved"
            assert retrieved["integration_test"] is True, "Integration test data corrupted"
            
            # Clean up
            await cache_manager.delete("integration_test")
            await cache_manager.close()
            await exchange_manager.close()
            
            print("  ✓ Component integration working")
            print("  ✓ Cross-component data flow verified")
            print("  ✓ System cleanup successful")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Integration error: {e}")
            return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in self.failed_tests:
                print(f"  - {test}")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! System is ready for Faz 3!")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please review before proceeding.")
        
        print("=" * 60)


async def main():
    """Main test runner."""
    tester = SystemTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 System is ready for production!")
        return 0
    else:
        print("\n⚠️  System has issues that need to be resolved.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
