#!/usr/bin/env python3
"""
Development helper script for AI Trading Bot System.

This script provides convenient commands for development tasks like
starting services, running tests, and managing the development environment.

Author: inkbytefo
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def run_command(command, description, check=True):
    """Run a shell command with description."""
    print(f"📋 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check)
        if result.returncode == 0:
            print(f"✅ {description} completed")
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False


def start_services():
    """Start all services with Docker Compose."""
    print("🚀 Starting AI Trading Bot services...")
    
    # Build and start services
    commands = [
        ("docker-compose build", "Building Docker images"),
        ("docker-compose up -d", "Starting services"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    # Wait for services to be ready
    print("⏳ Waiting for services to be ready...")
    time.sleep(10)
    
    # Check service health
    return check_services()


def stop_services():
    """Stop all services."""
    print("🛑 Stopping AI Trading Bot services...")
    return run_command("docker-compose down", "Stopping services")


def restart_services():
    """Restart all services."""
    print("🔄 Restarting AI Trading Bot services...")
    stop_services()
    return start_services()


def check_services():
    """Check if all services are running."""
    print("🔍 Checking service health...")
    
    services = [
        ("postgres", "Database"),
        ("redis", "Cache"),
        ("trading_bot", "Trading Bot"),
        ("prometheus", "Metrics"),
        ("grafana", "Monitoring")
    ]
    
    all_healthy = True
    
    for service, name in services:
        result = subprocess.run(
            f"docker-compose ps {service} | grep 'Up'",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ {name} is running")
        else:
            print(f"❌ {name} is not running")
            all_healthy = False
    
    return all_healthy


def view_logs(service=None, follow=False):
    """View service logs."""
    if service:
        command = f"docker-compose logs {'--follow' if follow else ''} {service}"
        description = f"Viewing logs for {service}"
    else:
        command = f"docker-compose logs {'--follow' if follow else ''}"
        description = "Viewing all service logs"
    
    print(f"📋 {description}...")
    subprocess.run(command, shell=True)


def run_tests():
    """Run the test suite."""
    print("🧪 Running tests...")
    
    # Run tests inside the container
    commands = [
        ("docker-compose exec trading_bot python -m pytest tests/ -v", "Running unit tests"),
        ("docker-compose exec trading_bot python -m pytest tests/integration/ -v", "Running integration tests"),
    ]
    
    all_passed = True
    for command, description in commands:
        if not run_command(command, description, check=False):
            all_passed = False
    
    return all_passed


def setup_dev_environment():
    """Set up development environment."""
    print("🛠️  Setting up development environment...")
    
    # Create necessary directories
    directories = [
        "logs",
        "models/saved",
        "models/checkpoints",
        "docker/grafana/dashboards",
        "docker/grafana/datasources",
        "docker/prometheus"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Copy environment file if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            subprocess.run("cp .env.example .env", shell=True)
            print("✅ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your configuration")
        else:
            print("❌ .env.example file not found")
            return False
    
    # Install pre-commit hooks
    if Path(".pre-commit-config.yaml").exists():
        run_command("pre-commit install", "Installing pre-commit hooks", check=False)
    
    print("✅ Development environment setup complete!")
    return True


def clean_environment():
    """Clean up development environment."""
    print("🧹 Cleaning development environment...")
    
    commands = [
        ("docker-compose down -v", "Stopping services and removing volumes"),
        ("docker system prune -f", "Cleaning Docker system"),
        ("find . -name '__pycache__' -type d -exec rm -rf {} +", "Removing Python cache"),
        ("find . -name '*.pyc' -delete", "Removing Python bytecode files"),
    ]
    
    for command, description in commands:
        run_command(command, description, check=False)
    
    print("✅ Environment cleaned!")


def backup_data():
    """Backup database and important data."""
    print("💾 Backing up data...")
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/{timestamp}"
    
    Path(backup_dir).mkdir(parents=True, exist_ok=True)
    
    # Backup database
    db_backup_cmd = f"docker-compose exec -T postgres pg_dump -U tradingbot trading_bot > {backup_dir}/database.sql"
    run_command(db_backup_cmd, "Backing up database", check=False)
    
    # Backup configuration
    config_files = [".env", "config/", "models/"]
    for config_file in config_files:
        if Path(config_file).exists():
            run_command(f"cp -r {config_file} {backup_dir}/", f"Backing up {config_file}", check=False)
    
    print(f"✅ Backup completed: {backup_dir}")


def show_status():
    """Show system status."""
    print("📊 AI Trading Bot System Status")
    print("=" * 40)
    
    # Service status
    check_services()
    
    # Docker stats
    print("\n📈 Resource Usage:")
    subprocess.run("docker stats --no-stream --format 'table {{.Name}}\\t{{.CPUPerc}}\\t{{.MemUsage}}'", shell=True)
    
    # Disk usage
    print("\n💾 Disk Usage:")
    subprocess.run("df -h .", shell=True)


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Trading Bot Development Helper")
    parser.add_argument("command", choices=[
        "start", "stop", "restart", "status", "logs", "test", "setup", "clean", "backup"
    ], help="Command to run")
    parser.add_argument("--service", help="Specific service for logs command")
    parser.add_argument("--follow", "-f", action="store_true", help="Follow logs")
    
    args = parser.parse_args()
    
    # Change to project root directory
    os.chdir(Path(__file__).parent.parent)
    
    if args.command == "start":
        success = start_services()
        if success:
            print("\n🎉 All services started successfully!")
            print("📊 Grafana: http://localhost:3000 (admin/admin123)")
            print("🔍 Prometheus: http://localhost:9090")
            print("🤖 Trading Bot API: http://localhost:8000")
            print("📈 Dashboard: http://localhost:8501")
        sys.exit(0 if success else 1)
    
    elif args.command == "stop":
        success = stop_services()
        sys.exit(0 if success else 1)
    
    elif args.command == "restart":
        success = restart_services()
        sys.exit(0 if success else 1)
    
    elif args.command == "status":
        show_status()
    
    elif args.command == "logs":
        view_logs(args.service, args.follow)
    
    elif args.command == "test":
        success = run_tests()
        sys.exit(0 if success else 1)
    
    elif args.command == "setup":
        success = setup_dev_environment()
        sys.exit(0 if success else 1)
    
    elif args.command == "clean":
        clean_environment()
    
    elif args.command == "backup":
        backup_data()


if __name__ == "__main__":
    main()
