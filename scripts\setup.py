#!/usr/bin/env python3
"""
Setup script for AI Trading Bot System.

This script helps with initial setup, database initialization,
and environment configuration.

Author: inkbytefo
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def check_dependencies():
    """Check if required system dependencies are installed."""
    dependencies = {
        "docker": "Docker",
        "docker-compose": "Docker Compose",
        "git": "Git"
    }
    
    missing = []
    for cmd, name in dependencies.items():
        if not shutil.which(cmd):
            missing.append(name)
        else:
            print(f"✅ {name} is installed")
    
    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        return False
    
    return True


def create_env_file():
    """Create .env file from .env.example if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from .env.example")
        print("⚠️  Please edit .env file with your configuration")
        return True
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("❌ .env.example file not found")
        return False


def install_python_dependencies():
    """Install Python dependencies."""
    try:
        print("📦 Installing Python dependencies...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Python dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = [
        "logs",
        "models/saved",
        "models/checkpoints",
        "strategies/custom",
        "docker/postgres",
        "docker/nginx",
        "docker/prometheus",
        "docker/grafana/dashboards",
        "docker/grafana/datasources"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def setup_docker_configs():
    """Setup Docker configuration files."""
    # PostgreSQL init script
    postgres_init = Path("docker/postgres/init.sql")
    if not postgres_init.exists():
        postgres_init.write_text("""
-- Initialize trading bot database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tables will be handled by SQLAlchemy migrations
""")
        print("✅ Created PostgreSQL init script")
    
    # Nginx configuration
    nginx_conf = Path("docker/nginx/nginx.conf")
    if not nginx_conf.exists():
        nginx_conf.write_text("""
events {
    worker_connections 1024;
}

http {
    upstream trading_bot {
        server trading_bot:8000;
    }
    
    upstream grafana {
        server grafana:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://trading_bot;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
""")
        print("✅ Created Nginx configuration")
    
    # Prometheus configuration
    prometheus_conf = Path("docker/prometheus/prometheus.yml")
    if not prometheus_conf.exists():
        prometheus_conf.write_text("""
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'trading-bot'
    static_configs:
      - targets: ['trading_bot:9090']
""")
        print("✅ Created Prometheus configuration")
    
    return True


def run_tests():
    """Run basic tests to verify setup."""
    try:
        print("🧪 Running basic tests...")
        subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"
        ], check=True)
        print("✅ Tests passed")
        return True
    except subprocess.CalledProcessError:
        print("⚠️  Some tests failed, but setup can continue")
        return True
    except FileNotFoundError:
        print("⚠️  pytest not found, skipping tests")
        return True


def main():
    """Main setup function."""
    print("🚀 AI Trading Bot System Setup")
    print("=" * 40)
    
    # Change to project root directory
    os.chdir(Path(__file__).parent.parent)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Checking system dependencies", check_dependencies),
        ("Creating .env file", create_env_file),
        ("Installing Python dependencies", install_python_dependencies),
        ("Creating directories", create_directories),
        ("Setting up Docker configs", setup_docker_configs),
        ("Running tests", run_tests),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
            failed_steps.append(step_name)
    
    print("\n" + "=" * 40)
    if failed_steps:
        print("⚠️  Setup completed with some issues:")
        for step in failed_steps:
            print(f"   - {step}")
        print("\nPlease resolve these issues before running the bot.")
    else:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your API keys and configuration")
        print("2. Start services: docker-compose up -d")
        print("3. Run the bot: python main.py")
    
    print("\n📚 Documentation: docs/")
    print("🐛 Issues: Check logs/ directory")


if __name__ == "__main__":
    main()
