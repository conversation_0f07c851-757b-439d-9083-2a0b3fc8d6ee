"""
Modern AI Service for Trading Bot.

This module provides high-level AI services using the modern OpenAI-based provider system
for trading operations including sentiment analysis, price prediction, and market analysis.

Author: inkbytefo
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from enum import Enum

from ..providers.openai_compatible_provider import ModernOpenAIProvider
from ..providers.base_provider import AIRequest, ProviderConfig, ProviderType
from ...config.settings import Settings


class AnalysisType(Enum):
    """Types of AI analysis."""
    SENTIMENT = "sentiment"
    PRICE_PREDICTION = "price_prediction"
    MARKET_ANALYSIS = "market_analysis"
    NEWS_ANALYSIS = "news_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    RISK_ASSESSMENT = "risk_assessment"


@dataclass
class SentimentResult:
    """Sentiment analysis result."""
    sentiment: str  # positive, negative, neutral
    confidence: float  # 0.0 to 1.0
    score: float  # -1.0 to 1.0
    reasoning: str
    keywords: List[str]
    timestamp: datetime


@dataclass
class PricePrediction:
    """Price prediction result."""
    symbol: str
    timeframe: str  # 1h, 4h, 1d, etc.
    predicted_price: float
    confidence: float
    direction: str  # up, down, sideways
    probability: float
    reasoning: str
    timestamp: datetime


@dataclass
class MarketAnalysis:
    """Market analysis result."""
    market_condition: str  # bullish, bearish, neutral, volatile
    trend_strength: float  # 0.0 to 1.0
    volatility_level: str  # low, medium, high
    key_factors: List[str]
    recommendations: List[str]
    confidence: float
    timestamp: datetime


class AIService:
    """
    Modern AI service for trading operations using OpenAI-based providers.

    Provides easy-to-use methods for various AI-powered trading tasks
    with direct OpenAI client integration.
    """

    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.provider: Optional[ModernOpenAIProvider] = None

        # Cache for recent results
        self.sentiment_cache: Dict[str, SentimentResult] = {}
        self.prediction_cache: Dict[str, PricePrediction] = {}
        self.analysis_cache: Dict[str, MarketAnalysis] = {}

        # Cache TTL
        self.cache_ttl = timedelta(minutes=5)
    
    async def initialize(self):
        """Initialize the AI service with modern OpenAI provider."""
        try:
            # Create provider configuration
            config = ProviderConfig(
                name="openai",
                provider_type=ProviderType.OPENAI,
                api_key=self.settings.ai.openai_api_key,
                base_url=self.settings.ai.openai_base_url,
                model_mapping={
                    'sentiment': self.settings.ai.sentiment_model,
                    'prediction': self.settings.ai.prediction_model,
                    'analysis': self.settings.ai.analysis_model,
                    'general': self.settings.ai.general_model
                },
                timeout_seconds=self.settings.ai.openai_timeout,
                rate_limit_rpm=60,
                rate_limit_tpm=10000
            )

            # Create and initialize provider
            self.provider = ModernOpenAIProvider(config, self.settings)
            await self.provider.initialize()

            self.logger.info("✅ Modern AI Service initialized with OpenAI provider")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize AI Service: {e}")
            raise
    
    async def close(self):
        """Close the AI service and cleanup resources."""
        if self.provider:
            await self.provider.close()

        self.logger.info("AI Service closed")

    async def generate_response(self, prompt: str, model_type: str = "general",
                               temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """
        Generate a general AI response for any prompt.

        Args:
            prompt: The input prompt
            model_type: Type of model to use (general, prediction, sentiment, analysis)
            temperature: Response creativity (0.0-1.0)
            max_tokens: Maximum response length

        Returns:
            Generated response text
        """
        try:
            if not self.provider:
                return "AI service not initialized"

            # Create AI request
            request = AIRequest(
                messages=[{"role": "user", "content": prompt}],
                model=self._get_model_for_type(model_type),
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Generate response
            response = await self.provider.generate(request)

            if response.success:
                return response.content
            else:
                self.logger.error(f"AI response generation failed: {response.error}")
                return f"Error: {response.error}"

        except Exception as e:
            self.logger.error(f"Exception in generate_response: {e}")
            return f"Exception: {str(e)}"

    def _get_model_for_type(self, model_type: str) -> str:
        """Get the appropriate model for the given type."""
        model_mapping = {
            'general': self.settings.ai.general_model,
            'prediction': self.settings.ai.prediction_model,
            'sentiment': self.settings.ai.sentiment_model,
            'analysis': self.settings.ai.analysis_model
        }
        return model_mapping.get(model_type, self.settings.ai.general_model)
    
    async def analyze_sentiment(self, text: str, context: str = "general", 
                               use_cache: bool = True) -> SentimentResult:
        """
        Analyze sentiment of text (news, social media, etc.).
        
        Args:
            text: Text to analyze
            context: Context for analysis (news, social, earnings, etc.)
            use_cache: Whether to use cached results
            
        Returns:
            Sentiment analysis result
        """
        # Check cache
        cache_key = f"sentiment_{hash(text)}_{context}"
        if use_cache and cache_key in self.sentiment_cache:
            cached_result = self.sentiment_cache[cache_key]
            if datetime.now(timezone.utc) - cached_result.timestamp < self.cache_ttl:
                return cached_result
        
        # Prepare AI request
        prompt = self._build_sentiment_prompt(text, context)
        request = AIRequest(
            messages=[{"role": "user", "content": prompt}],
            model=self.settings.ai.sentiment_model,
            temperature=0.3,
            max_tokens=500
        )
        
        try:
            # Get AI response using modern provider
            response = await self.provider.generate(request)

            # Parse response
            result = self._parse_sentiment_response(response.content, text)

            # Cache result
            if use_cache:
                self.sentiment_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"Sentiment analysis failed: {e}")
            # Return neutral sentiment as fallback
            return SentimentResult(
                sentiment="neutral",
                confidence=0.0,
                score=0.0,
                reasoning=f"Analysis failed: {e}",
                keywords=[],
                timestamp=datetime.now(timezone.utc)
            )
    
    async def predict_price(self, symbol: str, timeframe: str = "1h", 
                           market_data: Optional[Dict] = None,
                           use_cache: bool = True) -> PricePrediction:
        """
        Predict price movement for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., BTC/USDT)
            timeframe: Prediction timeframe
            market_data: Current market data
            use_cache: Whether to use cached results
            
        Returns:
            Price prediction result
        """
        # Check cache
        cache_key = f"prediction_{symbol}_{timeframe}"
        if use_cache and cache_key in self.prediction_cache:
            cached_result = self.prediction_cache[cache_key]
            if datetime.now(timezone.utc) - cached_result.timestamp < self.cache_ttl:
                return cached_result
        
        # Prepare AI request
        prompt = self._build_prediction_prompt(symbol, timeframe, market_data)
        request = AIRequest(
            messages=[{"role": "user", "content": prompt}],
            model=self.settings.ai.prediction_model,
            temperature=0.2,
            max_tokens=800
        )
        
        try:
            # Get AI response using modern provider
            response = await self.provider.generate(request)
            
            # Parse response
            result = self._parse_prediction_response(response.content, symbol, timeframe)
            
            # Cache result
            if use_cache:
                self.prediction_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Price prediction failed: {e}")
            # Return neutral prediction as fallback
            return PricePrediction(
                symbol=symbol,
                timeframe=timeframe,
                predicted_price=0.0,
                confidence=0.0,
                direction="sideways",
                probability=0.33,
                reasoning=f"Prediction failed: {e}",
                timestamp=datetime.now(timezone.utc)
            )
    
    async def analyze_market(self, symbols: List[str], 
                           news_data: Optional[List[str]] = None,
                           use_cache: bool = True) -> MarketAnalysis:
        """
        Analyze overall market conditions.
        
        Args:
            symbols: List of symbols to analyze
            news_data: Recent news articles
            use_cache: Whether to use cached results
            
        Returns:
            Market analysis result
        """
        # Check cache
        cache_key = f"market_{hash(str(symbols))}"
        if use_cache and cache_key in self.analysis_cache:
            cached_result = self.analysis_cache[cache_key]
            if datetime.now(timezone.utc) - cached_result.timestamp < self.cache_ttl:
                return cached_result
        
        # Prepare AI request
        prompt = self._build_market_analysis_prompt(symbols, news_data)
        request = AIRequest(
            messages=[{"role": "user", "content": prompt}],
            model=self.settings.ai.analysis_model,
            temperature=0.4,
            max_tokens=1000
        )
        
        try:
            # Get AI response using modern provider
            response = await self.provider.generate(request)
            
            # Parse response
            result = self._parse_market_analysis_response(response.content)
            
            # Cache result
            if use_cache:
                self.analysis_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {e}")
            # Return neutral analysis as fallback
            return MarketAnalysis(
                market_condition="neutral",
                trend_strength=0.5,
                volatility_level="medium",
                key_factors=[],
                recommendations=[],
                confidence=0.0,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _build_sentiment_prompt(self, text: str, context: str) -> str:
        """Build prompt for sentiment analysis."""
        return f"""IMPORTANT: You MUST respond with ONLY a valid JSON object. No explanations, no additional text.

Analyze the sentiment of this {context} text: "{text}"

JSON RESPONSE REQUIRED:
{{
    "sentiment": "positive",
    "confidence": 0.85,
    "score": 0.7,
    "reasoning": "Brief explanation",
    "keywords": ["word1", "word2", "word3"]
}}

STRICT RULES:
- sentiment: EXACTLY "positive", "negative", or "neutral"
- confidence: number 0.0-1.0
- score: number -1.0 to 1.0
- reasoning: max 80 chars
- keywords: 2-5 words from text

RESPOND WITH JSON ONLY - NO OTHER TEXT ALLOWED."""
    
    def _build_prediction_prompt(self, symbol: str, timeframe: str, market_data: Optional[Dict]) -> str:
        """Build prompt for price prediction."""
        market_info = ""
        if market_data:
            market_info = f"\nCurrent market data: {market_data}"
        
        return f"""IMPORTANT: You MUST respond with ONLY a valid JSON object. No explanations, no additional text.

Predict price movement for {symbol} over {timeframe}:
{market_info}

JSON RESPONSE REQUIRED:
{{
    "direction": "up",
    "confidence": 0.75,
    "probability": 0.68,
    "reasoning": "Brief explanation",
    "predicted_price": 45000.0
}}

STRICT RULES:
- direction: EXACTLY "up", "down", or "sideways"
- confidence: number 0.0-1.0
- probability: number 0.0-1.0
- reasoning: max 100 chars
- predicted_price: number (0.0 if uncertain)

RESPOND WITH JSON ONLY - NO OTHER TEXT ALLOWED."""
    
    def _build_market_analysis_prompt(self, symbols: List[str], news_data: Optional[List[str]]) -> str:
        """Build prompt for market analysis."""
        symbols_str = ", ".join(symbols)
        news_info = ""
        if news_data:
            news_info = f"\nRecent news:\n" + "\n".join(news_data[:5])  # Limit to 5 articles
        
        return f"""IMPORTANT: You MUST respond with ONLY a valid JSON object. No explanations, no additional text.

Analyze market conditions for: {symbols_str}
{news_info}

JSON RESPONSE REQUIRED:
{{
    "market_condition": "bullish",
    "trend_strength": 0.7,
    "volatility_level": "medium",
    "key_factors": ["factor1", "factor2"],
    "recommendations": ["rec1", "rec2"],
    "confidence": 0.8
}}

STRICT RULES:
- market_condition: EXACTLY "bullish", "bearish", "neutral", or "volatile"
- trend_strength: number 0.0-1.0
- volatility_level: EXACTLY "low", "medium", or "high"
- key_factors: 2-4 factors
- recommendations: 1-3 recommendations
- confidence: number 0.0-1.0

RESPOND WITH JSON ONLY - NO OTHER TEXT ALLOWED."""
    
    def _parse_sentiment_response(self, response: str, original_text: str) -> SentimentResult:
        """Parse sentiment analysis response."""
        try:
            import json

            # Clean response - remove any non-JSON text
            response = response.strip()

            # Try to find JSON in the response
            if '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end]
                data = json.loads(json_str)
            else:
                # If no JSON found, try to parse the whole response
                data = json.loads(response)

            return SentimentResult(
                sentiment=data.get("sentiment", "neutral"),
                confidence=float(data.get("confidence", 0.0)),
                score=float(data.get("score", 0.0)),
                reasoning=data.get("reasoning", ""),
                keywords=data.get("keywords", []),
                timestamp=datetime.now(timezone.utc)
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse sentiment response: {e}")
            self.logger.debug(f"Raw response: {response[:200]}...")

            # Fallback: try to extract sentiment from text
            response_lower = response.lower()
            if any(word in response_lower for word in ['positive', 'bullish', 'good', 'up']):
                sentiment = "positive"
                score = 0.5
            elif any(word in response_lower for word in ['negative', 'bearish', 'bad', 'down']):
                sentiment = "negative"
                score = -0.5
            else:
                sentiment = "neutral"
                score = 0.0

            return SentimentResult(
                sentiment=sentiment,
                confidence=0.3,  # Low confidence for fallback
                score=score,
                reasoning="Fallback parsing due to invalid JSON response",
                keywords=[],
                timestamp=datetime.now(timezone.utc)
            )
    
    def _parse_prediction_response(self, response: str, symbol: str, timeframe: str) -> PricePrediction:
        """Parse price prediction response."""
        try:
            import json

            # Clean response - remove any non-JSON text
            response = response.strip()

            # Try to find JSON in the response
            if '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end]
                data = json.loads(json_str)
            else:
                # If no JSON found, try to parse the whole response
                data = json.loads(response)

            return PricePrediction(
                symbol=symbol,
                timeframe=timeframe,
                predicted_price=float(data.get("predicted_price", 0.0)),
                confidence=float(data.get("confidence", 0.0)),
                direction=data.get("direction", "sideways"),
                probability=float(data.get("probability", 0.33)),
                reasoning=data.get("reasoning", ""),
                timestamp=datetime.now(timezone.utc)
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse prediction response: {e}")
            self.logger.debug(f"Raw prediction response: {response[:200]}...")

            # Fallback: try to extract direction from text
            response_lower = response.lower()
            if any(word in response_lower for word in ['up', 'bullish', 'increase', 'rise']):
                direction = "up"
                confidence = 0.3
            elif any(word in response_lower for word in ['down', 'bearish', 'decrease', 'fall']):
                direction = "down"
                confidence = 0.3
            else:
                direction = "sideways"
                confidence = 0.2

            return PricePrediction(
                symbol=symbol,
                timeframe=timeframe,
                predicted_price=0.0,
                confidence=confidence,
                direction=direction,
                probability=0.33,
                reasoning="Fallback parsing due to invalid JSON response",
                timestamp=datetime.now(timezone.utc)
            )
    
    def _parse_market_analysis_response(self, response: str) -> MarketAnalysis:
        """Parse market analysis response."""
        try:
            import json

            # Clean response - remove any non-JSON text
            response = response.strip()

            # Try to find JSON in the response
            if '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end]
                data = json.loads(json_str)
            else:
                # If no JSON found, try to parse the whole response
                data = json.loads(response)

            return MarketAnalysis(
                market_condition=data.get("market_condition", "neutral"),
                trend_strength=float(data.get("trend_strength", 0.5)),
                volatility_level=data.get("volatility_level", "medium"),
                key_factors=data.get("key_factors", []),
                recommendations=data.get("recommendations", []),
                confidence=float(data.get("confidence", 0.0)),
                timestamp=datetime.now(timezone.utc)
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse market analysis response: {e}")
            self.logger.debug(f"Raw market analysis response: {response[:200]}...")

            # Fallback: try to extract basic info from text
            response_lower = response.lower()
            if any(word in response_lower for word in ['bullish', 'positive', 'up', 'growth']):
                market_condition = "bullish"
                trend_strength = 0.6
            elif any(word in response_lower for word in ['bearish', 'negative', 'down', 'decline']):
                market_condition = "bearish"
                trend_strength = 0.6
            else:
                market_condition = "neutral"
                trend_strength = 0.5

            return MarketAnalysis(
                market_condition=market_condition,
                trend_strength=trend_strength,
                volatility_level="medium",
                key_factors=["Fallback analysis due to parsing error"],
                recommendations=["Review AI response format"],
                confidence=0.2,
                timestamp=datetime.now(timezone.utc)
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get AI service statistics."""
        stats = {
            "cache_sizes": {
                "sentiment": len(self.sentiment_cache),
                "prediction": len(self.prediction_cache),
                "analysis": len(self.analysis_cache)
            },
            "cache_ttl_minutes": self.cache_ttl.total_seconds() / 60
        }
        
        if self.provider_manager:
            stats["provider_stats"] = self.provider_manager.get_provider_statistics()
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            if not self.provider_manager:
                return {"healthy": False, "error": "Provider manager not initialized"}
            
            # Test with a simple request
            test_result = await self.analyze_sentiment("Test message", use_cache=False)
            
            return {
                "healthy": True,
                "providers_available": len(self.provider_manager.get_healthy_providers()),
                "cache_sizes": {
                    "sentiment": len(self.sentiment_cache),
                    "prediction": len(self.prediction_cache),
                    "analysis": len(self.analysis_cache)
                }
            }
            
        except Exception as e:
            return {"healthy": False, "error": str(e)}


# Global AI service instance
ai_service: Optional[AIService] = None


def get_ai_service() -> AIService:
    """Get the global AI service instance."""
    global ai_service
    if ai_service is None:
        from ...config.settings import Settings
        settings = Settings()
        ai_service = AIService(settings)
    return ai_service
