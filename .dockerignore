# Git
.git
.gitignore
README.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Environment files
.env
.env.local
.env.production

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Documentation
docs/_build/

# Node modules (if any)
node_modules/

# Temporary files
tmp/
temp/
*.tmp

# Database
*.db
*.sqlite3

# Models (these should be mounted as volumes)
models/saved/
models/checkpoints/

# Docker
docker-compose.override.yml
