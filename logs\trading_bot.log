2025-08-07 17:11:01,002 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:11:01,002 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:11:01,002 - __main__ - INFO - Environment: development
2025-08-07 17:11:01,002 - __main__ - INFO - Paper Trading: True
2025-08-07 17:11:01,002 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:11:01,007 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:11:01,091 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:11:01,174 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:11:02,516 - src.exchanges.exchange_manager - WARNING - Could not sync time with Binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:02,517 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:11:02,519 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
2025-08-07 17:11:02,519 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken']
2025-08-07 17:11:04,075 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 17:11:04,076 - src.config.database - INFO - Initializing database connection...
2025-08-07 17:11:04,122 - src.config.database - INFO - Creating database tables...
2025-08-07 17:11:04,188 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 17:11:04,191 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 17:11:04,194 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 17:11:04,196 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 17:11:04,198 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 17:11:04,200 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 17:11:04,203 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 17:11:04,205 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 17:11:04,207 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 17:11:04,209 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 17:11:04,211 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 17:11:04,213 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 17:11:04,216 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 17:11:04,218 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 17:11:04,220 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 17:11:04,222 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 17:11:04,224 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 17:11:04,225 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 17:11:04,228 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 17:11:04,230 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 17:11:04,232 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 17:11:04,234 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 17:11:04,235 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 17:11:04,237 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 17:11:04,239 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 17:11:04,241 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 17:11:04,242 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 17:11:04,245 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 17:11:04,247 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 17:11:04,249 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 17:11:04,250 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 17:11:04,252 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 17:11:04,255 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 17:11:04,257 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 17:11:04,259 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 17:11:04,261 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 17:11:04,263 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 17:11:04,264 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 17:11:04,266 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 17:11:04,268 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 17:11:04,269 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 17:11:04,272 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 17:11:04,273 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 17:11:04,275 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 17:11:04,276 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 17:11:04,279 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 17:11:04,280 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 17:11:04,282 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 17:11:04,284 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 17:11:04,285 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 17:11:04,288 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 17:11:04,289 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 17:11:04,291 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 17:11:04,294 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 17:11:04,295 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 17:11:04,305 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 11613 characters truncated ... tity_precision__54)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'price_precision__0': 8, 'id__0': UUID('43812e43-78ae-4763-83db-1eb351b51ed6'), 'is_active__0': True, 'min_order_size__0': None, 'base_asset__0': 'BTC', 'max_order_size__0': None, 'quantity_precision__0': 8, 'symbol__0': 'BTC/USDT', 'quote_asset__0': 'USDT', 'exchange__0': 'kraken', 'price_precision__1': 8, 'id__1': UUID('9fe6dd73-82e8-412b-a261-e011d6068bb6'), 'is_active__1': True, 'min_order_size__1': None, 'base_asset__1': 'ETH', 'max_order_size__1': None, 'quantity_precision__1': 8, 'symbol__1': 'ETH/USDT', 'quote_asset__1': 'USDT', 'exchange__1': 'kraken', 'price_precision__2': 8, 'id__2': UUID('de8eaff5-47dc-4e70-9020-d5ef602885f5'), 'is_active__2': True, 'min_order_size__2': None, 'base_asset__2': 'BNB', 'max_order_size__2': None, 'quantity_precision__2': 8, 'symbol__2': 'BNB/USDT', 'quote_asset__2': 'USDT', 'exchange__2': 'binance', 'price_precision__3': 8, 'id__3': UUID('a1a22f7f-111b-47ef-91d0-26c6cbdb2a19'), 'is_active__3': True, 'min_order_size__3': None, 'base_asset__3': 'BNB', 'max_order_size__3': None, 'quantity_precision__3': 8, 'symbol__3': 'BNB/USDT', 'quote_asset__3': 'USDT', 'exchange__3': 'kraken', 'price_precision__4': 8, 'id__4': UUID('05d80c19-d266-4876-aa81-da9fdef5456c'), 'is_active__4': True, 'min_order_size__4': None, 'base_asset__4': 'XRP', 'max_order_size__4': None, 'quantity_precision__4': 8, 'symbol__4': 'XRP/USDT', 'quote_asset__4': 'USDT', 'exchange__4': 'binance' ... 450 parameters truncated ... 'price_precision__50': 8, 'id__50': UUID('e36f1e3d-16c8-4817-9332-bdeb12e552ea'), 'is_active__50': True, 'min_order_size__50': None, 'base_asset__50': 'TRX', 'max_order_size__50': None, 'quantity_precision__50': 8, 'symbol__50': 'TRX/USDT', 'quote_asset__50': 'USDT', 'exchange__50': 'kraken', 'price_precision__51': 8, 'id__51': UUID('f0def20c-c7d5-48b7-80bb-992dde072a7e'), 'is_active__51': True, 'min_order_size__51': None, 'base_asset__51': 'EOS', 'max_order_size__51': None, 'quantity_precision__51': 8, 'symbol__51': 'EOS/USDT', 'quote_asset__51': 'USDT', 'exchange__51': 'binance', 'price_precision__52': 8, 'id__52': UUID('a7fa982f-67a7-4eae-bf53-8b51517477fd'), 'is_active__52': True, 'min_order_size__52': None, 'base_asset__52': 'EOS', 'max_order_size__52': None, 'quantity_precision__52': 8, 'symbol__52': 'EOS/USDT', 'quote_asset__52': 'USDT', 'exchange__52': 'kraken', 'price_precision__53': 8, 'id__53': UUID('a6b199d5-05d6-4350-a5bf-622717c9398b'), 'is_active__53': True, 'min_order_size__53': None, 'base_asset__53': 'IOTA', 'max_order_size__53': None, 'quantity_precision__53': 8, 'symbol__53': 'IOTA/USDT', 'quote_asset__53': 'USDT', 'exchange__53': 'binance', 'price_precision__54': 8, 'id__54': UUID('76ea066f-b1ce-445b-97c0-adbb6f3c606b'), 'is_active__54': True, 'min_order_size__54': None, 'base_asset__54': 'IOTA', 'max_order_size__54': None, 'quantity_precision__54': 8, 'symbol__54': 'IOTA/USDT', 'quote_asset__54': 'USDT', 'exchange__54': 'kraken'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 17:11:04,305 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 17:11:04,306 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 17:11:04,306 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 17:11:04,757 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:11:04,841 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 17:11:04,842 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 17:11:04,987 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:11:05,056 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 17:11:05,057 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 17:11:05,062 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 17:11:05,063 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 17:11:05,066 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 17:11:05,069 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 17:11:05,069 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 17:11:05,074 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 17:11:05,074 - __main__ - INFO - [OK] AI Trading Bot System started successfully!
2025-08-07 17:11:05,074 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 17:11:05,075 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 17:11:05,075 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 17:11:05,466 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:05,466 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:05,467 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:05,595 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:06,733 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,735 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,735 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:07,080 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:08,257 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:08,257 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:08,258 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:08,387 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:08,914 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:09,853 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:10,210 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:11,042 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:11,042 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:11,043 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:11,175 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:11,301 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:12,524 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:12,646 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:13,698 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:13,844 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:13,845 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:13,845 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:13,966 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:14,907 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:15,430 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:16,629 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:16,629 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:16,629 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:16,629 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:16,756 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:17,330 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:18,227 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:18,480 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:19,418 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:19,418 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:19,419 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:19,419 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:19,534 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:19,644 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:20,821 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:21,014 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:21,993 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:22,212 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:22,213 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:22,213 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:22,213 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:22,321 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:23,133 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:23,802 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:24,985 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:24,985 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:24,986 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:24,986 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:24,986 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:25,119 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:25,535 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:26,583 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:26,828 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:27,773 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:27,773 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:27,773 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:27,774 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:27,774 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:27,910 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:27,938 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:29,145 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:29,375 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:30,333 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:30,574 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:30,575 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:30,576 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:30,576 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:30,576 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:30,689 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:31,593 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:32,167 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:33,461 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:33,461 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:33,461 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:33,461 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:33,462 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:33,462 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:33,478 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:33,846 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:34,957 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:35,008 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:36,137 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:36,137 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:36,137 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:36,187 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:36,261 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:37,355 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:37,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:38,703 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:38,931 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:38,931 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:38,931 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:39,054 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:21:58,309 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:21:58,309 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:21:58,309 - __main__ - INFO - Environment: development
2025-08-07 17:21:58,309 - __main__ - INFO - Paper Trading: True
2025-08-07 17:21:58,309 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:21:58,311 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:21:58,389 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:21:58,467 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:21:59,791 - src.exchanges.exchange_manager - WARNING - Could not sync time with Binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:21:59,791 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:21:59,791 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
2025-08-07 17:22:00,153 - src.exchanges.exchange_manager - WARNING - Could not sync time with MEXC: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:00,153 - src.exchanges.exchange_manager - INFO - [OK] MEXC exchange configured
2025-08-07 17:22:00,153 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken', 'mexc']
2025-08-07 17:22:03,037 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 17:22:03,037 - src.config.database - INFO - Initializing database connection...
2025-08-07 17:22:03,078 - src.config.database - INFO - Creating database tables...
2025-08-07 17:22:03,132 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 17:22:03,133 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on mexc
2025-08-07 17:22:03,136 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 17:22:03,138 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on mexc
2025-08-07 17:22:03,140 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 17:22:03,141 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 17:22:03,142 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on mexc
2025-08-07 17:22:03,145 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 17:22:03,147 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 17:22:03,148 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on mexc
2025-08-07 17:22:03,151 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 17:22:03,153 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on mexc
2025-08-07 17:22:03,154 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 17:22:03,156 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 17:22:03,157 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on mexc
2025-08-07 17:22:03,159 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 17:22:03,161 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 17:22:03,162 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on mexc
2025-08-07 17:22:03,165 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 17:22:03,167 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on mexc
2025-08-07 17:22:03,168 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 17:22:03,170 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 17:22:03,172 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on mexc
2025-08-07 17:22:03,173 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 17:22:03,174 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 17:22:03,177 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on mexc
2025-08-07 17:22:03,178 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 17:22:03,180 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 17:22:03,181 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on mexc
2025-08-07 17:22:03,184 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 17:22:03,186 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on mexc
2025-08-07 17:22:03,187 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 17:22:03,189 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 17:22:03,191 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on mexc
2025-08-07 17:22:03,192 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 17:22:03,194 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 17:22:03,195 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on mexc
2025-08-07 17:22:03,197 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 17:22:03,198 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 17:22:03,200 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on mexc
2025-08-07 17:22:03,202 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 17:22:03,203 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 17:22:03,205 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on mexc
2025-08-07 17:22:03,205 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 17:22:03,208 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 17:22:03,210 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on mexc
2025-08-07 17:22:03,211 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 17:22:03,213 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 17:22:03,214 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on mexc
2025-08-07 17:22:03,215 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 17:22:03,217 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 17:22:03,218 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on mexc
2025-08-07 17:22:03,220 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 17:22:03,222 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 17:22:03,223 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on mexc
2025-08-07 17:22:03,224 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 17:22:03,227 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 17:22:03,228 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on mexc
2025-08-07 17:22:03,230 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 17:22:03,232 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 17:22:03,233 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on mexc
2025-08-07 17:22:03,234 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 17:22:03,236 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 17:22:03,237 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on mexc
2025-08-07 17:22:03,238 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 17:22:03,240 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 17:22:03,242 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on mexc
2025-08-07 17:22:03,244 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 17:22:03,245 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 17:22:03,247 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on mexc
2025-08-07 17:22:03,249 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 17:22:03,251 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 17:22:03,252 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on mexc
2025-08-07 17:22:03,254 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 17:22:03,255 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 17:22:03,256 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on mexc
2025-08-07 17:22:03,258 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 17:22:03,259 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 17:22:03,261 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on mexc
2025-08-07 17:22:03,262 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 17:22:03,264 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 17:22:03,265 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on mexc
2025-08-07 17:22:03,267 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 17:22:03,268 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 17:22:03,269 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on mexc
2025-08-07 17:22:03,279 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 18063 characters truncated ... tity_precision__84)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'base_asset__0': 'BTC', 'max_order_size__0': None, 'price_precision__0': 8, 'min_order_size__0': None, 'symbol__0': 'BTC/USDT', 'id__0': UUID('b1671b5a-d458-48d9-9ce3-d2a8c9870c0f'), 'exchange__0': 'kraken', 'quantity_precision__0': 8, 'quote_asset__0': 'USDT', 'is_active__0': True, 'base_asset__1': 'BTC', 'max_order_size__1': None, 'price_precision__1': 8, 'min_order_size__1': None, 'symbol__1': 'BTC/USDT', 'id__1': UUID('33ebf2a9-**************-66a5b32b18ce'), 'exchange__1': 'mexc', 'quantity_precision__1': 8, 'quote_asset__1': 'USDT', 'is_active__1': True, 'base_asset__2': 'ETH', 'max_order_size__2': None, 'price_precision__2': 8, 'min_order_size__2': None, 'symbol__2': 'ETH/USDT', 'id__2': UUID('21e2ac7b-21d6-4f5a-8591-7c2826eef2fc'), 'exchange__2': 'kraken', 'quantity_precision__2': 8, 'quote_asset__2': 'USDT', 'is_active__2': True, 'base_asset__3': 'ETH', 'max_order_size__3': None, 'price_precision__3': 8, 'min_order_size__3': None, 'symbol__3': 'ETH/USDT', 'id__3': UUID('5f28c0e4-94ed-4c82-8f70-720b10b2edd3'), 'exchange__3': 'mexc', 'quantity_precision__3': 8, 'quote_asset__3': 'USDT', 'is_active__3': True, 'base_asset__4': 'BNB', 'max_order_size__4': None, 'price_precision__4': 8, 'min_order_size__4': None, 'symbol__4': 'BNB/USDT', 'id__4': UUID('9f2113c1-16ab-413d-8701-9b34e3ecb0d2'), 'exchange__4': 'binance', 'quantity_precision__4': 8, 'quote_asset__4': 'USDT', 'is_active__4': True ... 750 parameters truncated ... 'base_asset__80': 'EOS', 'max_order_size__80': None, 'price_precision__80': 8, 'min_order_size__80': None, 'symbol__80': 'EOS/USDT', 'id__80': UUID('fc69a5a4-0329-47d9-ac3a-bcd30a909498'), 'exchange__80': 'kraken', 'quantity_precision__80': 8, 'quote_asset__80': 'USDT', 'is_active__80': True, 'base_asset__81': 'EOS', 'max_order_size__81': None, 'price_precision__81': 8, 'min_order_size__81': None, 'symbol__81': 'EOS/USDT', 'id__81': UUID('ba35dd56-9c03-498d-953d-7ef04d9077f5'), 'exchange__81': 'mexc', 'quantity_precision__81': 8, 'quote_asset__81': 'USDT', 'is_active__81': True, 'base_asset__82': 'IOTA', 'max_order_size__82': None, 'price_precision__82': 8, 'min_order_size__82': None, 'symbol__82': 'IOTA/USDT', 'id__82': UUID('a62be538-7b3c-40bf-ba54-950a38ec73f4'), 'exchange__82': 'binance', 'quantity_precision__82': 8, 'quote_asset__82': 'USDT', 'is_active__82': True, 'base_asset__83': 'IOTA', 'max_order_size__83': None, 'price_precision__83': 8, 'min_order_size__83': None, 'symbol__83': 'IOTA/USDT', 'id__83': UUID('6970c873-ba90-48fa-a070-3a9cf600b562'), 'exchange__83': 'kraken', 'quantity_precision__83': 8, 'quote_asset__83': 'USDT', 'is_active__83': True, 'base_asset__84': 'IOTA', 'max_order_size__84': None, 'price_precision__84': 8, 'min_order_size__84': None, 'symbol__84': 'IOTA/USDT', 'id__84': UUID('e9f5438b-fb41-449f-a9e0-721682845d46'), 'exchange__84': 'mexc', 'quantity_precision__84': 8, 'quote_asset__84': 'USDT', 'is_active__84': True}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 17:22:03,280 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 17:22:03,280 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 17:22:03,281 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 17:22:03,699 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:22:03,778 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 17:22:03,779 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 17:22:03,914 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:22:03,996 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 17:22:03,998 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 17:22:04,004 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 17:22:04,004 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 17:22:04,007 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 17:22:04,010 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 17:22:04,010 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 17:22:04,016 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 17:22:04,016 - __main__ - INFO - [OK] AI Trading Bot System started successfully!
2025-08-07 17:22:04,016 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 17:22:04,016 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 17:22:04,017 - src.data.market_data_collector - INFO - Starting collection loop for mexc
2025-08-07 17:22:04,017 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 17:22:04,393 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:22:04,542 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:05,298 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:05,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:22:06,014 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:06,290 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:22:06,295 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:22:07,299 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:07,478 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:08,116 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:08,592 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:08,740 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:22:08,873 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:09,323 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:09,908 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:10,348 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:10,502 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:10,895 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:22:10,895 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:22:11,675 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:11,828 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:11,878 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:12,838 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:13,173 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:13,284 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:14,012 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:22:14,471 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:14,478 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:22:14,612 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:15,756 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:16,069 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:16,326 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:16,758 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:22:16,758 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:22:16,759 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:22:17,554 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:17,591 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:17,775 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:18,745 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:19,016 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:19,067 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:20,012 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:20,226 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:22:20,328 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:20,358 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:22:21,189 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:21,518 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:22:21,518 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:22:21,518 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:22:21,807 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:22:22,393 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:22:22,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from mexc: mexc {"code":10072,"msg":"Api key info invalid"}
2025-08-07 17:35:36,729 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:35:36,730 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:35:36,730 - __main__ - INFO - Environment: development
2025-08-07 17:35:36,730 - __main__ - INFO - Paper Trading: True
2025-08-07 17:35:36,730 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:35:36,735 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:35:36,841 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:35:36,936 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:35:38,274 - src.exchanges.exchange_manager - WARNING - Could not sync time with Binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:35:38,274 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:35:38,279 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
2025-08-07 17:37:23,181 - src.exchanges.exchange_manager - INFO - MEXC time difference set to: -122ms
2025-08-07 17:37:23,181 - src.exchanges.exchange_manager - INFO - [OK] MEXC exchange configured
2025-08-07 17:37:23,181 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken', 'mexc']
2025-08-07 17:37:26,058 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 17:37:26,058 - src.config.database - INFO - Initializing database connection...
2025-08-07 17:37:26,114 - src.config.database - INFO - Creating database tables...
2025-08-07 17:37:26,184 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 17:37:26,186 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on mexc
2025-08-07 17:37:26,189 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 17:37:26,192 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on mexc
2025-08-07 17:37:26,194 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 17:37:26,196 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 17:37:26,199 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on mexc
2025-08-07 17:37:26,201 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 17:37:26,203 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 17:37:26,205 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on mexc
2025-08-07 17:37:26,210 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 17:37:26,212 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on mexc
2025-08-07 17:37:26,214 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 17:37:26,216 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 17:37:26,218 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on mexc
2025-08-07 17:37:26,220 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 17:37:26,223 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 17:37:26,225 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on mexc
2025-08-07 17:37:26,230 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 17:37:26,232 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on mexc
2025-08-07 17:37:26,235 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 17:37:26,237 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 17:37:26,239 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on mexc
2025-08-07 17:37:26,241 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 17:37:26,244 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 17:37:26,246 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on mexc
2025-08-07 17:37:26,248 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 17:37:26,250 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 17:37:26,253 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on mexc
2025-08-07 17:37:26,256 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 17:37:26,260 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on mexc
2025-08-07 17:37:26,262 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 17:37:26,264 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 17:37:26,266 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on mexc
2025-08-07 17:37:26,268 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 17:37:26,271 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 17:37:26,273 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on mexc
2025-08-07 17:37:26,276 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 17:37:26,278 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 17:37:26,280 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on mexc
2025-08-07 17:37:26,282 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 17:37:26,284 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 17:37:26,287 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on mexc
2025-08-07 17:37:26,289 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 17:37:26,292 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 17:37:26,294 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on mexc
2025-08-07 17:37:26,296 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 17:37:26,298 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 17:37:26,300 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on mexc
2025-08-07 17:37:26,302 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 17:37:26,304 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 17:37:26,307 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on mexc
2025-08-07 17:37:26,309 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 17:37:26,311 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 17:37:26,313 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on mexc
2025-08-07 17:37:26,316 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 17:37:26,318 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 17:37:26,320 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on mexc
2025-08-07 17:37:26,323 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 17:37:26,325 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 17:37:26,328 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on mexc
2025-08-07 17:37:26,331 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 17:37:26,333 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 17:37:26,335 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on mexc
2025-08-07 17:37:26,338 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 17:37:26,340 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 17:37:26,342 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on mexc
2025-08-07 17:37:26,345 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 17:37:26,347 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 17:37:26,350 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on mexc
2025-08-07 17:37:26,352 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 17:37:26,355 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 17:37:26,357 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on mexc
2025-08-07 17:37:26,359 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 17:37:26,361 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 17:37:26,363 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on mexc
2025-08-07 17:37:26,366 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 17:37:26,368 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 17:37:26,371 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on mexc
2025-08-07 17:37:26,373 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 17:37:26,376 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 17:37:26,378 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on mexc
2025-08-07 17:37:26,380 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 17:37:26,382 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 17:37:26,384 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on mexc
2025-08-07 17:37:26,396 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 18063 characters truncated ... tity_precision__84)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'quote_asset__0': 'USDT', 'price_precision__0': 8, 'quantity_precision__0': 8, 'min_order_size__0': None, 'id__0': UUID('a80d441e-c353-4dd8-bde5-de6764058d3c'), 'max_order_size__0': None, 'symbol__0': 'BTC/USDT', 'exchange__0': 'kraken', 'base_asset__0': 'BTC', 'is_active__0': True, 'quote_asset__1': 'USDT', 'price_precision__1': 8, 'quantity_precision__1': 8, 'min_order_size__1': None, 'id__1': UUID('7e55bd2a-d3ec-4329-83a0-85d838e571c8'), 'max_order_size__1': None, 'symbol__1': 'BTC/USDT', 'exchange__1': 'mexc', 'base_asset__1': 'BTC', 'is_active__1': True, 'quote_asset__2': 'USDT', 'price_precision__2': 8, 'quantity_precision__2': 8, 'min_order_size__2': None, 'id__2': UUID('d381db47-37ab-4005-ad9a-b4a75490a1a9'), 'max_order_size__2': None, 'symbol__2': 'ETH/USDT', 'exchange__2': 'kraken', 'base_asset__2': 'ETH', 'is_active__2': True, 'quote_asset__3': 'USDT', 'price_precision__3': 8, 'quantity_precision__3': 8, 'min_order_size__3': None, 'id__3': UUID('14e797fe-47ec-4107-8802-e7298636bfbf'), 'max_order_size__3': None, 'symbol__3': 'ETH/USDT', 'exchange__3': 'mexc', 'base_asset__3': 'ETH', 'is_active__3': True, 'quote_asset__4': 'USDT', 'price_precision__4': 8, 'quantity_precision__4': 8, 'min_order_size__4': None, 'id__4': UUID('b6645239-29e7-4c36-882b-e72ec7b15530'), 'max_order_size__4': None, 'symbol__4': 'BNB/USDT', 'exchange__4': 'binance', 'base_asset__4': 'BNB', 'is_active__4': True ... 750 parameters truncated ... 'quote_asset__80': 'USDT', 'price_precision__80': 8, 'quantity_precision__80': 8, 'min_order_size__80': None, 'id__80': UUID('381adec3-397e-4781-9846-532696add8f1'), 'max_order_size__80': None, 'symbol__80': 'EOS/USDT', 'exchange__80': 'kraken', 'base_asset__80': 'EOS', 'is_active__80': True, 'quote_asset__81': 'USDT', 'price_precision__81': 8, 'quantity_precision__81': 8, 'min_order_size__81': None, 'id__81': UUID('67e09798-034c-437b-aa46-6237566d552f'), 'max_order_size__81': None, 'symbol__81': 'EOS/USDT', 'exchange__81': 'mexc', 'base_asset__81': 'EOS', 'is_active__81': True, 'quote_asset__82': 'USDT', 'price_precision__82': 8, 'quantity_precision__82': 8, 'min_order_size__82': None, 'id__82': UUID('f13fbff2-d6a7-42ac-bf8b-64ccd74ec13b'), 'max_order_size__82': None, 'symbol__82': 'IOTA/USDT', 'exchange__82': 'binance', 'base_asset__82': 'IOTA', 'is_active__82': True, 'quote_asset__83': 'USDT', 'price_precision__83': 8, 'quantity_precision__83': 8, 'min_order_size__83': None, 'id__83': UUID('ddec1001-7087-4e8b-85f9-b5bd4c4dc017'), 'max_order_size__83': None, 'symbol__83': 'IOTA/USDT', 'exchange__83': 'kraken', 'base_asset__83': 'IOTA', 'is_active__83': True, 'quote_asset__84': 'USDT', 'price_precision__84': 8, 'quantity_precision__84': 8, 'min_order_size__84': None, 'id__84': UUID('873b13c6-d591-4019-906c-25a569853a51'), 'max_order_size__84': None, 'symbol__84': 'IOTA/USDT', 'exchange__84': 'mexc', 'base_asset__84': 'IOTA', 'is_active__84': True}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 17:37:26,397 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 17:37:26,397 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 17:37:26,397 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 17:37:26,871 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:37:26,946 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 17:37:26,947 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 17:37:27,071 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:37:27,104 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 17:37:27,104 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 17:37:27,111 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 17:37:27,112 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 17:37:27,115 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 17:37:27,118 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 17:37:27,119 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 17:37:27,125 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 17:37:27,125 - __main__ - INFO - [OK] AI Trading Bot System started successfully!
2025-08-07 17:37:27,126 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 17:37:27,126 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 17:37:27,126 - src.data.market_data_collector - INFO - Starting collection loop for mexc
2025-08-07 17:37:27,126 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 17:37:27,519 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:37:41,876 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:37:41,876 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:37:41,876 - __main__ - INFO - Environment: development
2025-08-07 17:37:41,876 - __main__ - INFO - Paper Trading: True
2025-08-07 17:37:41,876 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:37:41,881 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:37:41,966 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:37:42,052 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:37:54,021 - src.exchanges.exchange_manager - INFO - Binance time difference set to: -121ms
2025-08-07 17:37:54,021 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:37:54,024 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
2025-08-07 17:38:52,323 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:38:52,325 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:38:52,325 - __main__ - INFO - Environment: development
2025-08-07 17:38:52,325 - __main__ - INFO - Paper Trading: True
2025-08-07 17:38:52,325 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:38:52,325 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:38:52,415 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:38:52,497 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:39:04,498 - src.exchanges.exchange_manager - INFO - Binance time difference set to: -119ms
2025-08-07 17:39:04,498 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:39:04,501 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
