#!/usr/bin/env python3
"""
IP Adresi Öğrenme Script'i
Binance API için IP kısıtlaması eklemek üzere mevcut IP adresinizi öğrenin.
"""

import requests
import socket

def get_public_ip():
    """Public IP adresini öğren."""
    try:
        # Birkaç farklı servis dene
        services = [
            'https://api.ipify.org',
            'https://ipinfo.io/ip',
            'https://icanhazip.com'
        ]
        
        for service in services:
            try:
                response = requests.get(service, timeout=5)
                if response.status_code == 200:
                    return response.text.strip()
            except:
                continue
        
        return None
    except Exception as e:
        print(f"Hata: {e}")
        return None

def get_local_ip():
    """Local IP adresini öğren."""
    try:
        # Google DNS'e bağlanarak local IP'yi öğren
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return None

if __name__ == "__main__":
    print("🌐 IP Adresi Bilgileri")
    print("=" * 40)
    
    # Public IP
    public_ip = get_public_ip()
    if public_ip:
        print(f"🌍 Public IP: {public_ip}")
        print(f"   ↳ Bu IP'yi Binance API kısıtlamalarına ekleyin")
    else:
        print("❌ Public IP alınamadı")
    
    # Local IP
    local_ip = get_local_ip()
    if local_ip:
        print(f"🏠 Local IP: {local_ip}")
    else:
        print("❌ Local IP alınamadı")
    
    print("\n📋 Binance API IP Kısıtlaması Ekleme:")
    print("1. https://www.binance.com/en/my/settings/api-management adresine gidin")
    print("2. API anahtarınızı bulun ve 'Edit' butonuna tıklayın")
    print("3. 'Restrict access to trusted IPs only' seçeneğini işaretleyin")
    if public_ip:
        print(f"4. IP alanına şu adresi ekleyin: {public_ip}")
    print("5. Değişiklikleri kaydedin")
