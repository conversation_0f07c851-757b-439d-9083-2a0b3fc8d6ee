#!/usr/bin/env python3
"""
Test script for updated exchange connections (2025)
Tests Binance Spot, Binance Futures, and MEXC Global exchanges.

Author: inkbytefo
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import Settings
from src.exchanges.exchange_manager import ExchangeManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_exchange_connections():
    """Test all exchange connections with detailed logging."""
    print("🚀 Testing Exchange Connections (2025 Updated)")
    print("=" * 60)
    
    try:
        # Initialize settings
        settings = Settings()
        print(f"📋 Paper Trading Mode: {settings.PAPER_TRADING}")
        print(f"📋 Environment: {settings.ENVIRONMENT}")
        
        # Initialize exchange manager
        exchange_manager = ExchangeManager(settings)
        
        print("\n🔄 Initializing Exchange Manager...")
        await exchange_manager.initialize()
        
        # Get supported and active exchanges
        supported = exchange_manager.get_supported_exchanges()
        active = exchange_manager.get_active_exchanges()
        
        print(f"\n📊 Supported exchanges: {supported}")
        print(f"📊 Active exchanges: {active}")
        
        # Test each active exchange
        for exchange_name in active:
            print(f"\n🔍 Testing {exchange_name}...")
            
            try:
                # Test market data
                markets = await exchange_manager.get_markets(exchange_name)
                print(f"  ✅ Markets loaded: {len(markets)} pairs")
                
                # Test popular pairs
                popular_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
                available_pairs = []
                
                for pair in popular_pairs:
                    if pair in markets:
                        available_pairs.append(pair)
                
                print(f"  ✅ Popular pairs available: {available_pairs}")
                
                # Test ticker data for BTC/USDT if available
                if 'BTC/USDT' in markets:
                    ticker = await exchange_manager.get_ticker(exchange_name, 'BTC/USDT')
                    if ticker:
                        print(f"  ✅ BTC/USDT ticker: ${ticker.get('last', 'N/A')}")
                
            except Exception as e:
                print(f"  ❌ {exchange_name} test failed: {e}")
        
        # Test health check
        print(f"\n🏥 Running health check...")
        health = await exchange_manager.health_check()
        print(f"  {'✅' if health else '❌'} Health check: {'PASSED' if health else 'FAILED'}")
        
        # Close connections
        await exchange_manager.close()
        print(f"\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_specific_exchange_features():
    """Test specific features of each exchange."""
    print("\n🔬 Testing Exchange-Specific Features")
    print("=" * 60)
    
    settings = Settings()
    exchange_manager = ExchangeManager(settings)
    
    try:
        await exchange_manager.initialize()
        active = exchange_manager.get_active_exchanges()
        
        for exchange_name in active:
            print(f"\n🔍 Testing {exchange_name} specific features...")
            
            try:
                # Test rate limits
                exchange = exchange_manager.exchanges[exchange_name]
                rate_limit = exchange.rateLimit
                print(f"  ✅ Rate limit: {rate_limit}ms between requests")
                
                # Test timeout settings
                timeout = exchange.timeout
                print(f"  ✅ Timeout: {timeout}ms")
                
                # Test exchange type
                default_type = exchange.options.get('defaultType', 'unknown')
                print(f"  ✅ Default type: {default_type}")
                
                # Test time synchronization
                time_diff = exchange.options.get('timeDifference', 0)
                print(f"  ✅ Time difference: {time_diff}ms")
                
            except Exception as e:
                print(f"  ❌ {exchange_name} feature test failed: {e}")
        
        await exchange_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Feature test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🧪 AI Trading Bot - Exchange Connection Tests (2025)")
    print("=" * 80)
    
    # Test 1: Basic connections
    test1_success = await test_exchange_connections()
    
    # Test 2: Specific features
    test2_success = await test_specific_exchange_features()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 40)
    print(f"Basic Connection Test: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"Feature Test: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Exchange system is ready for 2025.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
