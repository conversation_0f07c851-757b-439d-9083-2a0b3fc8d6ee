#!/usr/bin/env python3
"""
AI Functions Test Script for AI Trading Bot System.

This script tests all AI-related functionality to ensure proper operation.

Author: inkbytefo
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config.settings import Settings
from src.ai.ai_manager import AIManager
from src.core.ai_agent import AITradingAgent
from src.analysis.technical_analyzer import TechnicalAnalyzer
from src.analysis.sentiment_analyzer import SentimentAnalyzer


class AIFunctionTester:
    """Test suite for AI functions."""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = self._setup_logging()
        
        # AI components
        self.ai_manager = None
        self.ai_agent = None
        self.technical_analyzer = None
        self.sentiment_analyzer = None
        
        # Test results
        self.test_results = {}
        
    def _setup_logging(self):
        """Setup basic logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    async def initialize_components(self):
        """Initialize AI components for testing."""
        try:
            self.logger.info("Initializing AI components for testing...")
            
            # Initialize AI Manager
            self.ai_manager = AIManager(self.settings)
            await self.ai_manager.initialize()
            
            # Initialize AI Agent
            self.ai_agent = AITradingAgent(self.settings)
            await self.ai_agent.initialize()
            
            # Initialize analyzers
            self.technical_analyzer = TechnicalAnalyzer(self.settings)
            self.sentiment_analyzer = SentimentAnalyzer(self.settings)
            
            self.logger.info("AI components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI components: {e}")
            return False
    
    async def test_ai_manager_connection(self):
        """Test AI Manager connection and basic functionality."""
        test_name = "AI Manager Connection"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            # Test connection
            result = await self.ai_manager.test_ai_connection()
            
            if result.get("connection_test") == "passed":
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "AI Manager connection successful",
                    "response": result.get("response", "")
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": f"Connection test failed: {result.get('error', 'Unknown error')}"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_ai_prediction_generation(self):
        """Test AI prediction generation."""
        test_name = "AI Prediction Generation"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            test_prompt = """
            Analyze BTC/USDT trading data:
            Current Price: $45,000
            24h Change: +2.5%
            Volume: High
            RSI: 65
            MACD: Bullish crossover
            
            Provide trading recommendation in JSON format:
            {"action": "buy/sell/hold", "confidence": 0.0-1.0, "reasoning": "explanation"}
            """
            
            response = await self.ai_manager.generate_prediction(
                prompt=test_prompt,
                model_type="prediction"
            )
            
            if response and len(response) > 10:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "AI prediction generated successfully",
                    "response_length": len(response),
                    "sample_response": response[:200] + "..." if len(response) > 200 else response
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "AI prediction generation failed or returned empty response"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_market_sentiment_analysis(self):
        """Test market sentiment analysis."""
        test_name = "Market Sentiment Analysis"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            test_market_data = {
                "symbol": "BTC/USDT",
                "current_price": 45000,
                "volume": 1000000,
                "price_change_24h": 2.5
            }
            
            result = await self.ai_manager.analyze_market_sentiment(test_market_data)
            
            if result and "sentiment" in result:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "Market sentiment analysis completed",
                    "sentiment": result.get("sentiment"),
                    "confidence": result.get("confidence"),
                    "reasoning": result.get("reasoning")
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "Market sentiment analysis failed"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_trading_strategy_generation(self):
        """Test trading strategy generation."""
        test_name = "Trading Strategy Generation"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            test_analysis_data = {
                "symbol": "BTC/USDT",
                "technical": {
                    "signals": {"overall": "buy", "strength": 0.7},
                    "trend": {"direction": "bullish"},
                    "indicators": {"rsi": 65, "macd": 0.5}
                },
                "sentiment": {
                    "overall_label": "bullish",
                    "overall_score": 0.6
                }
            }
            
            result = await self.ai_manager.generate_trading_strategy(test_analysis_data)
            
            if result and "strategy" in result:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "Trading strategy generated successfully",
                    "strategy": result.get("strategy"),
                    "confidence": result.get("confidence"),
                    "reasoning": result.get("reasoning"),
                    "risk_level": result.get("risk_level")
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "Trading strategy generation failed"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_ai_agent_market_analysis(self):
        """Test AI Agent market analysis."""
        test_name = "AI Agent Market Analysis"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            # Mock market data
            market_data = {
                "symbol": "BTC/USDT",
                "current_price": 45000,
                "volume": 1000000,
                "price_change_24h": 2.5
            }
            
            # Mock technical analysis
            technical_analysis = {
                "indicators": {"rsi": 65, "macd": 0.5, "bb_upper": 46000, "bb_lower": 44000},
                "trend": {"direction": "bullish", "strength": "moderate"},
                "signals": {"overall": "buy", "strength": 0.7, "reasons": ["RSI favorable", "MACD bullish"]}
            }
            
            # Mock sentiment analysis
            sentiment_analysis = {
                "overall_score": 0.6,
                "overall_label": "bullish",
                "confidence": 0.8,
                "signals": {"signal": "buy", "strength": 0.6}
            }
            
            result = await self.ai_agent.analyze_market(
                "BTC/USDT", market_data, technical_analysis, sentiment_analysis
            )
            
            if result and "decision" in result:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "AI Agent market analysis completed",
                    "decision": result.get("decision"),
                    "confidence": result.get("confidence"),
                    "reasoning": result.get("reasoning")
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "AI Agent market analysis failed"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_technical_analyzer(self):
        """Test Technical Analyzer."""
        test_name = "Technical Analyzer"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            # Mock OHLCV data (timestamp, open, high, low, close, volume)
            mock_ohlcv = []
            base_price = 45000
            for i in range(50):
                timestamp = 1640995200000 + (i * 3600000)  # Hourly data
                open_price = base_price + (i * 10)
                high_price = open_price + 100
                low_price = open_price - 100
                close_price = open_price + (i % 2) * 50
                volume = 1000 + (i * 10)
                mock_ohlcv.append([timestamp, open_price, high_price, low_price, close_price, volume])
            
            result = await self.technical_analyzer.analyze_symbol(
                "BTC/USDT", "1h", mock_ohlcv
            )
            
            if result and "indicators" in result:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "Technical analysis completed",
                    "indicators_count": len(result.get("indicators", {})),
                    "has_signals": "signals" in result,
                    "has_trend": "trend" in result
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "Technical analysis failed"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def test_sentiment_analyzer(self):
        """Test Sentiment Analyzer."""
        test_name = "Sentiment Analyzer"
        self.logger.info(f"Testing {test_name}...")
        
        try:
            result = await self.sentiment_analyzer.analyze_sentiment("BTC/USDT")
            
            if result and "overall_score" in result:
                self.test_results[test_name] = {
                    "status": "PASS",
                    "details": "Sentiment analysis completed",
                    "overall_score": result.get("overall_score"),
                    "overall_label": result.get("overall_label"),
                    "confidence": result.get("confidence")
                }
                self.logger.info(f"✅ {test_name} PASSED")
            else:
                self.test_results[test_name] = {
                    "status": "FAIL",
                    "details": "Sentiment analysis failed"
                }
                self.logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "ERROR",
                "details": f"Exception during test: {str(e)}"
            }
            self.logger.error(f"❌ {test_name} ERROR: {e}")
    
    async def run_all_tests(self):
        """Run all AI function tests."""
        self.logger.info("🚀 Starting AI Functions Test Suite...")
        
        # Initialize components
        if not await self.initialize_components():
            self.logger.error("Failed to initialize components. Aborting tests.")
            return
        
        # Run tests
        await self.test_ai_manager_connection()
        await self.test_ai_prediction_generation()
        await self.test_market_sentiment_analysis()
        await self.test_trading_strategy_generation()
        await self.test_ai_agent_market_analysis()
        await self.test_technical_analyzer()
        await self.test_sentiment_analyzer()
        
        # Print results
        self.print_test_results()
    
    def print_test_results(self):
        """Print comprehensive test results."""
        self.logger.info("\n" + "="*80)
        self.logger.info("AI FUNCTIONS TEST RESULTS")
        self.logger.info("="*80)
        
        passed = 0
        failed = 0
        errors = 0
        
        for test_name, result in self.test_results.items():
            status = result["status"]
            details = result["details"]
            
            if status == "PASS":
                self.logger.info(f"✅ {test_name}: PASSED")
                passed += 1
            elif status == "FAIL":
                self.logger.info(f"❌ {test_name}: FAILED - {details}")
                failed += 1
            else:  # ERROR
                self.logger.info(f"💥 {test_name}: ERROR - {details}")
                errors += 1
            
            # Print additional details for passed tests
            if status == "PASS" and len(result) > 2:
                for key, value in result.items():
                    if key not in ["status", "details"]:
                        self.logger.info(f"   {key}: {value}")
        
        self.logger.info("-"*80)
        self.logger.info(f"SUMMARY: {passed} PASSED, {failed} FAILED, {errors} ERRORS")
        self.logger.info(f"SUCCESS RATE: {(passed/(passed+failed+errors)*100):.1f}%")
        self.logger.info("="*80)


async def main():
    """Main test function."""
    tester = AIFunctionTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
