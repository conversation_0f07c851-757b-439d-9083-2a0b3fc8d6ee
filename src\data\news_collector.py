"""
News Data Collector for AI Trading Bot System.

This module collects cryptocurrency and financial news from various sources
and performs sentiment analysis for trading insights.

Author: inkbytefo
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.settings import Settings
from ..config.database import get_db_manager
from ..config.models import NewsArticle
from ..monitoring.metrics import get_metrics_collector


@dataclass
class NewsData:
    """Normalized news data structure."""
    title: str
    content: str
    source: str
    url: str
    published_at: datetime
    author: Optional[str] = None
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None
    keywords: Optional[List[str]] = None


class NewsCollector:
    """
    Collects cryptocurrency and financial news from multiple sources.
    
    Integrates with various news APIs and RSS feeds to gather
    relevant market information for sentiment analysis.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.db_manager = get_db_manager()
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Collection state
        self.is_running = False
        self.collection_tasks: List[asyncio.Task] = []
        
        # Configuration
        self.collection_interval = 300  # 5 minutes
        self.max_articles_per_source = 50
        
        # News sources configuration
        self.news_sources = {
            'newsapi': {
                'enabled': bool(settings.news.news_api_key),
                'api_key': settings.news.news_api_key,
                'base_url': 'https://newsapi.org/v2',
                'endpoints': {
                    'everything': '/everything',
                    'top_headlines': '/top-headlines'
                }
            },
            'coindesk': {
                'enabled': True,
                'rss_url': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
                'base_url': 'https://www.coindesk.com'
            },
            'cointelegraph': {
                'enabled': True,
                'rss_url': 'https://cointelegraph.com/rss',
                'base_url': 'https://cointelegraph.com'
            },
            'cryptonews': {
                'enabled': True,
                'rss_url': 'https://cryptonews.com/news/feed/',
                'base_url': 'https://cryptonews.com'
            }
        }
        
        # Keywords for filtering relevant news
        self.crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'cryptocurrency', 'crypto',
            'blockchain', 'defi', 'nft', 'altcoin', 'trading', 'exchange',
            'binance', 'coinbase', 'kraken', 'regulation', 'adoption',
            'institutional', 'whale', 'market', 'price', 'bull', 'bear'
        ]
        
        # Statistics
        self.collection_stats = {
            'total_articles': 0,
            'processed_articles': 0,
            'failed_articles': 0,
            'last_collection': None,
            'sources': {}
        }
    
    async def initialize(self):
        """Initialize the news collector."""
        try:
            self.logger.info("Initializing News Collector...")
            
            # Initialize statistics for each source
            for source_name in self.news_sources:
                self.collection_stats['sources'][source_name] = {
                    'articles': 0,
                    'errors': 0,
                    'last_update': None
                }
            
            self.logger.info("✅ News Collector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize News Collector: {e}")
            raise
    
    async def start(self):
        """Start news collection."""
        try:
            if self.is_running:
                self.logger.warning("News collection already running")
                return
            
            self.logger.info("🚀 Starting news collection...")
            self.is_running = True
            
            # Start collection tasks for each enabled source
            for source_name, config in self.news_sources.items():
                if config.get('enabled', False):
                    task = asyncio.create_task(
                        self._collection_loop(source_name)
                    )
                    self.collection_tasks.append(task)
            
            # Start statistics monitoring
            asyncio.create_task(self._stats_monitoring_loop())
            
            self.logger.info("✅ News collection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start news collection: {e}")
            raise
    
    async def stop(self):
        """Stop news collection."""
        try:
            self.logger.info("🛑 Stopping news collection...")
            self.is_running = False
            
            # Cancel all collection tasks
            for task in self.collection_tasks:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            self.collection_tasks.clear()
            self.logger.info("✅ News collection stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping news collection: {e}")
    
    async def _collection_loop(self, source_name: str):
        """Main collection loop for a news source."""
        self.logger.info(f"Starting collection loop for {source_name}")
        
        while self.is_running:
            try:
                # Collect news from source
                articles = await self._collect_from_source(source_name)
                
                # Process and store articles
                for article in articles:
                    await self._process_and_store_article(article)
                
                # Update statistics
                self.collection_stats['sources'][source_name]['articles'] += len(articles)
                self.collection_stats['sources'][source_name]['last_update'] = datetime.utcnow()
                
                # Wait before next collection
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in collection loop for {source_name}: {e}")
                self.collection_stats['sources'][source_name]['errors'] += 1
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _collect_from_source(self, source_name: str) -> List[NewsData]:
        """Collect news from a specific source."""
        try:
            if source_name == 'newsapi':
                return await self._collect_from_newsapi()
            elif source_name in ['coindesk', 'cointelegraph', 'cryptonews']:
                return await self._collect_from_rss(source_name)
            else:
                self.logger.warning(f"Unknown news source: {source_name}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to collect from {source_name}: {e}")
            return []
    
    async def _collect_from_newsapi(self) -> List[NewsData]:
        """Collect news from NewsAPI."""
        try:
            config = self.news_sources['newsapi']
            if not config['enabled']:
                return []
            
            articles = []
            
            async with aiohttp.ClientSession() as session:
                # Search for cryptocurrency news
                params = {
                    'q': 'cryptocurrency OR bitcoin OR ethereum',
                    'language': 'en',
                    'sortBy': 'publishedAt',
                    'pageSize': self.max_articles_per_source,
                    'apiKey': config['api_key']
                }
                
                url = f"{config['base_url']}{config['endpoints']['everything']}"
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for article_data in data.get('articles', []):
                            article = NewsData(
                                title=article_data.get('title', ''),
                                content=article_data.get('description', '') or article_data.get('content', ''),
                                source=article_data.get('source', {}).get('name', 'NewsAPI'),
                                url=article_data.get('url', ''),
                                published_at=datetime.fromisoformat(
                                    article_data.get('publishedAt', '').replace('Z', '+00:00')
                                ),
                                author=article_data.get('author')
                            )
                            
                            # Filter for relevance
                            if self._is_relevant_article(article):
                                articles.append(article)
                    
                    else:
                        self.logger.error(f"NewsAPI request failed: {response.status}")
            
            self.logger.info(f"Collected {len(articles)} articles from NewsAPI")
            return articles
            
        except Exception as e:
            self.logger.error(f"Error collecting from NewsAPI: {e}")
            return []
    
    async def _collect_from_rss(self, source_name: str) -> List[NewsData]:
        """Collect news from RSS feeds."""
        try:
            try:
                import feedparser
            except ImportError:
                self.logger.error("feedparser not installed. Install with: pip install feedparser")
                return []
            
            config = self.news_sources[source_name]
            rss_url = config['rss_url']
            
            # Parse RSS feed
            feed = feedparser.parse(rss_url)
            articles = []
            
            for entry in feed.entries[:self.max_articles_per_source]:
                try:
                    # Parse published date
                    published_at = datetime.now()
                    if hasattr(entry, 'published_parsed') and entry.published_parsed:
                        import time
                        published_at = datetime.fromtimestamp(time.mktime(entry.published_parsed))
                    
                    article = NewsData(
                        title=entry.get('title', ''),
                        content=entry.get('summary', '') or entry.get('description', ''),
                        source=source_name,
                        url=entry.get('link', ''),
                        published_at=published_at,
                        author=entry.get('author')
                    )
                    
                    # Filter for relevance
                    if self._is_relevant_article(article):
                        articles.append(article)
                        
                except Exception as e:
                    self.logger.error(f"Error parsing RSS entry: {e}")
                    continue
            
            self.logger.info(f"Collected {len(articles)} articles from {source_name}")
            return articles
            
        except Exception as e:
            self.logger.error(f"Error collecting from RSS {source_name}: {e}")
            return []
    
    def _is_relevant_article(self, article: NewsData) -> bool:
        """Check if article is relevant to cryptocurrency trading."""
        try:
            # Combine title and content for keyword search
            text = f"{article.title} {article.content}".lower()
            
            # Check for crypto keywords
            for keyword in self.crypto_keywords:
                if keyword in text:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking article relevance: {e}")
            return False
    
    async def _process_and_store_article(self, article: NewsData):
        """Process and store article in database."""
        try:
            # Calculate sentiment score (basic implementation)
            sentiment_score = await self._calculate_sentiment(article)
            article.sentiment_score = sentiment_score
            
            # Calculate relevance score
            relevance_score = self._calculate_relevance(article)
            article.relevance_score = relevance_score
            
            # Extract keywords
            keywords = self._extract_keywords(article)
            article.keywords = keywords
            
            # Store in database
            with self.db_manager.session_scope() as session:
                # Check if article already exists
                existing_article = session.query(NewsArticle).filter_by(
                    url=article.url
                ).first()
                
                if existing_article:
                    # Update existing article
                    existing_article.sentiment_score = article.sentiment_score
                    existing_article.relevance_score = article.relevance_score
                    existing_article.updated_at = datetime.utcnow()
                else:
                    # Create new article
                    news_article = NewsArticle(
                        title=article.title,
                        content=article.content,
                        source=article.source,
                        url=article.url,
                        published_at=article.published_at,
                        author=article.author,
                        sentiment_score=article.sentiment_score,
                        relevance_score=article.relevance_score,
                        keywords=','.join(article.keywords) if article.keywords else None
                    )
                    session.add(news_article)
                
                session.commit()
            
            # Record metrics
            self.metrics.record_custom_metric("news_articles_processed", 1)
            self.collection_stats['processed_articles'] += 1
            
        except Exception as e:
            self.logger.error(f"Error processing article: {e}")
            self.collection_stats['failed_articles'] += 1
    
    async def _calculate_sentiment(self, article: NewsData) -> float:
        """Calculate sentiment score for article (basic implementation)."""
        try:
            # Simple sentiment analysis using keyword matching
            # In production, you would use a proper NLP library like VADER or TextBlob
            
            positive_words = [
                'bullish', 'bull', 'rise', 'surge', 'pump', 'moon', 'adoption',
                'institutional', 'breakthrough', 'partnership', 'growth',
                'positive', 'optimistic', 'rally', 'breakout', 'uptrend'
            ]
            
            negative_words = [
                'bearish', 'bear', 'crash', 'dump', 'fall', 'decline', 'drop',
                'regulation', 'ban', 'hack', 'scam', 'bubble', 'correction',
                'negative', 'pessimistic', 'sell-off', 'downtrend', 'fear'
            ]
            
            text = f"{article.title} {article.content}".lower()
            
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)
            
            # Calculate sentiment score (-1 to 1)
            total_words = positive_count + negative_count
            if total_words == 0:
                return 0.0
            
            sentiment = (positive_count - negative_count) / total_words
            return max(-1.0, min(1.0, sentiment))
            
        except Exception as e:
            self.logger.error(f"Error calculating sentiment: {e}")
            return 0.0
    
    def _calculate_relevance(self, article: NewsData) -> float:
        """Calculate relevance score for article."""
        try:
            text = f"{article.title} {article.content}".lower()
            
            # Weight different keywords by importance
            keyword_weights = {
                'bitcoin': 1.0, 'btc': 1.0,
                'ethereum': 0.9, 'eth': 0.9,
                'trading': 0.8, 'price': 0.8,
                'market': 0.7, 'exchange': 0.7,
                'cryptocurrency': 0.6, 'crypto': 0.6,
                'blockchain': 0.5, 'defi': 0.5
            }
            
            relevance_score = 0.0
            for keyword, weight in keyword_weights.items():
                if keyword in text:
                    relevance_score += weight
            
            # Normalize to 0-1 range
            max_possible_score = sum(keyword_weights.values())
            return min(1.0, relevance_score / max_possible_score)
            
        except Exception as e:
            self.logger.error(f"Error calculating relevance: {e}")
            return 0.0
    
    def _extract_keywords(self, article: NewsData) -> List[str]:
        """Extract relevant keywords from article."""
        try:
            text = f"{article.title} {article.content}".lower()
            found_keywords = []
            
            for keyword in self.crypto_keywords:
                if keyword in text:
                    found_keywords.append(keyword)
            
            return found_keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    async def _stats_monitoring_loop(self):
        """Monitor collection statistics."""
        while self.is_running:
            try:
                # Log statistics
                total_articles = self.collection_stats['processed_articles']
                failed_articles = self.collection_stats['failed_articles']
                
                if total_articles > 0:
                    success_rate = (total_articles / (total_articles + failed_articles)) * 100
                    self.logger.info(
                        f"📰 News Collection Stats: "
                        f"{total_articles} processed, "
                        f"{success_rate:.1f}% success rate"
                    )
                
                # Record metrics
                self.metrics.record_custom_metric("news_success_rate", success_rate if total_articles > 0 else 0)
                
                await asyncio.sleep(600)  # Report every 10 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in stats monitoring: {e}")
                await asyncio.sleep(60)
    
    async def get_recent_news(self, hours: int = 24, min_relevance: float = 0.3) -> List[Dict[str, Any]]:
        """Get recent news articles."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            with self.db_manager.session_scope() as session:
                articles = session.query(NewsArticle).filter(
                    NewsArticle.published_at >= cutoff_time,
                    NewsArticle.relevance_score >= min_relevance
                ).order_by(NewsArticle.published_at.desc()).limit(100).all()
                
                result = []
                for article in articles:
                    result.append({
                        'title': article.title,
                        'content': article.content,
                        'source': article.source,
                        'url': article.url,
                        'published_at': article.published_at,
                        'sentiment_score': float(article.sentiment_score) if article.sentiment_score else None,
                        'relevance_score': float(article.relevance_score) if article.relevance_score else None,
                        'keywords': article.keywords.split(',') if article.keywords else []
                    })
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error getting recent news: {e}")
            return []
    
    def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status."""
        return {
            'is_running': self.is_running,
            'enabled_sources': [name for name, config in self.news_sources.items() if config.get('enabled')],
            'collection_interval': self.collection_interval,
            'stats': self.collection_stats
        }
