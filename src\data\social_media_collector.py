"""
Social Media Data Collector for AI Trading Bot System.

This module collects cryptocurrency-related social media data from
Twitter, Reddit, and other platforms for sentiment analysis.

Author: inkbytefo
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.settings import Settings
from ..config.database import get_db_manager
from ..config.models import SocialMediaPost
from ..monitoring.metrics import get_metrics_collector


@dataclass
class SocialMediaData:
    """Normalized social media data structure."""
    platform: str
    post_id: str
    content: str
    author: str
    created_at: datetime
    url: str
    engagement_score: Optional[float] = None
    sentiment_score: Optional[float] = None
    influence_score: Optional[float] = None
    hashtags: Optional[List[str]] = None
    mentions: Optional[List[str]] = None


class SocialMediaCollector:
    """
    Collects cryptocurrency-related social media data.
    
    Integrates with Twitter API, Reddit API, and other social platforms
    to gather sentiment data for trading insights.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.db_manager = get_db_manager()
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Collection state
        self.is_running = False
        self.collection_tasks: List[asyncio.Task] = []
        
        # Configuration
        self.collection_interval = 180  # 3 minutes
        self.max_posts_per_platform = 100
        
        # Platform configurations
        self.platforms = {
            'twitter': {
                'enabled': bool(settings.social_media.twitter_bearer_token),
                'bearer_token': settings.social_media.twitter_bearer_token,
                'base_url': 'https://api.twitter.com/2',
                'endpoints': {
                    'search': '/tweets/search/recent',
                    'users': '/users/by/username'
                }
            },
            'reddit': {
                'enabled': bool(settings.social_media.reddit_client_id),
                'client_id': settings.social_media.reddit_client_id,
                'client_secret': settings.social_media.reddit_client_secret,
                'user_agent': 'AI Trading Bot v1.0',
                'base_url': 'https://www.reddit.com',
                'subreddits': ['cryptocurrency', 'bitcoin', 'ethereum', 'cryptomarkets', 'altcoin']
            }
        }
        
        # Search keywords and hashtags
        self.crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'cryptocurrency', 'crypto',
            'blockchain', 'defi', 'nft', 'altcoin', 'trading', 'hodl',
            'bullish', 'bearish', 'moon', 'diamond hands', 'paper hands'
        ]
        
        self.crypto_hashtags = [
            '#bitcoin', '#btc', '#ethereum', '#eth', '#crypto', '#cryptocurrency',
            '#blockchain', '#defi', '#nft', '#altcoin', '#trading', '#hodl'
        ]
        
        # Statistics
        self.collection_stats = {
            'total_posts': 0,
            'processed_posts': 0,
            'failed_posts': 0,
            'last_collection': None,
            'platforms': {}
        }
    
    async def initialize(self):
        """Initialize the social media collector."""
        try:
            self.logger.info("Initializing Social Media Collector...")
            
            # Initialize statistics for each platform
            for platform_name in self.platforms:
                self.collection_stats['platforms'][platform_name] = {
                    'posts': 0,
                    'errors': 0,
                    'last_update': None
                }
            
            self.logger.info("✅ Social Media Collector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Social Media Collector: {e}")
            raise
    
    async def start(self):
        """Start social media data collection."""
        try:
            if self.is_running:
                self.logger.warning("Social media collection already running")
                return
            
            self.logger.info("🚀 Starting social media collection...")
            self.is_running = True
            
            # Start collection tasks for each enabled platform
            for platform_name, config in self.platforms.items():
                if config.get('enabled', False):
                    task = asyncio.create_task(
                        self._collection_loop(platform_name)
                    )
                    self.collection_tasks.append(task)
            
            # Start statistics monitoring
            asyncio.create_task(self._stats_monitoring_loop())
            
            self.logger.info("✅ Social media collection started")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start social media collection: {e}")
            raise
    
    async def stop(self):
        """Stop social media data collection."""
        try:
            self.logger.info("🛑 Stopping social media collection...")
            self.is_running = False
            
            # Cancel all collection tasks
            for task in self.collection_tasks:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            self.collection_tasks.clear()
            self.logger.info("✅ Social media collection stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping social media collection: {e}")
    
    async def _collection_loop(self, platform_name: str):
        """Main collection loop for a social media platform."""
        self.logger.info(f"Starting collection loop for {platform_name}")
        
        while self.is_running:
            try:
                # Collect posts from platform
                posts = await self._collect_from_platform(platform_name)
                
                # Process and store posts
                for post in posts:
                    await self._process_and_store_post(post)
                
                # Update statistics
                self.collection_stats['platforms'][platform_name]['posts'] += len(posts)
                self.collection_stats['platforms'][platform_name]['last_update'] = datetime.utcnow()
                
                # Wait before next collection
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in collection loop for {platform_name}: {e}")
                self.collection_stats['platforms'][platform_name]['errors'] += 1
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _collect_from_platform(self, platform_name: str) -> List[SocialMediaData]:
        """Collect posts from a specific platform."""
        try:
            if platform_name == 'twitter':
                return await self._collect_from_twitter()
            elif platform_name == 'reddit':
                return await self._collect_from_reddit()
            else:
                self.logger.warning(f"Unknown platform: {platform_name}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to collect from {platform_name}: {e}")
            return []
    
    async def _collect_from_twitter(self) -> List[SocialMediaData]:
        """Collect tweets from Twitter API."""
        try:
            config = self.platforms['twitter']
            if not config['enabled']:
                return []
            
            posts = []
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f"Bearer {config['bearer_token']}",
                    'Content-Type': 'application/json'
                }
                
                # Search for crypto-related tweets
                query = ' OR '.join(self.crypto_keywords[:10])  # Limit query length
                params = {
                    'query': f"({query}) -is:retweet lang:en",
                    'max_results': min(self.max_posts_per_platform, 100),
                    'tweet.fields': 'created_at,author_id,public_metrics,context_annotations',
                    'user.fields': 'username,public_metrics'
                }
                
                url = f"{config['base_url']}{config['endpoints']['search']}"
                
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        tweets = data.get('data', [])
                        users = {user['id']: user for user in data.get('includes', {}).get('users', [])}
                        
                        for tweet in tweets:
                            user = users.get(tweet['author_id'], {})
                            
                            post = SocialMediaData(
                                platform='twitter',
                                post_id=tweet['id'],
                                content=tweet['text'],
                                author=user.get('username', 'unknown'),
                                created_at=datetime.fromisoformat(
                                    tweet['created_at'].replace('Z', '+00:00')
                                ),
                                url=f"https://twitter.com/{user.get('username', 'unknown')}/status/{tweet['id']}",
                                engagement_score=self._calculate_twitter_engagement(tweet),
                                influence_score=self._calculate_twitter_influence(user),
                                hashtags=self._extract_hashtags(tweet['text']),
                                mentions=self._extract_mentions(tweet['text'])
                            )
                            
                            posts.append(post)
                    
                    elif response.status == 429:
                        self.logger.warning("Twitter API rate limit exceeded")
                    else:
                        self.logger.error(f"Twitter API request failed: {response.status}")
            
            self.logger.info(f"Collected {len(posts)} tweets")
            return posts
            
        except Exception as e:
            self.logger.error(f"Error collecting from Twitter: {e}")
            return []
    
    async def _collect_from_reddit(self) -> List[SocialMediaData]:
        """Collect posts from Reddit API."""
        try:
            config = self.platforms['reddit']
            if not config['enabled']:
                return []
            
            posts = []
            
            # Get Reddit access token
            access_token = await self._get_reddit_access_token()
            if not access_token:
                return []
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f"Bearer {access_token}",
                    'User-Agent': config['user_agent']
                }
                
                # Collect from each subreddit
                for subreddit in config['subreddits']:
                    try:
                        url = f"https://oauth.reddit.com/r/{subreddit}/hot"
                        params = {
                            'limit': min(self.max_posts_per_platform // len(config['subreddits']), 25)
                        }
                        
                        async with session.get(url, headers=headers, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                for post_data in data.get('data', {}).get('children', []):
                                    post_info = post_data.get('data', {})
                                    
                                    # Filter for crypto-related content
                                    if self._is_crypto_related(post_info.get('title', '') + ' ' + post_info.get('selftext', '')):
                                        post = SocialMediaData(
                                            platform='reddit',
                                            post_id=post_info.get('id', ''),
                                            content=f"{post_info.get('title', '')} {post_info.get('selftext', '')}".strip(),
                                            author=post_info.get('author', 'unknown'),
                                            created_at=datetime.fromtimestamp(post_info.get('created_utc', 0)),
                                            url=f"https://reddit.com{post_info.get('permalink', '')}",
                                            engagement_score=self._calculate_reddit_engagement(post_info),
                                            influence_score=None  # Reddit doesn't provide user metrics easily
                                        )
                                        
                                        posts.append(post)
                            
                            else:
                                self.logger.error(f"Reddit API request failed for r/{subreddit}: {response.status}")
                    
                    except Exception as e:
                        self.logger.error(f"Error collecting from r/{subreddit}: {e}")
                        continue
            
            self.logger.info(f"Collected {len(posts)} Reddit posts")
            return posts
            
        except Exception as e:
            self.logger.error(f"Error collecting from Reddit: {e}")
            return []
    
    async def _get_reddit_access_token(self) -> Optional[str]:
        """Get Reddit API access token."""
        try:
            config = self.platforms['reddit']
            
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(config['client_id'], config['client_secret'])
                headers = {'User-Agent': config['user_agent']}
                data = {'grant_type': 'client_credentials'}
                
                async with session.post(
                    'https://www.reddit.com/api/v1/access_token',
                    auth=auth,
                    headers=headers,
                    data=data
                ) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        return token_data.get('access_token')
                    else:
                        self.logger.error(f"Failed to get Reddit access token: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"Error getting Reddit access token: {e}")
            return None
    
    def _calculate_twitter_engagement(self, tweet: Dict[str, Any]) -> float:
        """Calculate engagement score for a tweet."""
        try:
            metrics = tweet.get('public_metrics', {})
            
            likes = metrics.get('like_count', 0)
            retweets = metrics.get('retweet_count', 0)
            replies = metrics.get('reply_count', 0)
            quotes = metrics.get('quote_count', 0)
            
            # Weighted engagement score
            engagement = (likes * 1) + (retweets * 3) + (replies * 2) + (quotes * 2)
            
            # Normalize to 0-1 range (log scale for large numbers)
            import math
            return min(1.0, math.log10(engagement + 1) / 6)  # log10(1M) ≈ 6
            
        except Exception as e:
            self.logger.error(f"Error calculating Twitter engagement: {e}")
            return 0.0
    
    def _calculate_twitter_influence(self, user: Dict[str, Any]) -> float:
        """Calculate influence score for a Twitter user."""
        try:
            metrics = user.get('public_metrics', {})
            
            followers = metrics.get('followers_count', 0)
            following = metrics.get('following_count', 1)  # Avoid division by zero
            tweets = metrics.get('tweet_count', 0)
            
            # Calculate influence based on follower ratio and activity
            follower_ratio = followers / following if following > 0 else 0
            activity_score = min(1.0, tweets / 10000)  # Normalize tweet count
            
            # Weighted influence score
            influence = (follower_ratio * 0.7) + (activity_score * 0.3)
            
            # Normalize to 0-1 range
            import math
            return min(1.0, math.log10(influence + 1) / 4)
            
        except Exception as e:
            self.logger.error(f"Error calculating Twitter influence: {e}")
            return 0.0
    
    def _calculate_reddit_engagement(self, post: Dict[str, Any]) -> float:
        """Calculate engagement score for a Reddit post."""
        try:
            score = post.get('score', 0)
            comments = post.get('num_comments', 0)
            
            # Weighted engagement score
            engagement = (score * 1) + (comments * 2)
            
            # Normalize to 0-1 range
            import math
            return min(1.0, math.log10(abs(engagement) + 1) / 5)
            
        except Exception as e:
            self.logger.error(f"Error calculating Reddit engagement: {e}")
            return 0.0
    
    def _is_crypto_related(self, text: str) -> bool:
        """Check if text is crypto-related."""
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in self.crypto_keywords)
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """Extract hashtags from text."""
        import re
        hashtags = re.findall(r'#\w+', text.lower())
        return [tag for tag in hashtags if any(keyword in tag for keyword in self.crypto_keywords)]
    
    def _extract_mentions(self, text: str) -> List[str]:
        """Extract mentions from text."""
        import re
        return re.findall(r'@\w+', text)
    
    async def _process_and_store_post(self, post: SocialMediaData):
        """Process and store social media post."""
        try:
            # Calculate sentiment score
            sentiment_score = await self._calculate_sentiment(post)
            post.sentiment_score = sentiment_score
            
            # Store in database
            with self.db_manager.session_scope() as session:
                # Check if post already exists
                existing_post = session.query(SocialMediaPost).filter_by(
                    platform=post.platform,
                    post_id=post.post_id
                ).first()
                
                if existing_post:
                    # Update existing post
                    existing_post.engagement_score = post.engagement_score
                    existing_post.sentiment_score = post.sentiment_score
                    existing_post.updated_at = datetime.utcnow()
                else:
                    # Create new post
                    social_post = SocialMediaPost(
                        platform=post.platform,
                        post_id=post.post_id,
                        content=post.content,
                        author=post.author,
                        created_at=post.created_at,
                        url=post.url,
                        engagement_score=post.engagement_score,
                        sentiment_score=post.sentiment_score,
                        influence_score=post.influence_score,
                        hashtags=','.join(post.hashtags) if post.hashtags else None,
                        mentions=','.join(post.mentions) if post.mentions else None
                    )
                    session.add(social_post)
                
                session.commit()
            
            # Record metrics
            self.metrics.record_custom_metric("social_posts_processed", 1)
            self.collection_stats['processed_posts'] += 1
            
        except Exception as e:
            self.logger.error(f"Error processing social media post: {e}")
            self.collection_stats['failed_posts'] += 1
    
    async def _calculate_sentiment(self, post: SocialMediaData) -> float:
        """Calculate sentiment score for social media post."""
        try:
            # Simple sentiment analysis (same as news collector)
            positive_words = [
                'bullish', 'bull', 'moon', 'pump', 'hodl', 'diamond hands',
                'buy', 'long', 'rally', 'breakout', 'surge', 'rocket'
            ]
            
            negative_words = [
                'bearish', 'bear', 'dump', 'crash', 'paper hands', 'sell',
                'short', 'rekt', 'fud', 'scam', 'bubble', 'correction'
            ]
            
            text = post.content.lower()
            
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)
            
            # Calculate sentiment score (-1 to 1)
            total_words = positive_count + negative_count
            if total_words == 0:
                return 0.0
            
            sentiment = (positive_count - negative_count) / total_words
            return max(-1.0, min(1.0, sentiment))
            
        except Exception as e:
            self.logger.error(f"Error calculating sentiment: {e}")
            return 0.0
    
    async def _stats_monitoring_loop(self):
        """Monitor collection statistics."""
        while self.is_running:
            try:
                # Log statistics
                total_posts = self.collection_stats['processed_posts']
                failed_posts = self.collection_stats['failed_posts']
                
                if total_posts > 0:
                    success_rate = (total_posts / (total_posts + failed_posts)) * 100
                    self.logger.info(
                        f"📱 Social Media Stats: "
                        f"{total_posts} processed, "
                        f"{success_rate:.1f}% success rate"
                    )
                
                # Record metrics
                self.metrics.record_custom_metric("social_success_rate", success_rate if total_posts > 0 else 0)
                
                await asyncio.sleep(600)  # Report every 10 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in stats monitoring: {e}")
                await asyncio.sleep(60)
    
    def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status."""
        return {
            'is_running': self.is_running,
            'enabled_platforms': [name for name, config in self.platforms.items() if config.get('enabled')],
            'collection_interval': self.collection_interval,
            'stats': self.collection_stats
        }
