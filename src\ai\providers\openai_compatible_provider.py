"""
Modern OpenAI Provider Implementation.

This module provides a modern OpenAI provider using the official OpenAI Python SDK v1.99.1
with support for multiple providers through base_url configuration.

Author: inkbytefo
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

from openai import AsyncOpenAI, OpenAI
from openai.types.chat import Chat<PERSON><PERSON>pletion, ChatCompletionMessage
from openai._exceptions import APIError, RateLimitError, APIConnectionError
from .base_provider import BaseAIProvider, AIRequest, AIResponse, ProviderConfig, ProviderType


class ModernOpenAIProvider(BaseAIProvider):
    """
    Modern OpenAI provider using the official OpenAI Python SDK v1.99.1.

    Supports OpenAI API and compatible providers through base_url configuration.
    Uses the latest chat completions API with proper async support.
    """
    
    def __init__(self, config: ProviderConfig, settings):
        super().__init__(config, settings)

        # Initialize OpenAI clients
        self.client = None
        self.async_client = None

        # Default cost mapping (can be overridden in config)
        self.default_costs = {
            'gpt-4': {'input': 0.03, 'output': 0.06},
            'gpt-4-turbo': {'input': 0.01, 'output': 0.03},
            'gpt-3.5-turbo': {'input': 0.001, 'output': 0.002},
            'claude-3-opus': {'input': 0.015, 'output': 0.075},
            'claude-3-sonnet': {'input': 0.003, 'output': 0.015},
            'claude-3-haiku': {'input': 0.00025, 'output': 0.00125},
            'gemini-pro': {'input': 0.0005, 'output': 0.0015},
            'local': {'input': 0.0, 'output': 0.0}
        }

    async def initialize(self):
        """Initialize the OpenAI clients."""
        try:
            # Initialize async client
            self.async_client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout_seconds
            )

            # Initialize sync client (for compatibility)
            self.client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout_seconds
            )

            # Test connection
            await self._test_connection()
            self.logger.info(f"Successfully initialized {self.config.name} provider")

        except Exception as e:
            self.logger.error(f"Failed to initialize {self.config.name} provider: {e}")
            raise

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests."""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': f'AI-Trading-Bot/1.0 ({self.config.name})'
        }
        
        # Add authorization header based on provider type
        if self.config.provider_type == ProviderType.OPENAI:
            headers['Authorization'] = f'Bearer {self.config.api_key}'
        elif self.config.provider_type == ProviderType.AZURE:
            headers['api-key'] = self.config.api_key
        elif self.config.provider_type == ProviderType.ANTHROPIC:
            headers['x-api-key'] = self.config.api_key
            headers['anthropic-version'] = '2023-06-01'
        elif self.config.provider_type == ProviderType.GOOGLE:
            headers['Authorization'] = f'Bearer {self.config.api_key}'
        elif self.config.api_key:  # Custom or local with API key
            headers['Authorization'] = f'Bearer {self.config.api_key}'
        
        return headers
    
    async def _test_connection(self):
        """Test the connection to the provider using OpenAI client."""
        try:
            # Test with a simple models list call
            models = await self.async_client.models.list()
            if models.data:
                self.logger.info(f"Connection test successful for {self.config.name}")
            else:
                self.logger.warning(f"Connection test returned no models for {self.config.name}")

        except Exception as e:
            self.logger.warning(f"Connection test failed for {self.config.name}: {e}")
            # Don't raise exception - some providers might not have models endpoint
    
    def _map_model_name(self, model: str) -> str:
        """Map generic model name to provider-specific model name."""
        if model in self.config.model_mapping:
            return self.config.model_mapping[model]
        return model
    
    def _calculate_cost(self, usage: Dict[str, int], model: str) -> float:
        """Calculate the cost of the request."""
        if not usage:
            return 0.0
        
        # Get cost configuration
        cost_config = self.config.cost_per_1k_tokens or self.default_costs.get(model, {'input': 0.0, 'output': 0.0})
        
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        
        input_cost = (input_tokens / 1000) * cost_config.get('input', 0.0)
        output_cost = (output_tokens / 1000) * cost_config.get('output', 0.0)
        
        return input_cost + output_cost
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """Make the actual API request using modern OpenAI client."""
        # Map model name
        mapped_model = self._map_model_name(request.model)

        try:
            # Prepare request parameters
            params = {
                'model': mapped_model,
                'messages': request.messages,
                'temperature': request.temperature,
                'stream': request.stream
            }

            if request.max_tokens:
                params['max_tokens'] = request.max_tokens

            # Use modern OpenAI client
            completion = await self.async_client.chat.completions.create(**params)

            # Parse response
            return self._parse_openai_response(completion, mapped_model)

        except RateLimitError as e:
            raise Exception(f"Rate limit exceeded: {e}")
        except APIConnectionError as e:
            raise Exception(f"Connection error: {e}")
        except APIError as e:
            raise Exception(f"API error: {e}")
        except Exception as e:
            raise Exception(f"Unexpected error: {e}")

    async def cleanup(self):
        """Cleanup provider resources."""
        try:
            if self.async_client:
                await self.async_client.close()
            if self.client:
                self.client.close()
            self.logger.info(f"Provider {self.config.name} cleaned up successfully")
        except Exception as e:
            self.logger.warning(f"Cleanup warning for {self.config.name}: {e}")

    def _parse_openai_response(self, completion: ChatCompletion, model: str) -> AIResponse:
        """Parse OpenAI chat completion response."""
        # Check if completion has choices
        if not completion or not completion.choices:
            raise Exception("No choices in completion response")

        # Extract content
        content = completion.choices[0].message.content or ""

        # Extract usage information
        usage = {
            'prompt_tokens': completion.usage.prompt_tokens if completion.usage else 0,
            'completion_tokens': completion.usage.completion_tokens if completion.usage else 0,
            'total_tokens': completion.usage.total_tokens if completion.usage else 0
        }

        # Calculate cost
        cost = self._calculate_cost(usage, model)

        # Calculate latency (approximate)
        latency_ms = 1000  # Default value, actual latency tracking would need more work

        return AIResponse(
            content=content,
            model=model,
            usage=usage,
            finish_reason=completion.choices[0].finish_reason if completion.choices else "stop",
            cost_usd=cost,
            latency_ms=latency_ms,
            timestamp=datetime.now(timezone.utc),
            metadata={
                'id': completion.id,
                'created': completion.created
            }
        )

    def _adapt_for_anthropic(self, payload: Dict) -> Dict:
        """Adapt payload for Anthropic Claude API."""
        # Anthropic uses different parameter names
        adapted = payload.copy()
        
        if 'max_tokens' not in adapted:
            adapted['max_tokens'] = 1000  # Anthropic requires max_tokens
        
        return adapted
    
    def _adapt_for_google(self, payload: Dict) -> Dict:
        """Adapt payload for Google Gemini API."""
        # Google might have different parameter requirements
        adapted = payload.copy()
        
        # Convert messages format if needed
        # (This would depend on the specific Google API format)
        
        return adapted
    
    def _parse_response(self, response_data: Dict, model: str) -> AIResponse:
        """Parse the API response into our standard format."""
        try:
            choice = response_data['choices'][0]
            message = choice['message']
            
            # Extract content
            content = message.get('content', '')
            
            # Extract function call if present
            function_call = message.get('function_call')
            
            # Extract usage information
            usage = response_data.get('usage', {})
            
            # Extract finish reason
            finish_reason = choice.get('finish_reason', 'unknown')
            
            return AIResponse(
                content=content,
                model=model,
                usage=usage,
                finish_reason=finish_reason,
                function_call=function_call,
                metadata=response_data.get('metadata', {})
            )
            
        except KeyError as e:
            raise Exception(f"Invalid response format: missing key {e}")
        except Exception as e:
            raise Exception(f"Failed to parse response: {e}")


class ProviderFactory:
    """Factory for creating modern OpenAI providers."""

    @staticmethod
    def create_provider(config: ProviderConfig, settings) -> ModernOpenAIProvider:
        """Create a provider instance based on configuration."""
        return ModernOpenAIProvider(config, settings)

    @staticmethod
    def create_openai_provider(api_key: str, settings, base_url: str = "https://api.openai.com",
                              model_mapping: Dict[str, str] = None) -> ModernOpenAIProvider:
        """Create an OpenAI provider."""
        config = ProviderConfig(
            name="openai",
            provider_type=ProviderType.OPENAI,
            api_key=api_key,
            base_url=base_url,
            model_mapping=model_mapping or {
                'gpt-4': 'gpt-4',
                'gpt-4-turbo': 'gpt-4-turbo-preview',
                'gpt-3.5': 'gpt-3.5-turbo',
                'gpt-3.5-turbo': 'gpt-3.5-turbo'
            },
            rate_limit_rpm=60,
            rate_limit_tpm=10000
        )
        return ModernOpenAIProvider(config, settings)

    @staticmethod
    def create_azure_provider(api_key: str, base_url: str, settings, model_mapping: Dict[str, str] = None) -> ModernOpenAIProvider:
        """Create an Azure OpenAI provider."""
        config = ProviderConfig(
            name="azure",
            provider_type=ProviderType.AZURE,
            api_key=api_key,
            base_url=base_url,
            model_mapping=model_mapping or {
                'gpt-4': 'gpt-4',
                'gpt-3.5': 'gpt-35-turbo'
            },
            rate_limit_rpm=60,
            rate_limit_tpm=10000
        )
        return ModernOpenAIProvider(config, settings)

    @staticmethod
    def create_local_provider(base_url: str, settings, model_mapping: Dict[str, str] = None) -> ModernOpenAIProvider:
        """Create a local provider (Ollama, LM Studio, etc.)."""
        config = ProviderConfig(
            name="local",
            provider_type=ProviderType.LOCAL,
            api_key="",  # Local providers usually don't need API key
            base_url=base_url,
            model_mapping=model_mapping or {
                'gpt-4': 'llama2:7b',
                'gpt-3.5': 'llama2:7b'
            },
            rate_limit_rpm=1000,  # Higher limits for local
            rate_limit_tpm=100000,
            cost_per_1k_tokens={'input': 0.0, 'output': 0.0}  # Free for local
        )
        return ModernOpenAIProvider(config, settings)

    @staticmethod
    def create_custom_provider(name: str, base_url: str, api_key: str, settings,
                             model_mapping: Dict[str, str] = None) -> ModernOpenAIProvider:
        """Create a custom provider."""
        config = ProviderConfig(
            name=name,
            provider_type=ProviderType.CUSTOM,
            api_key=api_key,
            base_url=base_url,
            model_mapping=model_mapping or {},
            rate_limit_rpm=60,
            rate_limit_tpm=10000
        )
        return ModernOpenAIProvider(config, settings)
