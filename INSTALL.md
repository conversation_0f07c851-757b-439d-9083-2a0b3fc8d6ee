# AI Trading Bot System - Ku<PERSON>lum Rehberi

Bu rehber, AI Trading Bot sistemini kurmak ve çalıştırmak için gerekli adımları açıklar.

## 📋 Gereksinimler

### Sistem Gereksinimleri
- **İşletim Sistemi**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **RAM**: Minimum 8GB (16GB önerilen)
- **Disk Alanı**: Minimum 10GB boş alan
- **İnternet**: Stabil internet bağlantısı

### Yazılım Gereksinimleri
- **Python**: 3.11 veya üzeri
- **Docker**: 20.10 veya üzeri
- **Docker Compose**: 2.0 veya üzeri
- **Git**: 2.30 veya üzeri

## 🚀 Hızlı Kurulum

### 1. <PERSON><PERSON>yi İndirin
```bash
git clone <repository-url>
cd ai-trading-bot
```

### 2. Otomatik Kurulum
```bash
python scripts/setup.py
```

Bu script otomatik olarak:
- Python bağımlılıklarını yükler
- Gerekli dizinleri oluşturur
- Docker konfigürasyonlarını hazırlar
- .env dosyasını oluşturur

### 3. Konfigürasyon
`.env` dosyasını düzenleyin ve gerekli API anahtarlarını ekleyin:

```bash
# Exchange API Keys
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# Database
DATABASE_URL=postgresql://tradingbot:password@localhost:5432/trading_bot

# Security
SECRET_KEY=your_super_secret_key
ENCRYPTION_KEY=your_encryption_key
```

### 4. Servisleri Başlatın
```bash
python scripts/dev.py start
```

## 📖 Detaylı Kurulum

### Adım 1: Sistem Hazırlığı

#### Windows
```powershell
# Chocolatey ile gerekli araçları yükleyin
choco install python docker-desktop git

# Docker Desktop'ı başlatın
```

#### macOS
```bash
# Homebrew ile gerekli araçları yükleyin
brew install python@3.11 docker git

# Docker Desktop'ı yükleyin
brew install --cask docker
```

#### Ubuntu/Debian
```bash
# Sistem paketlerini güncelleyin
sudo apt update && sudo apt upgrade -y

# Python 3.11 yükleyin
sudo apt install python3.11 python3.11-pip python3.11-venv

# Docker yükleyin
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose yükleyin
sudo apt install docker-compose-plugin
```

### Adım 2: Proje Kurulumu

```bash
# Projeyi klonlayın
git clone <repository-url>
cd ai-trading-bot

# Python sanal ortamı oluşturun
python -m venv venv

# Sanal ortamı aktifleştirin
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Bağımlılıkları yükleyin
pip install -r requirements.txt
```

### Adım 3: Veritabanı Kurulumu

```bash
# Veritabanını başlatın
docker-compose up -d postgres redis

# Veritabanı tablolarını oluşturun
python scripts/migrate.py init
```

### Adım 4: Konfigürasyon Detayları

#### Exchange API Anahtarları

**Binance:**
1. [Binance](https://www.binance.com) hesabınıza giriş yapın
2. API Management > Create API seçin
3. API Key ve Secret Key'i kopyalayın
4. Testnet için: [Binance Testnet](https://testnet.binance.vision)

**Coinbase Pro:**
1. [Coinbase Pro](https://pro.coinbase.com) hesabınıza giriş yapın
2. API > New API Key seçin
3. View, Trade izinlerini verin
4. API Key, Secret, Passphrase'i kopyalayın

**Kraken:**
1. [Kraken](https://www.kraken.com) hesabınıza giriş yapın
2. Settings > API seçin
3. Generate New Key seçin
4. API Key ve Private Key'i kopyalayın

#### Güvenlik Ayarları

```bash
# Güçlü secret key oluşturun
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Encryption key oluşturun
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### Adım 5: Servisleri Başlatma

```bash
# Tüm servisleri başlatın
docker-compose up -d

# Servislerin durumunu kontrol edin
python scripts/dev.py status

# Logları görüntüleyin
python scripts/dev.py logs --follow
```

## 🔧 Geliştirme Ortamı

### Geliştirme Komutları

```bash
# Servisleri başlat
python scripts/dev.py start

# Servisleri durdur
python scripts/dev.py stop

# Servisleri yeniden başlat
python scripts/dev.py restart

# Sistem durumunu kontrol et
python scripts/dev.py status

# Testleri çalıştır
python scripts/dev.py test

# Logları görüntüle
python scripts/dev.py logs --service trading_bot --follow

# Geliştirme ortamını temizle
python scripts/dev.py clean
```

### Test Çalıştırma

```bash
# Tüm testleri çalıştır
pytest tests/ -v

# Belirli bir test dosyasını çalıştır
pytest tests/test_basic.py -v

# Coverage raporu ile
pytest tests/ --cov=src --cov-report=html
```

## 📊 Monitoring ve Dashboard

Sistem başlatıldıktan sonra aşağıdaki arayüzlere erişebilirsiniz:

- **Trading Bot API**: http://localhost:8000
- **Streamlit Dashboard**: http://localhost:8501
- **Grafana Monitoring**: http://localhost:3000 (admin/admin123)
- **Prometheus Metrics**: http://localhost:9090

## 🔒 Güvenlik

### Üretim Ortamı İçin

1. **API Anahtarları**: Gerçek API anahtarlarını kullanmadan önce testnet'te test edin
2. **HTTPS**: Üretimde SSL sertifikası kullanın
3. **Firewall**: Sadece gerekli portları açın
4. **Backup**: Düzenli veri yedeklemesi yapın
5. **Monitoring**: Sistem metriklerini sürekli izleyin

### Güvenlik Kontrol Listesi

- [ ] API anahtarları şifrelenmiş olarak saklanıyor
- [ ] Strong secret keys kullanılıyor
- [ ] Database bağlantıları güvenli
- [ ] Log dosyaları hassas bilgi içermiyor
- [ ] Rate limiting aktif
- [ ] SSL/TLS sertifikaları geçerli

## 🐛 Sorun Giderme

### Yaygın Sorunlar

**Docker servisleri başlamıyor:**
```bash
# Docker daemon'ın çalıştığını kontrol edin
docker --version
docker-compose --version

# Portların kullanımda olup olmadığını kontrol edin
netstat -tulpn | grep :5432
netstat -tulpn | grep :6379
```

**Veritabanı bağlantı hatası:**
```bash
# PostgreSQL servisini kontrol edin
docker-compose logs postgres

# Bağlantı ayarlarını kontrol edin
python scripts/migrate.py check
```

**API anahtarı hataları:**
```bash
# API anahtarlarının doğru olduğunu kontrol edin
# Testnet/sandbox modunda olduğunuzdan emin olun
# API izinlerinin yeterli olduğunu kontrol edin
```

### Log Dosyaları

```bash
# Uygulama logları
tail -f logs/trading_bot.log

# Docker servisleri logları
docker-compose logs --follow

# Sistem logları
journalctl -u docker
```

## 📞 Destek

Sorun yaşıyorsanız:

1. **Dokümantasyon**: `docs/` klasöründeki detaylı dokümantasyonu inceleyin
2. **Loglar**: Hata mesajlarını `logs/` klasöründe kontrol edin
3. **Test**: `python scripts/dev.py test` ile sistem testlerini çalıştırın
4. **GitHub Issues**: Sorunları GitHub repository'de bildirin

## 🎯 Sonraki Adımlar

Kurulum tamamlandıktan sonra:

1. **Konfigürasyon**: Trading stratejilerini `config/strategies.yml` dosyasında ayarlayın
2. **Backtesting**: Geçmiş verilerle stratejilerinizi test edin
3. **Paper Trading**: Gerçek para kullanmadan test edin
4. **Monitoring**: Grafana dashboard'larını özelleştirin
5. **Alerting**: Bildirim kanallarını yapılandırın

---

**⚠️ Önemli Uyarı**: Bu sistem eğitim ve araştırma amaçlıdır. Gerçek para ile işlem yapmadan önce kapsamlı testler yapın ve riskleri anlayın.
