#!/usr/bin/env python3
"""
AI Trading Bot System - Main Entry Point

This is the main entry point for the AI Trading Bot System.
It initializes all components and starts the trading engine.

Author: inkbytefo
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import Settings
from src.core.trading_engine import TradingEngine
from src.monitoring.logger import setup_logging
from src.monitoring.health_checker import HealthChecker


class TradingBotApplication:
    """Main application class for the AI Trading Bot."""
    
    def __init__(self):
        self.settings = Settings()
        self.trading_engine = None
        self.health_checker = None
        self.running = False
        self.logger = None
        
    async def startup(self):
        """Initialize and start all components."""
        try:
            # Setup basic logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('logs/trading_bot.log')
                ]
            )
            self.logger = logging.getLogger(__name__)

            # Create logs directory
            Path("logs").mkdir(exist_ok=True)
            
            self.logger.info("[START] Starting AI Trading Bot System...")
            self.logger.info(f"Version: {self.settings.VERSION}")
            self.logger.info(f"Environment: {self.settings.ENVIRONMENT}")
            self.logger.info(f"Paper Trading: {self.settings.PAPER_TRADING}")
            
            # Initialize health checker
            self.health_checker = HealthChecker(self.settings)
            await self.health_checker.start()
            
            # Initialize trading engine
            self.trading_engine = TradingEngine(self.settings)
            await self.trading_engine.initialize()
            
            # Start trading engine
            await self.trading_engine.start()
            
            self.running = True
            self.logger.info("[OK] AI Trading Bot System started successfully!")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"[ERROR] Failed to start trading bot: {e}")
            else:
                print(f"[ERROR] Failed to start trading bot: {e}")
            await self.shutdown()
            sys.exit(1)
    
    async def shutdown(self):
        """Gracefully shutdown all components."""
        if not self.running:
            return
            
        if self.logger:
            self.logger.info("[STOP] Shutting down AI Trading Bot System...")
        else:
            print("[STOP] Shutting down AI Trading Bot System...")
        self.running = False
        
        try:
            # Stop trading engine
            if self.trading_engine:
                await self.trading_engine.stop()
                
            # Stop health checker
            if self.health_checker:
                await self.health_checker.stop()
                
            if self.logger:
                self.logger.info("[OK] AI Trading Bot System shutdown complete!")
            else:
                print("[OK] AI Trading Bot System shutdown complete!")

        except Exception as e:
            if self.logger:
                self.logger.error(f"[ERROR] Error during shutdown: {e}")
            else:
                print(f"[ERROR] Error during shutdown: {e}")
    
    async def run(self):
        """Main run loop."""
        await self.startup()
        
        try:
            # Keep the application running
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            if self.logger:
                self.logger.info("Received interrupt signal")
            else:
                print("Received interrupt signal")
        finally:
            await self.shutdown()


def signal_handler(signum, frame):
    """Handle system signals for graceful shutdown."""
    print(f"\nReceived signal {signum}. Initiating graceful shutdown...")
    # This will be handled by the main loop
    

async def main():
    """Main function."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run the application
    app = TradingBotApplication()
    await app.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
