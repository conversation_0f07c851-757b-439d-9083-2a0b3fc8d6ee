groups:
  - name: trading_bot_alerts
    rules:
      # System alerts
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes. Current value: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes. Current value: {{ $value }}%"

      - alert: HighDiskUsage
        expr: system_disk_usage_percent > 90
        for: 10m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is above 90% for more than 10 minutes. Current value: {{ $value }}%"

      # Application alerts
      - alert: ApplicationDown
        expr: up{job="trading-bot"} == 0
        for: 1m
        labels:
          severity: critical
          component: application
        annotations:
          summary: "Trading bot application is down"
          description: "The trading bot application has been down for more than 1 minute"

      - alert: DatabaseConnectionFailed
        expr: database_active_connections == 0
        for: 2m
        labels:
          severity: critical
          component: database
        annotations:
          summary: "Database connection failed"
          description: "No active database connections detected for more than 2 minutes"

      # Trading alerts
      - alert: HighDrawdown
        expr: trading_drawdown_current_percent > 10
        for: 1m
        labels:
          severity: critical
          component: trading
        annotations:
          summary: "High drawdown detected"
          description: "Current drawdown is above 10%. Current value: {{ $value }}%"

      - alert: NoTradingActivity
        expr: increase(trading_orders_total[1h]) == 0
        for: 2h
        labels:
          severity: warning
          component: trading
        annotations:
          summary: "No trading activity"
          description: "No trading orders have been placed in the last 2 hours"

      - alert: HighOrderFailureRate
        expr: (rate(trading_orders_total{status="FAILED"}[5m]) / rate(trading_orders_total[5m])) > 0.1
        for: 5m
        labels:
          severity: warning
          component: trading
        annotations:
          summary: "High order failure rate"
          description: "Order failure rate is above 10% for the last 5 minutes"

      # AI/ML alerts
      - alert: LowModelAccuracy
        expr: ai_model_prediction_accuracy < 0.6
        for: 10m
        labels:
          severity: warning
          component: ai
        annotations:
          summary: "Low model prediction accuracy"
          description: "Model {{ $labels.model_name }} accuracy is below 60% for {{ $labels.timeframe }}. Current value: {{ $value }}"

      - alert: HighModelInferenceTime
        expr: ai_model_inference_seconds > 5
        for: 5m
        labels:
          severity: warning
          component: ai
        annotations:
          summary: "High model inference time"
          description: "Model {{ $labels.model_name }} inference time is above 5 seconds. Current value: {{ $value }}s"

      # Performance alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          component: performance
        annotations:
          summary: "High HTTP response time"
          description: "95th percentile response time is above 2 seconds for {{ $labels.endpoint }}"

      - alert: HighErrorRate
        expr: (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: warning
          component: performance
        annotations:
          summary: "High HTTP error rate"
          description: "HTTP 5xx error rate is above 5% for {{ $labels.endpoint }}"

  - name: business_alerts
    rules:
      # Business metric alerts
      - alert: LowWinRate
        expr: ai_strategy_win_rate_percent < 40
        for: 30m
        labels:
          severity: warning
          component: strategy
        annotations:
          summary: "Low strategy win rate"
          description: "Strategy {{ $labels.strategy_name }} win rate is below 40%. Current value: {{ $value }}%"

      - alert: NegativeSharpeRatio
        expr: ai_strategy_sharpe_ratio < 0
        for: 1h
        labels:
          severity: warning
          component: strategy
        annotations:
          summary: "Negative Sharpe ratio"
          description: "Strategy {{ $labels.strategy_name }} has negative Sharpe ratio. Current value: {{ $value }}"

      - alert: HighTradingFees
        expr: increase(business_trading_fees_usd[1d]) > 100
        for: 1h
        labels:
          severity: info
          component: business
        annotations:
          summary: "High daily trading fees"
          description: "Daily trading fees exceed $100. Current value: ${{ $value }}"

  - name: infrastructure_alerts
    rules:
      # Infrastructure alerts
      - alert: ContainerRestart
        expr: increase(container_restart_count[1h]) > 3
        for: 1m
        labels:
          severity: warning
          component: infrastructure
        annotations:
          summary: "Container restarting frequently"
          description: "Container {{ $labels.container }} has restarted {{ $value }} times in the last hour"

      - alert: HighNetworkLatency
        expr: market_data_latency_seconds > 1
        for: 5m
        labels:
          severity: warning
          component: network
        annotations:
          summary: "High market data latency"
          description: "Market data latency for {{ $labels.exchange }}/{{ $labels.pair }} is above 1 second. Current value: {{ $value }}s"
