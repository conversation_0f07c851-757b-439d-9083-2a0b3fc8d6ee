#!/usr/bin/env python3
"""
Real AI System Test with Modern OpenAI Integration.

This script tests the modernized AI system with real API keys to ensure
everything works correctly in production with the new OpenAI-based architecture.

Author: inkbytefo
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.ai.services.ai_service import AIService
from src.ai.providers.openai_compatible_provider import ModernOpenAIProvider, ProviderFactory
from src.ai.providers.base_provider import ProviderConfig, ProviderType


async def test_real_ai():
    """Test modern AI system with real API keys."""
    print("🤖 Modern AI System Test with Real API Keys")
    print("=" * 60)

    # Initialize settings
    settings = Settings()

    # Check if we have AI API keys
    if not settings.ai.openai_api_key or settings.ai.openai_api_key == "your_openai_api_key_here":
        print("❌ No OpenAI API key found in .env file")
        print("Please add your OpenAI API key to .env file:")
        print("OPENAI_API_KEY=sk-your-actual-key-here")
        return False

    print(f"✅ OpenAI API Key found: {settings.ai.openai_api_key[:20]}...")
    print(f"✅ OpenAI Base URL: {settings.ai.openai_base_url}")
    print(f"✅ Sentiment Model: {settings.ai.sentiment_model}")
    print(f"✅ Prediction Model: {settings.ai.prediction_model}")
    print(f"✅ Analysis Model: {settings.ai.analysis_model}")
    print(f"✅ General Model: {settings.ai.general_model}")

    # Test .env integration
    print(f"✅ Database URL: {settings.database.url[:40]}...")
    if hasattr(settings, 'exchanges') and settings.exchanges.binance_api_key:
        print(f"✅ Binance API Key: {settings.exchanges.binance_api_key[:20]}...")
        print(f"✅ Binance Testnet: {settings.exchanges.binance_testnet}")
    else:
        print("⚠️  Binance API Key: Not configured")

    if hasattr(settings, 'trading'):
        print(f"✅ Trading Pairs: {settings.trading.default_trading_pairs}")

    print(f"✅ Redis URL: {settings.redis.url[:40]}...")
    
    try:
        # Test 1: Modern OpenAI Provider
        print("\n📋 Test 1: Modern OpenAI Provider")
        print("-" * 40)

        # Create provider configuration
        config = ProviderConfig(
            name="real-test-openai",
            provider_type=ProviderType.OPENAI,
            api_key=settings.ai.openai_api_key,
            base_url=settings.ai.openai_base_url,
            model_mapping={
                'sentiment': settings.ai.sentiment_model,
                'prediction': settings.ai.prediction_model,
                'analysis': settings.ai.analysis_model,
                'general': settings.ai.general_model
            },
            timeout_seconds=settings.ai.openai_timeout,
            rate_limit_rpm=60,
            rate_limit_tpm=10000
        )

        # Create and initialize provider
        provider = ModernOpenAIProvider(config, settings)
        await provider.initialize()

        print(f"✅ Provider initialized: {provider.config.name}")
        print(f"✅ Provider type: {provider.config.provider_type.value}")
        print(f"✅ Base URL: {provider.config.base_url}")

        # Test basic functionality
        stats = provider.get_statistics()
        print(f"✅ Provider statistics accessible: {stats['name']}")

        provider_healthy = True
        
        # Test 2: Modern AI Service
        print("\n📋 Test 2: Modern AI Service")
        print("-" * 40)

        ai_service = AIService(settings)
        await ai_service.initialize()

        print(f"✅ AI Service initialized successfully")
        print(f"✅ Provider: {ai_service.provider.config.name if ai_service.provider else 'None'}")
        print(f"✅ Cache TTL: {ai_service.cache_ttl.total_seconds()/60} minutes")
        
        # Test 3: Real Sentiment Analysis
        print("\n📋 Test 3: Real Sentiment Analysis with Modern AI")
        print("-" * 50)

        test_texts = [
            ("Bitcoin reaches new all-time high as institutional adoption increases significantly", "positive"),
            ("Major cryptocurrency exchange faces security breach, Bitcoin price drops sharply", "negative"),
            ("Federal Reserve announces interest rate decision, markets remain stable and unchanged", "neutral"),
            ("Ethereum upgrade successful, network efficiency improves dramatically", "positive"),
            ("Regulatory uncertainty causes market volatility and investor concerns", "negative")
        ]

        sentiment_success = 0
        total_cost = 0.0

        for i, (text, expected_sentiment) in enumerate(test_texts, 1):
            print(f"\n🔍 Test {i}: {text[:60]}...")
            print(f"  Expected: {expected_sentiment}")
            try:
                sentiment = await ai_service.analyze_sentiment(text, context="crypto_news")
                print(f"  ✅ Actual: {sentiment.sentiment}")
                print(f"  📊 Confidence: {sentiment.confidence:.2f}")
                print(f"  📈 Score: {sentiment.score:.2f}")
                print(f"  🔑 Keywords: {sentiment.keywords[:3] if sentiment.keywords else []}")
                print(f"  💭 Reasoning: {sentiment.reasoning[:100]}..." if sentiment.reasoning else "")

                # Check if sentiment is reasonable (not just neutral fallback)
                if sentiment.confidence > 0.3:
                    sentiment_success += 1
                    print(f"  ✅ Success (high confidence)")
                elif sentiment.confidence > 0.1:
                    sentiment_success += 0.5
                    print(f"  ⚠️  Moderate confidence")
                else:
                    print(f"  ❌ Low confidence (likely fallback)")

            except Exception as e:
                print(f"  ❌ Failed: {e}")
                # Don't return False, continue with other tests
                continue

        print(f"\n📊 Sentiment Analysis Summary: {sentiment_success}/{len(test_texts)} tests successful")
        
        # Test 4: Real Price Prediction
        print("\n📋 Test 4: Real Price Prediction with Modern AI")
        print("-" * 50)

        symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
        timeframes = ["1h", "4h"]  # Test 2 timeframes to save API calls

        prediction_success = 0
        total_predictions = 0

        for symbol in symbols[:2]:  # Test first 2 symbols to save API calls
            for timeframe in timeframes:
                total_predictions += 1
                print(f"\n📈 Predicting {symbol} ({timeframe})...")
                try:
                    # Add realistic market data
                    current_price = 45000.0 if "BTC" in symbol else 3000.0 if "ETH" in symbol else 0.5
                    market_data = {
                        "current_price": current_price,
                        "volume_24h": 1000000000 if "BTC" in symbol else 500000000,
                        "price_change_24h": 0.02,
                        "high_24h": current_price * 1.05,
                        "low_24h": current_price * 0.95,
                        "market_cap": 800000000000 if "BTC" in symbol else 400000000000
                    }

                    prediction = await ai_service.predict_price(symbol, timeframe, market_data)
                    print(f"  🎯 Direction: {prediction.direction}")
                    print(f"  📊 Confidence: {prediction.confidence:.2f}")
                    print(f"  📈 Probability: {prediction.probability:.2f}")
                    if prediction.predicted_price > 0:
                        print(f"  💰 Predicted Price: ${prediction.predicted_price:.2f}")
                    print(f"  💭 Reasoning: {prediction.reasoning[:100]}..." if prediction.reasoning else "")

                    # Check if prediction is reasonable
                    if prediction.confidence > 0.3:
                        prediction_success += 1
                        print(f"  ✅ Success (high confidence)")
                    elif prediction.confidence > 0.1:
                        prediction_success += 0.5
                        print(f"  ⚠️  Moderate confidence")
                    else:
                        print(f"  ❌ Low confidence")

                except Exception as e:
                    print(f"  ❌ Failed: {e}")
                    # Continue with other tests
                    continue

        print(f"\n📊 Price Prediction Summary: {prediction_success}/{total_predictions} tests successful")
        
        # Test 5: Real Market Analysis
        print("\n📋 Test 5: Real Market Analysis with Modern AI")
        print("-" * 50)

        try:
            news_data = [
                "Bitcoin ETF approval boosts market confidence significantly",
                "Ethereum upgrade improves network efficiency and reduces fees",
                "Regulatory clarity increases institutional interest in crypto",
                "Major bank announces cryptocurrency trading services",
                "Central bank digital currency pilot program launched"
            ]

            symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]

            print("🔍 Analyzing market conditions...")
            analysis = await ai_service.analyze_market(symbols, news_data)

            print(f"  🌍 Market Condition: {analysis.market_condition}")
            print(f"  📊 Trend Strength: {analysis.trend_strength:.2f}")
            print(f"  📈 Volatility Level: {analysis.volatility_level}")
            print(f"  🔑 Key Factors ({len(analysis.key_factors)}): {analysis.key_factors[:3]}")
            print(f"  💡 Recommendations ({len(analysis.recommendations)}): {analysis.recommendations[:2]}")
            print(f"  📊 Confidence: {analysis.confidence:.2f}")

            # Check if analysis is reasonable
            if analysis.confidence > 0.3 and len(analysis.key_factors) > 0:
                print(f"  ✅ Success (detailed analysis with high confidence)")
                market_analysis_success = True
            elif analysis.confidence > 0.1:
                print(f"  ⚠️  Moderate confidence analysis")
                market_analysis_success = True
            else:
                print(f"  ❌ Low confidence or minimal analysis")
                market_analysis_success = False

        except Exception as e:
            print(f"  ❌ Failed: {e}")
            market_analysis_success = False
        
        # Test 6: Configuration Validation
        print("\n📋 Test 6: Configuration Validation")
        print("-" * 50)

        try:
            # Test that all models are properly loaded from .env
            print(f"  🤖 Sentiment Model: {settings.ai.sentiment_model}")
            print(f"  📈 Prediction Model: {settings.ai.prediction_model}")
            print(f"  📊 Analysis Model: {settings.ai.analysis_model}")
            print(f"  🔧 General Model: {settings.ai.general_model}")
            print(f"  🌐 Base URL: {settings.ai.openai_base_url}")
            print(f"  ⏱️  Timeout: {settings.ai.openai_timeout}s")

            # Validate that models are different (if configured differently)
            models = [
                settings.ai.sentiment_model,
                settings.ai.prediction_model,
                settings.ai.analysis_model,
                settings.ai.general_model
            ]

            print(f"  ✅ Configuration loaded successfully")
            print(f"  📊 Total unique models: {len(set(models))}")

        except Exception as e:
            print(f"  ❌ Configuration validation failed: {e}")

        # Test 7: Provider Statistics
        print("\n📋 Test 7: Provider Statistics")
        print("-" * 50)

        try:
            provider_stats = provider.get_statistics()

            print(f"  📊 Provider Name: {provider_stats['name']}")
            print(f"  📈 Total Requests: {provider_stats['total_requests']}")
            print(f"  💰 Total Cost: ${provider_stats.get('total_cost_usd', 0.0):.6f}")
            print(f"  ⚡ Average Latency: {provider_stats.get('average_latency_ms', 0.0):.2f}ms")
            print(f"  ❌ Error Count: {provider_stats.get('error_count', 0)}")
            print(f"  ✅ Statistics accessible")

        except Exception as e:
            print(f"  ❌ Statistics failed: {e}")
            provider_stats = {'total_requests': 0, 'total_cost_usd': 0.0, 'error_count': 0, 'average_latency_ms': 0.0}
        
        # Test 8: Cleanup and Final Assessment
        print("\n📋 Test 8: Cleanup and Final Assessment")
        print("-" * 50)

        # Cleanup
        try:
            if ai_service.provider:
                await ai_service.provider.cleanup()
            print("  ✅ Provider cleanup completed")
        except Exception as e:
            print(f"  ⚠️  Cleanup warning: {e}")

        # Final statistics
        final_stats = provider.get_statistics()

        print(f"\n🎉 All tests completed!")
        print("=" * 60)
        print(f"💰 Total AI cost: ${final_stats['total_cost_usd']:.6f}")
        print(f"📊 Total API requests: {final_stats['total_requests']}")
        print(f"⚡ Average latency: {final_stats['average_latency_ms']:.2f}ms")
        print(f"❌ Error count: {final_stats['error_count']}")

        # Calculate success rate
        total_requests = final_stats['total_requests']
        error_count = final_stats['error_count']
        success_rate = ((total_requests - error_count) / max(total_requests, 1)) * 100

        print(f"✅ Success rate: {success_rate:.1f}%")

        # Overall assessment
        if success_rate > 70 and sentiment_success > 2:
            print("\n🚀 Modern AI System is performing excellently!")
            print("✅ Ready for production use")
            return True
        elif success_rate > 50:
            print("\n⚠️  AI System is working but could be improved")
            print("🔧 Consider optimizing prompts or checking API limits")
            return True
        else:
            print("\n❌ AI System needs attention")
            print("🔧 Check API keys, network connection, and provider status")
            return False
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function for modern AI system."""
    print("🚀 Starting Real AI System Test...")
    print("Testing modern OpenAI-based architecture with real API keys")
    print("=" * 70)

    success = await test_real_ai()

    if success:
        print("\n🎉 Modern AI System is fully operational!")
        print("\n📝 Next steps:")
        print("1. 🤖 Integrate AI with trading strategies")
        print("2. 📰 Set up automated news monitoring")
        print("3. 💰 Configure cost limits and monitoring")
        print("4. 📊 Test with different market conditions")
        print("5. 🔧 Fine-tune model selection for different tasks")
        print("6. 📈 Implement real-time trading signals")

        print("\n🔧 Configuration Tips:")
        print("- Adjust models in .env for different tasks")
        print("- Monitor API costs and usage")
        print("- Test with different providers via OPENAI_BASE_URL")
        print("- Consider rate limiting for production use")

        return 0
    else:
        print("\n⚠️  AI System needs attention before production use")
        print("\n🔧 Troubleshooting:")
        print("1. Check API key validity")
        print("2. Verify network connection")
        print("3. Check provider status")
        print("4. Review error messages above")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
