"""
Sentiment Analysis Module for AI Trading Bot System.

This module provides sentiment analysis capabilities for news,
social media, and market sentiment indicators.

Author: inkbytefo
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ..config.settings import Settings


class SentimentAnalyzer:
    """
    Sentiment analysis engine for cryptocurrency trading.
    
    Analyzes news, social media, and market sentiment to provide
    sentiment scores for trading decisions.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Sentiment cache
        self.sentiment_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 600  # 10 minutes
        
    async def analyze_sentiment(self, symbol: str) -> Dict[str, Any]:
        """
        Perform comprehensive sentiment analysis for a symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTC/USDT')
            
        Returns:
            Dictionary containing sentiment analysis results
        """
        try:
            # Check cache first
            cached_result = self.get_cached_sentiment(symbol)
            if cached_result:
                return cached_result
            
            # Analyze different sentiment sources
            news_sentiment = await self._analyze_news_sentiment(symbol)
            social_sentiment = await self._analyze_social_sentiment(symbol)
            market_sentiment = await self._analyze_market_sentiment(symbol)
            
            # Combine sentiments
            overall_sentiment = self._combine_sentiments(
                news_sentiment, social_sentiment, market_sentiment
            )
            
            sentiment_result = {
                "symbol": symbol,
                "timestamp": datetime.utcnow().isoformat(),
                "overall_score": overall_sentiment["score"],
                "overall_label": overall_sentiment["label"],
                "confidence": overall_sentiment["confidence"],
                "sources": {
                    "news": news_sentiment,
                    "social": social_sentiment,
                    "market": market_sentiment
                },
                "signals": self._generate_sentiment_signals(overall_sentiment)
            }
            
            # Cache the result
            self.sentiment_cache[symbol] = {
                "data": sentiment_result,
                "timestamp": datetime.utcnow()
            }
            
            return sentiment_result
            
        except Exception as e:
            self.logger.error(f"Sentiment analysis failed for {symbol}: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "overall_score": 0.0,
                "overall_label": "neutral",
                "confidence": 0.0
            }
    
    async def _analyze_news_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Analyze news sentiment for the symbol."""
        try:
            # Placeholder for news sentiment analysis
            # In a real implementation, this would:
            # 1. Fetch recent news articles about the symbol
            # 2. Use NLP models to analyze sentiment
            # 3. Return aggregated sentiment score
            
            # For now, return neutral sentiment
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.5,
                "article_count": 0,
                "sources": []
            }
            
        except Exception as e:
            self.logger.error(f"News sentiment analysis failed: {e}")
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.0,
                "article_count": 0,
                "sources": []
            }
    
    async def _analyze_social_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Analyze social media sentiment for the symbol."""
        try:
            # Placeholder for social media sentiment analysis
            # In a real implementation, this would:
            # 1. Fetch tweets, Reddit posts, etc.
            # 2. Analyze sentiment using NLP models
            # 3. Return aggregated sentiment score
            
            # For now, return neutral sentiment
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.5,
                "post_count": 0,
                "platforms": {
                    "twitter": {"score": 0.0, "count": 0},
                    "reddit": {"score": 0.0, "count": 0}
                }
            }
            
        except Exception as e:
            self.logger.error(f"Social sentiment analysis failed: {e}")
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.0,
                "post_count": 0,
                "platforms": {}
            }
    
    async def _analyze_market_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Analyze market sentiment indicators."""
        try:
            # Placeholder for market sentiment analysis
            # In a real implementation, this would:
            # 1. Analyze fear & greed index
            # 2. Look at funding rates
            # 3. Analyze order book sentiment
            # 4. Check derivatives data
            
            # For now, return neutral sentiment
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.5,
                "indicators": {
                    "fear_greed_index": 50,
                    "funding_rate": 0.0,
                    "open_interest": "neutral"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Market sentiment analysis failed: {e}")
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.0,
                "indicators": {}
            }
    
    def _combine_sentiments(self, news: Dict[str, Any], social: Dict[str, Any], 
                          market: Dict[str, Any]) -> Dict[str, Any]:
        """Combine different sentiment sources into overall sentiment."""
        try:
            # Weights for different sources
            weights = {
                "news": 0.4,
                "social": 0.3,
                "market": 0.3
            }
            
            # Calculate weighted average
            total_score = (
                news["score"] * weights["news"] +
                social["score"] * weights["social"] +
                market["score"] * weights["market"]
            )
            
            # Calculate confidence as average of individual confidences
            total_confidence = (
                news["confidence"] * weights["news"] +
                social["confidence"] * weights["social"] +
                market["confidence"] * weights["market"]
            )
            
            # Determine label
            if total_score > 0.2:
                label = "bullish"
            elif total_score < -0.2:
                label = "bearish"
            else:
                label = "neutral"
            
            return {
                "score": total_score,
                "label": label,
                "confidence": total_confidence
            }
            
        except Exception as e:
            self.logger.error(f"Failed to combine sentiments: {e}")
            return {
                "score": 0.0,
                "label": "neutral",
                "confidence": 0.0
            }
    
    def _generate_sentiment_signals(self, sentiment: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading signals based on sentiment analysis."""
        try:
            score = sentiment["score"]
            confidence = sentiment["confidence"]
            
            # Generate signal based on sentiment score and confidence
            if score > 0.3 and confidence > 0.6:
                signal = "buy"
                strength = min(score * confidence, 1.0)
            elif score < -0.3 and confidence > 0.6:
                signal = "sell"
                strength = min(abs(score) * confidence, 1.0)
            else:
                signal = "neutral"
                strength = 0.0
            
            return {
                "signal": signal,
                "strength": strength,
                "reasoning": f"Sentiment score: {score:.2f}, Confidence: {confidence:.2f}"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate sentiment signals: {e}")
            return {
                "signal": "neutral",
                "strength": 0.0,
                "reasoning": "Error in sentiment signal generation"
            }
    
    def get_cached_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached sentiment if available and not expired."""
        if symbol in self.sentiment_cache:
            cached_data = self.sentiment_cache[symbol]
            age = (datetime.utcnow() - cached_data["timestamp"]).total_seconds()
            
            if age < self.cache_ttl:
                return cached_data["data"]
            else:
                # Remove expired cache
                del self.sentiment_cache[symbol]
        
        return None
    
    def clear_cache(self):
        """Clear sentiment cache."""
        self.sentiment_cache.clear()
        self.logger.info("Sentiment analysis cache cleared")
    
    async def get_market_fear_greed_index(self) -> Dict[str, Any]:
        """Get market-wide fear and greed index."""
        try:
            # Placeholder for fear & greed index
            # In a real implementation, this would fetch from APIs like:
            # - Alternative.me Fear & Greed Index
            # - Custom calculated index
            
            return {
                "value": 50,  # Neutral
                "label": "neutral",
                "timestamp": datetime.utcnow().isoformat(),
                "description": "Market sentiment is neutral"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get fear & greed index: {e}")
            return {
                "value": 50,
                "label": "neutral",
                "timestamp": datetime.utcnow().isoformat(),
                "description": "Error fetching market sentiment"
            }
    
    async def analyze_multiple_symbols(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Analyze sentiment for multiple symbols concurrently."""
        try:
            tasks = [self.analyze_sentiment(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            sentiment_results = {}
            for symbol, result in zip(symbols, results):
                if isinstance(result, Exception):
                    self.logger.error(f"Sentiment analysis failed for {symbol}: {result}")
                    sentiment_results[symbol] = {
                        "error": str(result),
                        "overall_score": 0.0,
                        "overall_label": "neutral"
                    }
                else:
                    sentiment_results[symbol] = result
            
            return sentiment_results
            
        except Exception as e:
            self.logger.error(f"Batch sentiment analysis failed: {e}")
            return {symbol: {"error": str(e)} for symbol in symbols}

    async def get_latest_sentiment(self, symbol: str = None) -> Dict[str, Any]:
        """
        Get the latest sentiment analysis results.

        Args:
            symbol: Optional specific symbol to get sentiment for.
                   If None, returns general market sentiment.

        Returns:
            Dict containing latest sentiment analysis results
        """
        try:
            if symbol:
                # Get sentiment for specific symbol
                # First check cache
                cached_result = self.get_cached_sentiment(symbol)
                if cached_result:
                    return cached_result

                # If not in cache, perform fresh analysis
                return await self.analyze_sentiment(symbol)

            else:
                # Return general market sentiment
                fear_greed = await self.get_market_fear_greed_index()

                # Get sentiment for major cryptocurrencies
                major_symbols = ["BTC/USDT", "ETH/USDT"]
                major_sentiments = {}

                for sym in major_symbols:
                    cached = self.get_cached_sentiment(sym)
                    if cached:
                        major_sentiments[sym] = cached
                    else:
                        # Use basic sentiment if no cache available
                        major_sentiments[sym] = {
                            "overall_score": 0.0,
                            "overall_label": "neutral",
                            "confidence": 0.5
                        }

                # Calculate overall market sentiment
                total_score = sum(s.get("overall_score", 0) for s in major_sentiments.values())
                avg_score = total_score / len(major_sentiments) if major_sentiments else 0

                if avg_score > 0.2:
                    overall_sentiment = "bullish"
                elif avg_score < -0.2:
                    overall_sentiment = "bearish"
                else:
                    overall_sentiment = "neutral"

                return {
                    "timestamp": datetime.utcnow().isoformat(),
                    "overall_sentiment": overall_sentiment,
                    "overall_score": avg_score,
                    "confidence": 0.6,
                    "fear_greed_index": fear_greed,
                    "major_symbols": major_sentiments,
                    "market_sentiment": overall_sentiment
                }

        except Exception as e:
            self.logger.error(f"Failed to get latest sentiment: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_sentiment": "neutral",
                "overall_score": 0.0,
                "confidence": 0.0,
                "error": str(e)
            }
