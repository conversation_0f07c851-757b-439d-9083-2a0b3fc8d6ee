"""
AI Manager Module for AI Trading Bot System.

This module manages AI model interactions and predictions.

Author: inkbytefo
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..config.settings import Settings
from .providers.provider_manager import ProviderManager
from .services.ai_service import AIService


class AIManager:
    """
    Manages AI model interactions for trading decisions.
    
    Coordinates between different AI providers and services
    to generate predictions and analysis.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # AI components
        self.provider_manager = ProviderManager(settings)
        self.ai_service = AIService(settings)
        
        # State
        self.is_initialized = False
        self.prediction_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def initialize(self):
        """Initialize the AI manager."""
        try:
            self.logger.info("Initializing AI Manager...")
            
            # Initialize provider manager
            await self.provider_manager.initialize()
            
            # Initialize AI service
            await self.ai_service.initialize()
            
            self.is_initialized = True
            self.logger.info("✅ AI Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize AI Manager: {e}")
            raise
    
    async def generate_prediction(self, prompt: str, model_type: str = "prediction",
                                context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate AI prediction based on prompt and context.
        
        Args:
            prompt: Input prompt for the AI model
            model_type: Type of model to use
            context: Additional context for the prediction
            
        Returns:
            AI model response
        """
        try:
            if not self.is_initialized:
                raise RuntimeError("AI Manager not initialized")
            
            # Check cache first
            cache_key = f"{model_type}_{hash(prompt)}"
            cached_result = self._get_cached_prediction(cache_key)
            if cached_result:
                return cached_result
            
            # Generate prediction using AI service
            response = await self.ai_service.generate_response(
                prompt=prompt,
                model_type=model_type
            )
            
            # Cache the result
            self._cache_prediction(cache_key, response)
            
            return response
            
        except Exception as e:
            self.logger.error(f"AI prediction generation failed: {e}")
            return self._get_fallback_response(model_type)
    
    async def analyze_market_sentiment(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market sentiment using AI models.
        
        Args:
            market_data: Market data for analysis
            
        Returns:
            Sentiment analysis results
        """
        try:
            # Prepare sentiment analysis prompt
            prompt = self._prepare_sentiment_prompt(market_data)
            
            # Get AI analysis
            response = await self.generate_prediction(
                prompt=prompt,
                model_type="sentiment",
                context=market_data
            )
            
            # Parse response
            sentiment_result = self._parse_sentiment_response(response)
            
            return sentiment_result
            
        except Exception as e:
            self.logger.error(f"Market sentiment analysis failed: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.0,
                "reasoning": f"Analysis failed: {str(e)}"
            }
    
    async def generate_trading_strategy(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate trading strategy recommendations using AI.
        
        Args:
            analysis_data: Combined analysis data
            
        Returns:
            Strategy recommendations
        """
        try:
            # Prepare strategy generation prompt
            prompt = self._prepare_strategy_prompt(analysis_data)
            
            # Get AI strategy
            response = await self.generate_prediction(
                prompt=prompt,
                model_type="strategy",
                context=analysis_data
            )
            
            # Parse strategy response
            strategy_result = self._parse_strategy_response(response)
            
            return strategy_result
            
        except Exception as e:
            self.logger.error(f"Trading strategy generation failed: {e}")
            return {
                "strategy": "hold",
                "confidence": 0.0,
                "reasoning": f"Strategy generation failed: {str(e)}"
            }
    
    def _prepare_sentiment_prompt(self, market_data: Dict[str, Any]) -> str:
        """Prepare prompt for sentiment analysis."""
        symbol = market_data.get("symbol", "Unknown")
        price = market_data.get("current_price", "N/A")
        volume = market_data.get("volume", "N/A")
        change_24h = market_data.get("price_change_24h", "N/A")
        
        prompt = f"""
        Analyze the market sentiment for {symbol} based on the following data:
        
        Current Price: {price}
        24h Volume: {volume}
        24h Price Change: {change_24h}%
        
        Provide sentiment analysis in the following format:
        {{
            "sentiment": "bullish/bearish/neutral",
            "confidence": 0.0-1.0,
            "reasoning": "brief explanation"
        }}
        """
        
        return prompt
    
    def _prepare_strategy_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Prepare prompt for strategy generation."""
        symbol = analysis_data.get("symbol", "Unknown")
        technical = analysis_data.get("technical", {})
        sentiment = analysis_data.get("sentiment", {})
        
        prompt = f"""
        Generate a trading strategy for {symbol} based on the following analysis:
        
        TECHNICAL ANALYSIS:
        - Signal: {technical.get('signals', {}).get('overall', 'neutral')}
        - Trend: {technical.get('trend', {}).get('direction', 'neutral')}
        - RSI: {technical.get('indicators', {}).get('rsi', 'N/A')}
        
        SENTIMENT ANALYSIS:
        - Sentiment: {sentiment.get('overall_label', 'neutral')}
        - Score: {sentiment.get('overall_score', 0)}
        
        Provide strategy recommendation in the following format:
        {{
            "strategy": "buy/sell/hold",
            "confidence": 0.0-1.0,
            "reasoning": "brief explanation",
            "risk_level": "low/medium/high"
        }}
        """
        
        return prompt
    
    def _parse_sentiment_response(self, response: str) -> Dict[str, Any]:
        """Parse AI sentiment response."""
        try:
            import json
            
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                return {
                    "sentiment": parsed.get("sentiment", "neutral"),
                    "confidence": float(parsed.get("confidence", 0.0)),
                    "reasoning": parsed.get("reasoning", "AI sentiment analysis")
                }
            else:
                raise ValueError("No valid JSON found")
                
        except Exception as e:
            self.logger.error(f"Failed to parse sentiment response: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.0,
                "reasoning": "Failed to parse AI response"
            }
    
    def _parse_strategy_response(self, response: str) -> Dict[str, Any]:
        """Parse AI strategy response."""
        try:
            import json
            
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                return {
                    "strategy": parsed.get("strategy", "hold"),
                    "confidence": float(parsed.get("confidence", 0.0)),
                    "reasoning": parsed.get("reasoning", "AI strategy recommendation"),
                    "risk_level": parsed.get("risk_level", "medium")
                }
            else:
                raise ValueError("No valid JSON found")
                
        except Exception as e:
            self.logger.error(f"Failed to parse strategy response: {e}")
            return {
                "strategy": "hold",
                "confidence": 0.0,
                "reasoning": "Failed to parse AI response",
                "risk_level": "high"
            }
    
    def _get_cached_prediction(self, cache_key: str) -> Optional[str]:
        """Get cached prediction if available and not expired."""
        if cache_key in self.prediction_cache:
            cached_data = self.prediction_cache[cache_key]
            age = (datetime.utcnow() - cached_data["timestamp"]).total_seconds()
            
            if age < self.cache_ttl:
                return cached_data["response"]
            else:
                # Remove expired cache
                del self.prediction_cache[cache_key]
        
        return None
    
    def _cache_prediction(self, cache_key: str, response: str):
        """Cache prediction response."""
        self.prediction_cache[cache_key] = {
            "response": response,
            "timestamp": datetime.utcnow()
        }
        
        # Keep cache size manageable
        if len(self.prediction_cache) > 100:
            # Remove oldest entries
            oldest_keys = sorted(
                self.prediction_cache.keys(),
                key=lambda k: self.prediction_cache[k]["timestamp"]
            )[:20]
            
            for key in oldest_keys:
                del self.prediction_cache[key]
    
    def _get_fallback_response(self, model_type: str) -> str:
        """Get fallback response when AI fails."""
        fallback_responses = {
            "prediction": '{"action": "hold", "confidence": 0.0, "reasoning": "AI service unavailable"}',
            "sentiment": '{"sentiment": "neutral", "confidence": 0.0, "reasoning": "AI service unavailable"}',
            "strategy": '{"strategy": "hold", "confidence": 0.0, "reasoning": "AI service unavailable", "risk_level": "high"}'
        }
        
        return fallback_responses.get(model_type, '{"error": "AI service unavailable"}')
    
    async def get_model_health(self) -> Dict[str, Any]:
        """Get health status of AI models."""
        try:
            provider_health = await self.provider_manager.get_health_status()
            service_health = await self.ai_service.get_health_status()
            
            return {
                "ai_manager_initialized": self.is_initialized,
                "provider_manager": provider_health,
                "ai_service": service_health,
                "cache_size": len(self.prediction_cache)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get model health: {e}")
            return {
                "ai_manager_initialized": self.is_initialized,
                "error": str(e)
            }
    
    def clear_cache(self):
        """Clear prediction cache."""
        self.prediction_cache.clear()
        self.logger.info("AI prediction cache cleared")
    
    async def shutdown(self):
        """Shutdown the AI manager."""
        try:
            self.logger.info("Shutting down AI Manager...")
            
            # Shutdown components
            await self.ai_service.shutdown()
            await self.provider_manager.shutdown()
            
            # Clear cache
            self.clear_cache()
            
            self.is_initialized = False
            self.logger.info("AI Manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during AI manager shutdown: {e}")
    
    async def test_ai_connection(self) -> Dict[str, Any]:
        """Test AI model connections."""
        try:
            test_prompt = "Test connection. Respond with: {'status': 'ok'}"
            
            response = await self.generate_prediction(
                prompt=test_prompt,
                model_type="test"
            )
            
            return {
                "connection_test": "passed",
                "response": response,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"AI connection test failed: {e}")
            return {
                "connection_test": "failed",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
