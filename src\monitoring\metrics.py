"""
Metrics collection and monitoring for AI Trading Bot System.

This module provides comprehensive metrics collection using Prometheus
for monitoring system performance, trading activities, and health status.

Author: inkbytefo
"""

import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque

from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info,
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)

from ..config.settings import Settings


@dataclass
class MetricPoint:
    """Data class for metric points."""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str] = None


class MetricsCollector:
    """
    Comprehensive metrics collector for the AI Trading Bot.
    
    Collects and exposes metrics for monitoring system performance,
    trading activities, and business metrics.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Create custom registry
        self.registry = CollectorRegistry()
        
        # Initialize metrics
        self._init_system_metrics()
        self._init_trading_metrics()
        self._init_ai_metrics()
        self._init_business_metrics()
        
        # Metric storage for custom calculations
        self._metric_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._last_update = datetime.utcnow()
    
    def _init_system_metrics(self):
        """Initialize system performance metrics."""
        # System resource metrics
        self.cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'system_memory_usage_percent',
            'Memory usage percentage',
            registry=self.registry
        )
        
        self.disk_usage = Gauge(
            'system_disk_usage_percent',
            'Disk usage percentage',
            registry=self.registry
        )
        
        # Application metrics
        self.app_uptime = Gauge(
            'app_uptime_seconds',
            'Application uptime in seconds',
            registry=self.registry
        )
        
        self.active_connections = Gauge(
            'database_active_connections',
            'Number of active database connections',
            registry=self.registry
        )
        
        # Request metrics
        self.request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.request_count = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
    
    def _init_trading_metrics(self):
        """Initialize trading-specific metrics."""
        # Order metrics
        self.orders_total = Counter(
            'trading_orders_total',
            'Total number of orders',
            ['exchange', 'pair', 'side', 'status'],
            registry=self.registry
        )
        
        self.order_execution_time = Histogram(
            'trading_order_execution_seconds',
            'Order execution time',
            ['exchange', 'pair'],
            registry=self.registry
        )
        
        # Position metrics
        self.active_positions = Gauge(
            'trading_active_positions',
            'Number of active positions',
            ['exchange', 'pair'],
            registry=self.registry
        )
        
        self.position_pnl = Gauge(
            'trading_position_pnl_usd',
            'Position P&L in USD',
            ['exchange', 'pair', 'side'],
            registry=self.registry
        )
        
        # Portfolio metrics
        self.portfolio_value = Gauge(
            'trading_portfolio_value_usd',
            'Total portfolio value in USD',
            ['exchange'],
            registry=self.registry
        )
        
        self.portfolio_pnl_daily = Gauge(
            'trading_portfolio_pnl_daily_usd',
            'Daily portfolio P&L in USD',
            ['exchange'],
            registry=self.registry
        )
        
        # Risk metrics
        self.drawdown_current = Gauge(
            'trading_drawdown_current_percent',
            'Current drawdown percentage',
            registry=self.registry
        )
        
        self.risk_exposure = Gauge(
            'trading_risk_exposure_percent',
            'Current risk exposure percentage',
            ['exchange'],
            registry=self.registry
        )
    
    def _init_ai_metrics(self):
        """Initialize AI and ML metrics."""
        # Model performance
        self.model_prediction_accuracy = Gauge(
            'ai_model_prediction_accuracy',
            'Model prediction accuracy',
            ['model_name', 'timeframe'],
            registry=self.registry
        )
        
        self.model_inference_time = Histogram(
            'ai_model_inference_seconds',
            'Model inference time',
            ['model_name'],
            registry=self.registry
        )
        
        # Signal metrics
        self.signals_generated = Counter(
            'ai_signals_generated_total',
            'Total signals generated',
            ['strategy', 'signal_type', 'pair'],
            registry=self.registry
        )
        
        self.signal_confidence = Histogram(
            'ai_signal_confidence',
            'Signal confidence distribution',
            ['strategy', 'signal_type'],
            registry=self.registry
        )
        
        # Strategy performance
        self.strategy_win_rate = Gauge(
            'ai_strategy_win_rate_percent',
            'Strategy win rate percentage',
            ['strategy_name'],
            registry=self.registry
        )
        
        self.strategy_sharpe_ratio = Gauge(
            'ai_strategy_sharpe_ratio',
            'Strategy Sharpe ratio',
            ['strategy_name'],
            registry=self.registry
        )
    
    def _init_business_metrics(self):
        """Initialize business and performance metrics."""
        # Trading performance
        self.total_trades = Counter(
            'business_total_trades',
            'Total number of trades executed',
            ['exchange', 'pair'],
            registry=self.registry
        )
        
        self.winning_trades = Counter(
            'business_winning_trades',
            'Number of winning trades',
            ['exchange', 'pair'],
            registry=self.registry
        )
        
        self.total_volume = Counter(
            'business_total_volume_usd',
            'Total trading volume in USD',
            ['exchange', 'pair'],
            registry=self.registry
        )
        
        # Fees and costs
        self.trading_fees = Counter(
            'business_trading_fees_usd',
            'Total trading fees paid in USD',
            ['exchange', 'fee_type'],
            registry=self.registry
        )
        
        # Market data metrics
        self.market_data_updates = Counter(
            'market_data_updates_total',
            'Total market data updates received',
            ['exchange', 'pair', 'timeframe'],
            registry=self.registry
        )
        
        self.market_data_latency = Histogram(
            'market_data_latency_seconds',
            'Market data update latency',
            ['exchange', 'pair'],
            registry=self.registry
        )
    
    def record_order(self, exchange: str, pair: str, side: str, status: str, 
                    execution_time: Optional[float] = None):
        """Record order metrics."""
        self.orders_total.labels(
            exchange=exchange, pair=pair, side=side, status=status
        ).inc()
        
        if execution_time is not None:
            self.order_execution_time.labels(
                exchange=exchange, pair=pair
            ).observe(execution_time)
    
    def record_position_update(self, exchange: str, pair: str, side: str, 
                              pnl: float, is_active: bool):
        """Record position metrics."""
        self.position_pnl.labels(
            exchange=exchange, pair=pair, side=side
        ).set(pnl)
        
        # Update active positions count
        current_positions = self.active_positions.labels(
            exchange=exchange, pair=pair
        )._value._value if hasattr(self.active_positions.labels(
            exchange=exchange, pair=pair
        ), '_value') else 0
        
        if is_active:
            self.active_positions.labels(
                exchange=exchange, pair=pair
            ).set(current_positions + 1)
    
    def record_portfolio_update(self, exchange: str, total_value: float, 
                               daily_pnl: float):
        """Record portfolio metrics."""
        self.portfolio_value.labels(exchange=exchange).set(total_value)
        self.portfolio_pnl_daily.labels(exchange=exchange).set(daily_pnl)
    
    def record_ai_signal(self, strategy: str, signal_type: str, pair: str, 
                        confidence: float):
        """Record AI signal metrics."""
        self.signals_generated.labels(
            strategy=strategy, signal_type=signal_type, pair=pair
        ).inc()
        
        self.signal_confidence.labels(
            strategy=strategy, signal_type=signal_type
        ).observe(confidence)
    
    def record_model_performance(self, model_name: str, timeframe: str, 
                               accuracy: float, inference_time: float):
        """Record ML model performance metrics."""
        self.model_prediction_accuracy.labels(
            model_name=model_name, timeframe=timeframe
        ).set(accuracy)
        
        self.model_inference_time.labels(
            model_name=model_name
        ).observe(inference_time)
    
    def record_trade_execution(self, exchange: str, pair: str, volume: float, 
                             is_winning: bool, fees: float):
        """Record trade execution metrics."""
        self.total_trades.labels(exchange=exchange, pair=pair).inc()
        self.total_volume.labels(exchange=exchange, pair=pair).inc(volume)
        self.trading_fees.labels(exchange=exchange, fee_type='trading').inc(fees)
        
        if is_winning:
            self.winning_trades.labels(exchange=exchange, pair=pair).inc()
    
    def record_market_data_update(self, exchange: str, pair: str, timeframe: str, 
                                 latency: float):
        """Record market data update metrics."""
        self.market_data_updates.labels(
            exchange=exchange, pair=pair, timeframe=timeframe
        ).inc()
        
        self.market_data_latency.labels(
            exchange=exchange, pair=pair
        ).observe(latency)
    
    def record_system_metrics(self, cpu_percent: float, memory_percent: float, 
                            disk_percent: float, active_connections: int):
        """Record system resource metrics."""
        self.cpu_usage.set(cpu_percent)
        self.memory_usage.set(memory_percent)
        self.disk_usage.set(disk_percent)
        self.active_connections.set(active_connections)
    
    def record_http_request(self, method: str, endpoint: str, status: str, 
                           duration: float):
        """Record HTTP request metrics."""
        self.request_count.labels(
            method=method, endpoint=endpoint, status=status
        ).inc()
        
        self.request_duration.labels(
            method=method, endpoint=endpoint, status=status
        ).observe(duration)
    
    def update_uptime(self, start_time: datetime):
        """Update application uptime."""
        uptime_seconds = (datetime.utcnow() - start_time).total_seconds()
        self.app_uptime.set(uptime_seconds)
    
    def calculate_win_rate(self, strategy_name: str, winning_trades: int, 
                          total_trades: int):
        """Calculate and record strategy win rate."""
        if total_trades > 0:
            win_rate = (winning_trades / total_trades) * 100
            self.strategy_win_rate.labels(strategy_name=strategy_name).set(win_rate)
    
    def calculate_sharpe_ratio(self, strategy_name: str, returns: List[float], 
                              risk_free_rate: float = 0.02):
        """Calculate and record strategy Sharpe ratio."""
        if len(returns) > 1:
            import numpy as np
            
            excess_returns = np.array(returns) - risk_free_rate / 252  # Daily risk-free rate
            if np.std(excess_returns) > 0:
                sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
                self.strategy_sharpe_ratio.labels(strategy_name=strategy_name).set(sharpe_ratio)
    
    def record_custom_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a custom metric point."""
        metric_point = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            labels=labels or {}
        )

        self._metric_history[name].append(metric_point)

    def record_system_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a system metric point."""
        metric_point = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            labels=labels or {}
        )

        self._metric_history[f"system_{name}"].append(metric_point)

    def get_custom_metrics(self) -> List[MetricPoint]:
        """Get all custom metrics."""
        all_metrics = []
        for metric_list in self._metric_history.values():
            all_metrics.extend(metric_list)
        return all_metrics

    def get_system_metrics(self) -> List[MetricPoint]:
        """Get all system metrics."""
        system_metrics = []
        for name, metric_list in self._metric_history.items():
            if name.startswith("system_"):
                system_metrics.extend(metric_list)
        return system_metrics
    
    def get_metric_history(self, name: str, duration_minutes: int = 60) -> List[MetricPoint]:
        """Get metric history for a specific time period."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=duration_minutes)
        
        return [
            point for point in self._metric_history[name]
            if point.timestamp >= cutoff_time
        ]
    
    def export_metrics(self) -> str:
        """Export metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics."""
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "system": {
                "cpu_usage": self.cpu_usage._value._value if hasattr(self.cpu_usage, '_value') else 0,
                "memory_usage": self.memory_usage._value._value if hasattr(self.memory_usage, '_value') else 0,
                "disk_usage": self.disk_usage._value._value if hasattr(self.disk_usage, '_value') else 0,
                "uptime": self.app_uptime._value._value if hasattr(self.app_uptime, '_value') else 0,
            },
            "trading": {
                "total_orders": sum(self.orders_total._value.values()) if hasattr(self.orders_total, '_value') else 0,
                "total_trades": sum(self.total_trades._value.values()) if hasattr(self.total_trades, '_value') else 0,
                "total_volume": sum(self.total_volume._value.values()) if hasattr(self.total_volume, '_value') else 0,
            },
            "ai": {
                "total_signals": sum(self.signals_generated._value.values()) if hasattr(self.signals_generated, '_value') else 0,
            }
        }
        
        return summary
    
    def reset_metrics(self):
        """Reset all metrics (useful for testing)."""
        self.logger.warning("Resetting all metrics")
        
        # Clear metric history
        self._metric_history.clear()
        
        # Reset counters and gauges would require recreating them
        # This is typically not done in production
        self.logger.info("Metrics reset completed")


# Global metrics collector instance
metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global metrics_collector
    if metrics_collector is None:
        from ..config.settings import Settings
        settings = Settings()
        metrics_collector = MetricsCollector(settings)
    return metrics_collector
