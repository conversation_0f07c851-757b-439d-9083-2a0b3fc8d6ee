"""Initial migration

Revision ID: 299a988ccb78
Revises: 
Create Date: 2025-08-07 14:02:43.497444

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '299a988ccb78'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('system_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('metric_value', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('metric_unit', sa.String(length=20), nullable=True),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_metrics_name_timestamp', 'system_metrics', ['metric_name', 'created_at'], unique=False)
    op.create_index(op.f('ix_system_metrics_metric_name'), 'system_metrics', ['metric_name'], unique=False)
    op.create_table('trading_pairs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('base_asset', sa.String(length=10), nullable=False),
    sa.Column('quote_asset', sa.String(length=10), nullable=False),
    sa.Column('exchange', sa.String(length=50), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('min_order_size', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('max_order_size', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('price_precision', sa.Integer(), nullable=True),
    sa.Column('quantity_precision', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_trading_pair_exchange_symbol', 'trading_pairs', ['exchange', 'symbol'], unique=False)
    op.create_index(op.f('ix_trading_pairs_symbol'), 'trading_pairs', ['symbol'], unique=True)
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('salt', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_admin', sa.Boolean(), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('market_data',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('trading_pair_id', sa.UUID(), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('open_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('high_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('low_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('close_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('volume', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('vwap', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('trades_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['trading_pair_id'], ['trading_pairs.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('trading_pair_id', 'timeframe', 'timestamp', name='unique_market_data')
    )
    op.create_index('idx_market_data_pair_timeframe_timestamp', 'market_data', ['trading_pair_id', 'timeframe', 'timestamp'], unique=False)
    op.create_table('strategies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('strategy_type', sa.String(length=50), nullable=False),
    sa.Column('parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('winning_trades', sa.Integer(), nullable=True),
    sa.Column('total_pnl', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('max_drawdown', sa.Numeric(precision=10, scale=4), nullable=True),
    sa.Column('sharpe_ratio', sa.Numeric(precision=10, scale=4), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_strategy_user_type', 'strategies', ['user_id', 'strategy_type'], unique=False)
    op.create_table('trading_accounts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('exchange', sa.String(length=50), nullable=False),
    sa.Column('account_name', sa.String(length=100), nullable=False),
    sa.Column('encrypted_credentials', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_testnet', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'exchange', 'account_name', name='unique_user_exchange_account')
    )
    op.create_index('idx_trading_account_user_exchange', 'trading_accounts', ['user_id', 'exchange'], unique=False)
    op.create_table('positions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('trading_account_id', sa.UUID(), nullable=False),
    sa.Column('trading_pair_id', sa.UUID(), nullable=False),
    sa.Column('side', sa.String(length=10), nullable=False),
    sa.Column('quantity', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('entry_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('current_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('unrealized_pnl', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('realized_pnl', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('stop_loss', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('take_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('opened_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('closed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint('quantity > 0', name='check_positive_position_quantity'),
    sa.ForeignKeyConstraint(['trading_account_id'], ['trading_accounts.id'], ),
    sa.ForeignKeyConstraint(['trading_pair_id'], ['trading_pairs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_position_account_pair_status', 'positions', ['trading_account_id', 'trading_pair_id', 'status'], unique=False)
    op.create_table('trading_signals',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('strategy_id', sa.UUID(), nullable=False),
    sa.Column('trading_pair_id', sa.UUID(), nullable=False),
    sa.Column('signal_type', sa.String(length=10), nullable=False),
    sa.Column('confidence', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('strength', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('entry_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('stop_loss', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('take_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('indicators_used', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('market_conditions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('executed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint('confidence >= 0 AND confidence <= 1', name='check_confidence_range'),
    sa.CheckConstraint('strength >= 0 AND strength <= 1', name='check_strength_range'),
    sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
    sa.ForeignKeyConstraint(['trading_pair_id'], ['trading_pairs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_signal_strategy_pair_timestamp', 'trading_signals', ['strategy_id', 'trading_pair_id', 'created_at'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('trading_account_id', sa.UUID(), nullable=False),
    sa.Column('trading_pair_id', sa.UUID(), nullable=False),
    sa.Column('signal_id', sa.UUID(), nullable=True),
    sa.Column('exchange_order_id', sa.String(length=100), nullable=True),
    sa.Column('order_type', sa.String(length=20), nullable=False),
    sa.Column('side', sa.String(length=10), nullable=False),
    sa.Column('quantity', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('filled_quantity', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('average_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('submitted_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('filled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('cancelled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('fee_amount', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('fee_currency', sa.String(length=10), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint('filled_quantity >= 0', name='check_non_negative_filled'),
    sa.CheckConstraint('quantity > 0', name='check_positive_quantity'),
    sa.ForeignKeyConstraint(['signal_id'], ['trading_signals.id'], ),
    sa.ForeignKeyConstraint(['trading_account_id'], ['trading_accounts.id'], ),
    sa.ForeignKeyConstraint(['trading_pair_id'], ['trading_pairs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_order_account_pair_status', 'orders', ['trading_account_id', 'trading_pair_id', 'status'], unique=False)
    op.create_index('idx_order_exchange_id', 'orders', ['exchange_order_id'], unique=False)
    op.create_index(op.f('ix_orders_exchange_order_id'), 'orders', ['exchange_order_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_orders_exchange_order_id'), table_name='orders')
    op.drop_index('idx_order_exchange_id', table_name='orders')
    op.drop_index('idx_order_account_pair_status', table_name='orders')
    op.drop_table('orders')
    op.drop_index('idx_signal_strategy_pair_timestamp', table_name='trading_signals')
    op.drop_table('trading_signals')
    op.drop_index('idx_position_account_pair_status', table_name='positions')
    op.drop_table('positions')
    op.drop_index('idx_trading_account_user_exchange', table_name='trading_accounts')
    op.drop_table('trading_accounts')
    op.drop_index('idx_strategy_user_type', table_name='strategies')
    op.drop_table('strategies')
    op.drop_index('idx_market_data_pair_timeframe_timestamp', table_name='market_data')
    op.drop_table('market_data')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_trading_pairs_symbol'), table_name='trading_pairs')
    op.drop_index('idx_trading_pair_exchange_symbol', table_name='trading_pairs')
    op.drop_table('trading_pairs')
    op.drop_index(op.f('ix_system_metrics_metric_name'), table_name='system_metrics')
    op.drop_index('idx_metrics_name_timestamp', table_name='system_metrics')
    op.drop_table('system_metrics')
    # ### end Alembic commands ###
