"""
Settings and configuration management for AI Trading Bot System.

This module handles all configuration settings using Pydantic for validation
and type safety.

Author: inkbytefo
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator, ConfigDict, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load .env file explicitly
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="DATABASE_",
        extra="ignore"
    )

    url: str = Field(default="postgresql://tradingbot:tradingbot123@localhost:5432/trading_bot", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")


class RedisSettings(BaseSettings):
    """Redis configuration settings."""

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="REDIS_",
        extra="ignore"
    )

    url: str = Field(default="redis://:redis123@localhost:6379/0", env="REDIS_URL")
    max_connections: int = Field(default=10, env="REDIS_MAX_CONNECTIONS")


class ExchangeSettings(BaseSettings):
    """Exchange API configuration settings."""

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="",
        extra="ignore"
    )

    # Binance (Spot & Futures)
    binance_api_key: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    binance_secret_key: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    binance_testnet: bool = Field(default=True, env="BINANCE_TESTNET")

    # MEXC Global
    mexc_api_key: Optional[str] = Field(default=None, env="MEXC_API_KEY")
    mexc_secret_key: Optional[str] = Field(default=None, env="MEXC_SECRET_KEY")
    mexc_testnet: bool = Field(default=False, env="MEXC_TESTNET")


class TradingSettings(BaseSettings):
    """Trading configuration settings."""

    model_config = SettingsConfigDict(
        env_file=[".env", "../.env", "../../.env"],
        env_file_encoding="utf-8",
        env_prefix="",
        case_sensitive=False,
        extra="ignore"
    )

    # Geniş coin yelpazesi - popüler ve likidite yüksek coinler
    default_trading_pairs: List[str] = Field(default=[
        # Major cryptocurrencies
        "BTC/USDT", "ETH/USDT", "BNB/USDT", "XRP/USDT", "ADA/USDT",
        "SOL/USDT", "DOGE/USDT", "DOT/USDT", "MATIC/USDT", "AVAX/USDT",
        # DeFi tokens
        "UNI/USDT", "LINK/USDT", "AAVE/USDT", "SUSHI/USDT", "COMP/USDT",
        # Layer 1 blockchains
        "ATOM/USDT", "ALGO/USDT", "NEAR/USDT", "FTM/USDT", "ONE/USDT",
        # Popular altcoins
        "LTC/USDT", "BCH/USDT", "ETC/USDT", "XLM/USDT", "VET/USDT",
        "THETA/USDT", "FIL/USDT", "TRX/USDT", "EOS/USDT", "IOTA/USDT"
    ])

    default_base_currency: str = Field(default="USDT")
    max_position_size: float = Field(default=0.1)
    stop_loss_percentage: float = Field(default=0.05)
    take_profit_percentage: float = Field(default=0.15)

    def __init__(self, **kwargs):
        # Load from environment variables manually first (excluding trading pairs)
        base_currency = os.getenv("DEFAULT_BASE_CURRENCY")
        if base_currency:
            kwargs["default_base_currency"] = base_currency

        position_size = os.getenv("MAX_POSITION_SIZE")
        if position_size:
            kwargs["max_position_size"] = float(position_size)

        stop_loss = os.getenv("STOP_LOSS_PERCENTAGE")
        if stop_loss:
            kwargs["stop_loss_percentage"] = float(stop_loss)

        take_profit = os.getenv("TAKE_PROFIT_PERCENTAGE")
        if take_profit:
            kwargs["take_profit_percentage"] = float(take_profit)

        super().__init__(**kwargs)


class RiskSettings(BaseSettings):
    """Risk management configuration settings."""

    model_config = ConfigDict(env_prefix="")

    max_daily_loss: float = Field(default=0.02, env="MAX_DAILY_LOSS")
    max_drawdown: float = Field(default=0.10, env="MAX_DRAWDOWN")
    emergency_stop_loss: float = Field(default=0.20, env="EMERGENCY_STOP_LOSS")


class DataSourceSettings(BaseSettings):
    """Data source configuration settings."""

    model_config = ConfigDict(env_prefix="")

    news_api_key: Optional[str] = Field(default=None, env="NEWS_API_KEY")
    twitter_bearer_token: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    reddit_client_id: Optional[str] = Field(default=None, env="REDDIT_CLIENT_ID")
    reddit_client_secret: Optional[str] = Field(default=None, env="REDDIT_CLIENT_SECRET")


class AISettings(BaseSettings):
    """AI/ML configuration settings."""

    model_config = SettingsConfigDict(
        env_file=[".env", "../.env", "../../.env"],  # Multiple possible locations
        env_file_encoding="utf-8",
        env_prefix="",
        protected_namespaces=(),
        extra="ignore",
        case_sensitive=False
    )

    # Primary OpenAI Configuration - Values loaded directly from .env
    openai_api_key: Optional[str] = Field(env="OPENAI_API_KEY")
    openai_base_url: str = Field(env="OPENAI_BASE_URL")
    openai_model: str = Field(env="OPENAI_MODEL")
    openai_max_tokens: int = Field(env="OPENAI_MAX_TOKENS")
    openai_temperature: float = Field(env="OPENAI_TEMPERATURE")
    openai_timeout: int = Field(env="OPENAI_TIMEOUT")

    # Model Selection for Different Tasks - Values loaded from .env with fallback
    sentiment_model: str = Field(default_factory=lambda: os.getenv("AI_SENTIMENT_MODEL", "gpt-3.5-turbo"))
    prediction_model: str = Field(default_factory=lambda: os.getenv("AI_PREDICTION_MODEL", "gpt-3.5-turbo"))
    analysis_model: str = Field(default_factory=lambda: os.getenv("AI_ANALYSIS_MODEL", "gpt-3.5-turbo"))
    general_model: str = Field(default_factory=lambda: os.getenv("AI_GENERAL_MODEL", "gpt-3.5-turbo"))

    # Azure OpenAI Configuration
    azure_openai_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_version: str = Field(default="2023-12-01-preview", env="AZURE_OPENAI_VERSION")

    # Anthropic Configuration
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    anthropic_model: str = Field(default="claude-3-sonnet-********", env="ANTHROPIC_MODEL")

    # Google AI Configuration
    google_ai_key: Optional[str] = Field(default=None, env="GOOGLE_AI_KEY")
    google_ai_model: str = Field(default="gemini-pro", env="GOOGLE_AI_MODEL")

    # Local AI Configuration
    local_ai_url: Optional[str] = Field(default=None, env="LOCAL_AI_URL")
    local_ai_model: str = Field(default="llama2:7b", env="LOCAL_AI_MODEL")

    # AI Routing Configuration
    ai_routing_strategy: str = Field(default="health_based", env="AI_ROUTING_STRATEGY")
    ai_fallback_enabled: bool = Field(default=True, env="AI_FALLBACK_ENABLED")
    ai_max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    ai_timeout_seconds: int = Field(default=30, env="AI_TIMEOUT_SECONDS")

    # Cost Control
    ai_max_cost_per_request: float = Field(default=1.0, env="AI_MAX_COST_PER_REQUEST")
    ai_daily_budget: float = Field(default=50.0, env="AI_DAILY_BUDGET")

    # Model Performance
    ai_model_update_interval: int = Field(default=3600, env="MODEL_UPDATE_INTERVAL")
    prediction_confidence_threshold: float = Field(default=0.7, env="PREDICTION_CONFIDENCE_THRESHOLD")
    sentiment_weight: float = Field(default=0.3, env="SENTIMENT_WEIGHT")
    technical_weight: float = Field(default=0.7, env="TECHNICAL_WEIGHT")


class MonitoringSettings(BaseSettings):
    """Monitoring and alerting configuration settings."""

    model_config = ConfigDict(env_prefix="")

    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    grafana_port: int = Field(default=3000, env="GRAFANA_PORT")
    alert_email: Optional[str] = Field(default=None, env="ALERT_EMAIL")
    slack_webhook_url: Optional[str] = Field(default=None, env="SLACK_WEBHOOK_URL")
    discord_webhook_url: Optional[str] = Field(default=None, env="DISCORD_WEBHOOK_URL")


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    model_config = ConfigDict(env_prefix="LOG_")

    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")
    file_path: str = Field(default="logs/trading_bot.log", env="LOG_FILE_PATH")
    rotation_size: str = Field(default="100MB", env="LOG_ROTATION_SIZE")
    retention_days: int = Field(default=30, env="LOG_RETENTION_DAYS")


class BacktestingSettings(BaseSettings):
    """Backtesting configuration settings."""

    model_config = ConfigDict(env_prefix="BACKTEST_")

    start_date: str = Field(default="2023-01-01", env="BACKTEST_START_DATE")
    end_date: str = Field(default="2024-01-01", env="BACKTEST_END_DATE")
    initial_balance: float = Field(default=10000.0, env="BACKTEST_INITIAL_BALANCE")


class MarketDataSettings(BaseSettings):
    """Market data collection configuration settings."""

    model_config = ConfigDict(env_prefix="MARKET_DATA_", extra="ignore")

    timeframes: List[str] = Field(default=["1m", "5m", "15m", "1h", "4h", "1d"])
    collection_interval: int = Field(default=60)

    def __init__(self, **kwargs):
        # Load from environment variables manually first (excluding timeframes)
        collection_interval = os.getenv("MARKET_DATA_COLLECTION_INTERVAL")
        if collection_interval:
            kwargs["collection_interval"] = int(collection_interval)

        super().__init__(**kwargs)


class Settings(BaseSettings):
    """Main settings class that combines all configuration sections."""

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )

    # Application settings
    app_name: str = Field(default="AI Trading Bot", env="APP_NAME")
    VERSION: str = Field(default="1.0.0", env="VERSION")
    environment: str = Field(default="development", env="ENVIRONMENT")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    TESTING: bool = Field(default=False, env="TESTING")
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")

    # Security
    SECRET_KEY: str = Field(default="default_secret_key_change_in_production", env="SECRET_KEY")
    ENCRYPTION_KEY: str = Field(default="default_encryption_key_change_in_production", env="ENCRYPTION_KEY")

    # Web interface
    WEB_HOST: str = Field(default="0.0.0.0", env="WEB_HOST")
    WEB_PORT: int = Field(default=8000, env="WEB_PORT")
    STREAMLIT_PORT: int = Field(default=8501, env="STREAMLIT_PORT")

    # Celery
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")

    # Performance
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")
    REQUEST_TIMEOUT: int = Field(default=30, env="REQUEST_TIMEOUT")
    MAX_RETRIES: int = Field(default=3, env="MAX_RETRIES")

    # Configuration sections
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    cache: RedisSettings = Field(default_factory=RedisSettings)  # Alias for cache
    exchanges: ExchangeSettings = Field(default_factory=ExchangeSettings)
    trading: TradingSettings = Field(default_factory=TradingSettings)
    risk: RiskSettings = Field(default_factory=RiskSettings)
    ai: AISettings = Field(default_factory=AISettings)
    data_sources: DataSourceSettings = Field(default_factory=DataSourceSettings)
    news: DataSourceSettings = Field(default_factory=DataSourceSettings)  # Alias for news
    social_media: DataSourceSettings = Field(default_factory=DataSourceSettings)  # Alias for social media
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    backtesting: BacktestingSettings = Field(default_factory=BacktestingSettings)
    market_data: MarketDataSettings = Field(default_factory=MarketDataSettings)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure logs directory exists
        os.makedirs(os.path.dirname(self.logging.file_path), exist_ok=True)
