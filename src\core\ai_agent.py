"""
AI Trading Agent for AI Trading Bot System.

This module contains the AI agent that makes trading decisions
based on technical analysis, sentiment analysis, and market data.

Author: inkbytefo
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..config.settings import Settings
from ..ai.ai_manager import AIManager


class AITradingAgent:
    """
    AI-powered trading agent that makes trading decisions.
    
    Combines technical analysis, sentiment analysis, and AI models
    to generate trading signals and make trading decisions.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # AI manager for model interactions
        self.ai_manager = AIManager(settings)
        
        # Agent state
        self.is_active = False
        self.decision_history: List[Dict[str, Any]] = []
        
        # Decision weights
        self.weights = {
            "technical": settings.ai.technical_weight,
            "sentiment": settings.ai.sentiment_weight,
            "ai_prediction": 0.4  # Remaining weight for AI predictions
        }
    
    async def initialize(self):
        """Initialize the AI trading agent."""
        try:
            self.logger.info("Initializing AI Trading Agent...")
            
            # Initialize AI manager
            await self.ai_manager.initialize()
            
            self.is_active = True
            self.logger.info("AI Trading Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Trading Agent: {e}")
            raise
    
    async def analyze_market(self, symbol: str, market_data: Dict[str, Any],
                           technical_analysis: Dict[str, Any],
                           sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market conditions and generate trading decision.
        
        Args:
            symbol: Trading pair symbol
            market_data: Current market data
            technical_analysis: Technical analysis results
            sentiment_analysis: Sentiment analysis results
            
        Returns:
            Trading decision with confidence and reasoning
        """
        try:
            if not self.is_active:
                return {"error": "AI agent not active"}
            
            # Prepare analysis context
            analysis_context = {
                "symbol": symbol,
                "timestamp": datetime.utcnow().isoformat(),
                "market_data": market_data,
                "technical": technical_analysis,
                "sentiment": sentiment_analysis
            }
            
            # Get AI prediction
            ai_prediction = await self._get_ai_prediction(analysis_context)
            
            # Combine all signals
            combined_decision = self._combine_signals(
                technical_analysis, sentiment_analysis, ai_prediction
            )
            
            # Add reasoning and metadata
            decision = {
                "symbol": symbol,
                "timestamp": datetime.utcnow().isoformat(),
                "decision": combined_decision["action"],
                "confidence": combined_decision["confidence"],
                "reasoning": combined_decision["reasoning"],
                "signals": {
                    "technical": technical_analysis.get("signals", {}),
                    "sentiment": sentiment_analysis.get("signals", {}),
                    "ai_prediction": ai_prediction
                },
                "risk_assessment": self._assess_risk(analysis_context, combined_decision),
                "metadata": {
                    "weights_used": self.weights,
                    "model_version": "1.0.0"
                }
            }
            
            # Store decision in history
            self.decision_history.append(decision)
            
            # Keep only last 1000 decisions
            if len(self.decision_history) > 1000:
                self.decision_history = self.decision_history[-1000:]
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Market analysis failed for {symbol}: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "decision": "hold",
                "confidence": 0.0
            }
    
    async def _get_ai_prediction(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get AI model prediction for the market context."""
        try:
            # Prepare prompt for AI model
            prompt = self._prepare_analysis_prompt(context)
            
            # Get prediction from AI model
            response = await self.ai_manager.generate_prediction(
                prompt=prompt,
                model_type="prediction",
                context=context
            )
            
            # Parse AI response
            prediction = self._parse_ai_response(response)
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"AI prediction failed: {e}")
            return {
                "action": "hold",
                "confidence": 0.0,
                "reasoning": f"AI prediction error: {str(e)}"
            }
    
    def _prepare_analysis_prompt(self, context: Dict[str, Any]) -> str:
        """Prepare prompt for AI analysis."""
        symbol = context["symbol"]
        technical = context.get("technical", {})
        sentiment = context.get("sentiment", {})
        market_data = context.get("market_data", {})
        
        prompt = f"""
        Analyze the following cryptocurrency trading data for {symbol} and provide a trading recommendation:

        MARKET DATA:
        - Current Price: {market_data.get('current_price', 'N/A')}
        - Volume: {market_data.get('volume', 'N/A')}
        - 24h Change: {market_data.get('price_change_24h', 'N/A')}%

        TECHNICAL ANALYSIS:
        - Overall Signal: {technical.get('signals', {}).get('overall', 'neutral')}
        - Signal Strength: {technical.get('signals', {}).get('strength', 0)}
        - RSI: {technical.get('indicators', {}).get('rsi', 'N/A')}
        - MACD: {technical.get('indicators', {}).get('macd', 'N/A')}
        - Trend Direction: {technical.get('trend', {}).get('direction', 'neutral')}

        SENTIMENT ANALYSIS:
        - Overall Sentiment: {sentiment.get('overall_label', 'neutral')}
        - Sentiment Score: {sentiment.get('overall_score', 0)}
        - Confidence: {sentiment.get('confidence', 0)}

        Based on this analysis, provide:
        1. Trading action (buy/sell/hold)
        2. Confidence level (0-1)
        3. Brief reasoning (2-3 sentences)

        Respond in JSON format:
        {{
            "action": "buy/sell/hold",
            "confidence": 0.0-1.0,
            "reasoning": "explanation"
        }}
        """
        
        return prompt
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI model response."""
        try:
            import json
            
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                # Validate required fields
                action = parsed.get("action", "hold").lower()
                if action not in ["buy", "sell", "hold"]:
                    action = "hold"
                
                confidence = float(parsed.get("confidence", 0.0))
                confidence = max(0.0, min(1.0, confidence))  # Clamp to 0-1
                
                reasoning = parsed.get("reasoning", "AI analysis completed")
                
                return {
                    "action": action,
                    "confidence": confidence,
                    "reasoning": reasoning
                }
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            self.logger.error(f"Failed to parse AI response: {e}")
            return {
                "action": "hold",
                "confidence": 0.0,
                "reasoning": f"Failed to parse AI response: {str(e)}"
            }
    
    def _combine_signals(self, technical: Dict[str, Any], sentiment: Dict[str, Any],
                        ai_prediction: Dict[str, Any]) -> Dict[str, Any]:
        """Combine different signals into final decision."""
        try:
            # Extract signals
            tech_signal = technical.get("signals", {}).get("overall", "neutral")
            tech_strength = technical.get("signals", {}).get("strength", 0.0)
            
            sent_signal = sentiment.get("signals", {}).get("signal", "neutral")
            sent_strength = sentiment.get("signals", {}).get("strength", 0.0)
            
            ai_signal = ai_prediction.get("action", "neutral")
            ai_confidence = ai_prediction.get("confidence", 0.0)
            
            # Convert signals to numeric scores (-1 to 1)
            def signal_to_score(signal, strength):
                if signal == "buy":
                    return strength
                elif signal == "sell":
                    return -strength
                else:
                    return 0.0
            
            tech_score = signal_to_score(tech_signal, tech_strength)
            sent_score = signal_to_score(sent_signal, sent_strength)
            ai_score = signal_to_score(ai_signal, ai_confidence)
            
            # Calculate weighted average
            total_score = (
                tech_score * self.weights["technical"] +
                sent_score * self.weights["sentiment"] +
                ai_score * self.weights["ai_prediction"]
            )
            
            # Determine final action
            if total_score > 0.3:
                action = "buy"
            elif total_score < -0.3:
                action = "sell"
            else:
                action = "hold"
            
            # Calculate confidence
            confidence = min(abs(total_score), 1.0)
            
            # Generate reasoning
            reasoning_parts = []
            if abs(tech_score) > 0.1:
                reasoning_parts.append(f"Technical: {tech_signal} ({tech_strength:.2f})")
            if abs(sent_score) > 0.1:
                reasoning_parts.append(f"Sentiment: {sent_signal} ({sent_strength:.2f})")
            if abs(ai_score) > 0.1:
                reasoning_parts.append(f"AI: {ai_signal} ({ai_confidence:.2f})")
            
            reasoning = f"Combined analysis suggests {action}. " + "; ".join(reasoning_parts)
            
            return {
                "action": action,
                "confidence": confidence,
                "reasoning": reasoning,
                "component_scores": {
                    "technical": tech_score,
                    "sentiment": sent_score,
                    "ai_prediction": ai_score,
                    "total": total_score
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to combine signals: {e}")
            return {
                "action": "hold",
                "confidence": 0.0,
                "reasoning": f"Signal combination error: {str(e)}"
            }
    
    def _assess_risk(self, context: Dict[str, Any], decision: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for the trading decision."""
        try:
            # Basic risk assessment
            risk_factors = []
            risk_score = 0.0
            
            # Market volatility risk
            volatility = context.get("technical", {}).get("volatility", {})
            if volatility.get("daily", 0) > 0.05:  # 5% daily volatility
                risk_factors.append("High volatility")
                risk_score += 0.3
            
            # Sentiment uncertainty
            sentiment_confidence = context.get("sentiment", {}).get("confidence", 1.0)
            if sentiment_confidence < 0.5:
                risk_factors.append("Low sentiment confidence")
                risk_score += 0.2
            
            # Technical signal strength
            tech_strength = context.get("technical", {}).get("signals", {}).get("strength", 1.0)
            if tech_strength < 0.5:
                risk_factors.append("Weak technical signals")
                risk_score += 0.2
            
            # Decision confidence
            if decision.get("confidence", 0) < 0.6:
                risk_factors.append("Low decision confidence")
                risk_score += 0.3
            
            risk_level = "low"
            if risk_score > 0.7:
                risk_level = "high"
            elif risk_score > 0.4:
                risk_level = "medium"
            
            return {
                "level": risk_level,
                "score": min(risk_score, 1.0),
                "factors": risk_factors,
                "recommendation": "Reduce position size" if risk_level == "high" else "Normal position size"
            }
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {e}")
            return {
                "level": "high",
                "score": 1.0,
                "factors": ["Risk assessment error"],
                "recommendation": "Avoid trading"
            }
    
    def get_decision_history(self, symbol: Optional[str] = None, 
                           hours: int = 24) -> List[Dict[str, Any]]:
        """Get decision history for analysis."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            filtered_decisions = []
            for decision in self.decision_history:
                decision_time = datetime.fromisoformat(decision["timestamp"].replace('Z', '+00:00'))
                
                if decision_time >= cutoff_time:
                    if symbol is None or decision.get("symbol") == symbol:
                        filtered_decisions.append(decision)
            
            return filtered_decisions
            
        except Exception as e:
            self.logger.error(f"Failed to get decision history: {e}")
            return []
    
    async def shutdown(self):
        """Shutdown the AI trading agent."""
        try:
            self.is_active = False
            await self.ai_manager.shutdown()
            self.logger.info("AI Trading Agent shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during AI agent shutdown: {e}")

    async def make_decisions(self, market_data: Dict[str, Any],
                           technical_analysis: Dict[str, Any],
                           sentiment_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make trading decisions based on market data and analysis.

        Args:
            market_data: Current market data
            technical_analysis: Technical analysis results
            sentiment_analysis: Optional sentiment analysis results

        Returns:
            Dict containing trading decisions and recommendations
        """
        try:
            if not self.is_active:
                return {
                    "status": "inactive",
                    "decisions": [],
                    "message": "AI Trading Agent is not active"
                }

            decisions = []

            # Process each symbol in market data
            symbols = market_data.get("symbols", [])
            if not symbols and "symbol" in market_data:
                symbols = [market_data["symbol"]]

            for symbol in symbols:
                try:
                    # Get symbol-specific data
                    symbol_data = market_data.get(symbol, market_data)
                    symbol_technical = technical_analysis.get(symbol, technical_analysis)
                    symbol_sentiment = sentiment_analysis.get(symbol, {}) if sentiment_analysis else {}

                    # Make decision for this symbol
                    decision = await self.analyze_and_decide(
                        symbol=symbol,
                        market_data=symbol_data,
                        technical_analysis=symbol_technical,
                        sentiment_analysis=symbol_sentiment
                    )

                    decisions.append(decision)

                except Exception as e:
                    self.logger.error(f"Error making decision for {symbol}: {e}")
                    decisions.append({
                        "symbol": symbol,
                        "decision": "hold",
                        "confidence": 0.0,
                        "reasoning": f"Error in analysis: {str(e)}",
                        "error": str(e)
                    })

            # If no symbols processed, make a general decision
            if not decisions:
                decision = await self.analyze_and_decide(
                    symbol="UNKNOWN",
                    market_data=market_data,
                    technical_analysis=technical_analysis,
                    sentiment_analysis=sentiment_analysis or {}
                )
                decisions.append(decision)

            return {
                "status": "success",
                "timestamp": datetime.utcnow().isoformat(),
                "decisions": decisions,
                "total_decisions": len(decisions),
                "agent_status": "active" if self.is_active else "inactive"
            }

        except Exception as e:
            self.logger.error(f"Error in make_decisions: {e}")
            return {
                "status": "error",
                "timestamp": datetime.utcnow().isoformat(),
                "decisions": [],
                "error": str(e),
                "message": "Failed to make trading decisions"
            }

    async def analyze_and_decide(self, symbol: str, market_data: Dict[str, Any],
                               technical_analysis: Dict[str, Any],
                               sentiment_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analyze market data and make a trading decision for a specific symbol.

        Args:
            symbol: Trading pair symbol
            market_data: Market data for the symbol
            technical_analysis: Technical analysis results
            sentiment_analysis: Optional sentiment analysis results

        Returns:
            Dict containing trading decision for the symbol
        """
        try:
            # Extract key information
            price = None
            if isinstance(market_data, dict):
                if 'ticker' in market_data and market_data['ticker']:
                    price = market_data['ticker'].get('last') or market_data['ticker'].get('close')
                elif 'close' in market_data:
                    price = market_data['close']
                elif 'price' in market_data:
                    price = market_data['price']

            # Get technical signals
            technical_signal = "neutral"
            technical_confidence = 0.5

            if isinstance(technical_analysis, dict):
                signals = technical_analysis.get('signals', {})
                if isinstance(signals, dict):
                    technical_signal = signals.get('overall', 'neutral')
                    strength = signals.get('strength', 'medium')

                    # Convert strength to confidence
                    strength_map = {
                        'very_weak': 0.1, 'weak': 0.3, 'medium': 0.5,
                        'strong': 0.7, 'very_strong': 0.9
                    }
                    technical_confidence = strength_map.get(strength, 0.5)

            # Get sentiment signal
            sentiment_signal = "neutral"
            sentiment_confidence = 0.5

            if isinstance(sentiment_analysis, dict):
                sentiment_signal = sentiment_analysis.get('overall_sentiment', 'neutral')
                sentiment_confidence = sentiment_analysis.get('confidence', 0.5)

            # Combine signals to make decision
            decision = "hold"
            confidence = 0.5
            reasoning = []

            # Technical analysis weight: 60%
            # Sentiment analysis weight: 40%
            tech_weight = 0.6
            sent_weight = 0.4

            # Calculate combined score
            tech_score = 0
            if technical_signal == "bullish" or technical_signal == "buy":
                tech_score = technical_confidence
            elif technical_signal == "bearish" or technical_signal == "sell":
                tech_score = -technical_confidence

            sent_score = 0
            if sentiment_signal == "bullish" or sentiment_signal == "positive":
                sent_score = sentiment_confidence
            elif sentiment_signal == "bearish" or sentiment_signal == "negative":
                sent_score = -sentiment_confidence

            combined_score = (tech_score * tech_weight) + (sent_score * sent_weight)

            # Make decision based on combined score
            if combined_score > 0.3:
                decision = "buy"
                confidence = min(abs(combined_score), 0.9)
                reasoning.append(f"Bullish signals: technical={technical_signal}, sentiment={sentiment_signal}")
            elif combined_score < -0.3:
                decision = "sell"
                confidence = min(abs(combined_score), 0.9)
                reasoning.append(f"Bearish signals: technical={technical_signal}, sentiment={sentiment_signal}")
            else:
                decision = "hold"
                confidence = 0.5 - abs(combined_score)
                reasoning.append(f"Mixed/neutral signals: technical={technical_signal}, sentiment={sentiment_signal}")

            # Add price information to reasoning
            if price:
                reasoning.append(f"Current price: {price}")

            return {
                "symbol": symbol,
                "decision": decision,
                "confidence": round(confidence, 3),
                "reasoning": "; ".join(reasoning),
                "technical_signal": technical_signal,
                "technical_confidence": technical_confidence,
                "sentiment_signal": sentiment_signal,
                "sentiment_confidence": sentiment_confidence,
                "combined_score": round(combined_score, 3),
                "price": price,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in analyze_and_decide for {symbol}: {e}")
            return {
                "symbol": symbol,
                "decision": "hold",
                "confidence": 0.0,
                "reasoning": f"Error in analysis: {str(e)}",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
