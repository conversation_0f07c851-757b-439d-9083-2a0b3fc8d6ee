# Database Configuration
DATABASE_URL=postgresql://tradingbot:tradingbot123@localhost:5432/trading_bot
REDIS_URL=redis://:redis123@localhost:6379/0

# Exchange API Keys (NEVER commit real keys to version control)
# Binance
BINANCE_API_KEY=
BINANCE_SECRET_KEY=
BINANCE_TESTNET=true

# Coinbase Pro
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here
COINBASE_SANDBOX=true

# Kraken
KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_SECRET_KEY=your_kraken_secret_key_here

# Security
SECRET_KEY=your_super_secret_key_here_change_this_in_production
ENCRYPTION_KEY=your_encryption_key_for_sensitive_data

# Trading Configuration
# DEFAULT_TRADING_PAIRS - Now defined in code with extensive coin list
DEFAULT_BASE_CURRENCY=USDT
MAX_POSITION_SIZE=0.1  # Maximum 10% of portfolio per position
STOP_LOSS_PERCENTAGE=0.05  # 5% stop loss
TAKE_PROFIT_PERCENTAGE=0.15  # 15% take profit

# Risk Management
MAX_DAILY_LOSS=0.02  # Maximum 2% daily loss
MAX_DRAWDOWN=0.10  # Maximum 10% drawdown
EMERGENCY_STOP_LOSS=0.20  # Emergency stop at 20% loss

# AI/ML Configuration
MODEL_UPDATE_INTERVAL=3600  # Update models every hour
PREDICTION_CONFIDENCE_THRESHOLD=0.7
SENTIMENT_WEIGHT=0.3
TECHNICAL_WEIGHT=0.7

# Data Sources
NEWS_API_KEY=
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# Monitoring & Alerting
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
DISCORD_WEBHOOK_URL=your_discord_webhook_url_here

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=logs/trading_bot.log
LOG_ROTATION_SIZE=100MB
LOG_RETENTION_DAYS=30

# Development Settings
DEBUG=false
TESTING=false
PAPER_TRADING=true  # Start with paper trading for safety

# Web Interface
WEB_HOST=0.0.0.0
WEB_PORT=8000
STREAMLIT_PORT=8501

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Backtesting
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_BALANCE=10000

# Performance
MAX_WORKERS=4
CACHE_TTL=300  # 5 minutes cache TTL
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# AI Provider Configuration (NEVER commit real keys to version control)
# Primary OpenAI Configuration
OPENAI_API_KEY=
OPENAI_BASE_URL=https://openrouter.ai/api/v1
OPENAI_MODEL=openai/gpt-oss-20b:free
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=30

# Alternative Provider URLs (users can switch by changing OPENAI_BASE_URL)
# OpenAI Official: https://api.openai.com/v1
# OpenRouter: https://openrouter.ai/api/v1
# Azure OpenAI: https://your-resource.openai.azure.com/
# Local (Ollama): http://localhost:11434/v1
# Local (LM Studio): http://localhost:1234/v1

# AI Model Selection for Different Tasks
AI_SENTIMENT_MODEL=mistralai/mistral-small-3.2-24b-instruct:free
AI_PREDICTION_MODEL=openai/gpt-oss-20b:free
AI_ANALYSIS_MODEL=openrouter/horizon-beta
AI_GENERAL_MODEL=openai/gpt-oss-20b:free

# Azure OpenAI (Optional)
# AZURE_OPENAI_KEY=your_azure_openai_key_here
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_VERSION=2023-12-01-preview

# Anthropic Claude (Optional)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-sonnet-********

# Google AI (Optional)
# GOOGLE_AI_KEY=your_google_ai_key_here
# GOOGLE_AI_MODEL=gemini-pro

# Local AI (Optional - for Ollama, LM Studio, etc.)
# LOCAL_AI_URL=http://localhost:11434
# LOCAL_AI_MODEL=llama2:7b

# AI Configuration
AI_ROUTING_STRATEGY=health_based
AI_FALLBACK_ENABLED=true
AI_MAX_RETRIES=3
AI_TIMEOUT_SECONDS=30
AI_MAX_COST_PER_REQUEST=1.0
AI_DAILY_BUDGET=50.0

# Application Configuration
APP_NAME=AI Trading Bot
VERSION=1.0.0
ENVIRONMENT=development

# Database Configuration (Additional)
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration (Additional)
REDIS_MAX_CONNECTIONS=10

# Docker Passwords
POSTGRES_PASSWORD=tradingbot123
REDIS_PASSWORD=redis123
GRAFANA_PASSWORD=admin123

# Backtesting Configuration
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_BALANCE=10000

# Market Data Collection
# MARKET_DATA_TIMEFRAMES - Now defined in code with default timeframes
MARKET_DATA_COLLECTION_INTERVAL=60
