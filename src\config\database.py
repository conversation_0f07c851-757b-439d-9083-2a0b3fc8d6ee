"""
Database configuration and connection management for AI Trading Bot System.

This module provides database connection management, session handling,
and SQLAlchemy configuration.

Author: inkbytefo
"""

import logging
from typing import Generator, Optional
from contextlib import contextmanager

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.engine import Engine

from .settings import Settings


# SQLAlchemy Base
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


class DatabaseManager:
    """
    Database manager for handling connections and sessions.
    
    Provides connection pooling, session management, and database
    initialization for the trading bot system.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
    
    def initialize(self):
        """Initialize database connection and session factory."""
        try:
            self.logger.info("Initializing database connection...")
            
            # Create engine with connection pooling
            self._engine = create_engine(
                self.settings.database.url,
                echo=self.settings.database.echo,
                poolclass=QueuePool,
                pool_size=self.settings.database.pool_size,
                max_overflow=self.settings.database.max_overflow,
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=3600,   # Recycle connections every hour
            )
            
            # Create session factory
            self._session_factory = sessionmaker(
                bind=self._engine,
                autocommit=False,
                autoflush=False
            )
            
            # Test connection
            from sqlalchemy import text
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.logger.info("✅ Database connection initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize database: {e}")
            raise
    
    @property
    def engine(self) -> Engine:
        """Get database engine."""
        if self._engine is None:
            raise RuntimeError("Database not initialized")
        return self._engine
    
    def get_session(self) -> Session:
        """
        Get a new database session.
        
        Returns:
            SQLAlchemy session
        """
        if self._session_factory is None:
            raise RuntimeError("Database not initialized")
        
        return self._session_factory()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """
        Provide a transactional scope around a series of operations.
        
        Yields:
            SQLAlchemy session with automatic commit/rollback
        """
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """Create all database tables."""
        try:
            self.logger.info("Creating database tables...")
            Base.metadata.create_all(bind=self._engine)
            self.logger.info("✅ Database tables created")
        except Exception as e:
            self.logger.error(f"❌ Failed to create tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables."""
        try:
            self.logger.warning("Dropping all database tables...")
            Base.metadata.drop_all(bind=self._engine)
            self.logger.info("✅ Database tables dropped")
        except Exception as e:
            self.logger.error(f"❌ Failed to drop tables: {e}")
            raise
    
    def health_check(self) -> bool:
        """
        Check database health.

        Returns:
            True if database is healthy, False otherwise
        """
        try:
            from sqlalchemy import text
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
    
    def get_connection_info(self) -> dict:
        """
        Get database connection information.
        
        Returns:
            Dictionary with connection details
        """
        if self._engine is None:
            return {"status": "not_initialized"}
        
        pool = self._engine.pool
        return {
            "status": "connected",
            "url": str(self._engine.url).replace(self._engine.url.password or "", "***"),
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    def close(self):
        """Close database connections."""
        try:
            if self._engine:
                self._engine.dispose()
                self.logger.info("Database connections closed")
        except Exception as e:
            self.logger.error(f"Error closing database connections: {e}")


# Global database manager instance
db_manager: Optional[DatabaseManager] = None


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global db_manager
    if db_manager is None:
        from .settings import Settings
        settings = Settings()
        db_manager = DatabaseManager(settings)
        # Don't initialize automatically - let caller handle it
    return db_manager


def get_db_session() -> Session:
    """Get a database session."""
    return get_db_manager().get_session()


@contextmanager
def db_session_scope() -> Generator[Session, None, None]:
    """Get a database session with automatic transaction management."""
    with get_db_manager().session_scope() as session:
        yield session
