"""
SQLAlchemy models for AI Trading Bot System.

This module defines all database models for storing trading data,
user information, strategies, and system metrics.

Author: inkbytefo
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from decimal import Decimal

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint, Numeric
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .database import Base


class TimestampMixin:
    """Mixin for adding timestamp columns."""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)


class User(Base, TimestampMixin):
    """User model for authentication and authorization."""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    salt = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    trading_accounts = relationship("TradingAccount", back_populates="user")
    strategies = relationship("Strategy", back_populates="user")
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"


class TradingAccount(Base, TimestampMixin):
    """Trading account model for exchange credentials."""
    __tablename__ = "trading_accounts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    exchange = Column(String(50), nullable=False)  # binance, coinbase, kraken
    account_name = Column(String(100), nullable=False)
    encrypted_credentials = Column(JSONB, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_testnet = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="trading_accounts")
    orders = relationship("Order", back_populates="trading_account")
    positions = relationship("Position", back_populates="trading_account")
    
    __table_args__ = (
        UniqueConstraint('user_id', 'exchange', 'account_name', name='unique_user_exchange_account'),
        Index('idx_trading_account_user_exchange', 'user_id', 'exchange'),
    )
    
    def __repr__(self):
        return f"<TradingAccount(exchange='{self.exchange}', account='{self.account_name}')>"


class TradingPair(Base, TimestampMixin):
    """Trading pair model for cryptocurrency pairs."""
    __tablename__ = "trading_pairs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    symbol = Column(String(20), unique=True, nullable=False, index=True)  # BTC/USDT
    base_asset = Column(String(10), nullable=False)  # BTC
    quote_asset = Column(String(10), nullable=False)  # USDT
    exchange = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Trading parameters
    min_order_size = Column(Numeric(20, 8))
    max_order_size = Column(Numeric(20, 8))
    price_precision = Column(Integer, default=8)
    quantity_precision = Column(Integer, default=8)
    
    # Relationships
    market_data = relationship("MarketData", back_populates="trading_pair")
    orders = relationship("Order", back_populates="trading_pair")
    positions = relationship("Position", back_populates="trading_pair")
    
    __table_args__ = (
        Index('idx_trading_pair_exchange_symbol', 'exchange', 'symbol'),
    )
    
    def __repr__(self):
        return f"<TradingPair(symbol='{self.symbol}', exchange='{self.exchange}')>"


class MarketData(Base, TimestampMixin):
    """Market data model for OHLCV data."""
    __tablename__ = "market_data"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trading_pair_id = Column(UUID(as_uuid=True), ForeignKey("trading_pairs.id"), nullable=False)
    timeframe = Column(String(10), nullable=False)  # 1m, 5m, 1h, 1d
    timestamp = Column(DateTime(timezone=True), nullable=False)
    
    # OHLCV data
    open_price = Column(Numeric(20, 8), nullable=False)
    high_price = Column(Numeric(20, 8), nullable=False)
    low_price = Column(Numeric(20, 8), nullable=False)
    close_price = Column(Numeric(20, 8), nullable=False)
    volume = Column(Numeric(20, 8), nullable=False)
    
    # Additional metrics
    vwap = Column(Numeric(20, 8))  # Volume Weighted Average Price
    trades_count = Column(Integer)
    
    # Relationships
    trading_pair = relationship("TradingPair", back_populates="market_data")
    
    __table_args__ = (
        UniqueConstraint('trading_pair_id', 'timeframe', 'timestamp', name='unique_market_data'),
        Index('idx_market_data_pair_timeframe_timestamp', 'trading_pair_id', 'timeframe', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<MarketData(pair='{self.trading_pair.symbol}', timeframe='{self.timeframe}', timestamp='{self.timestamp}')>"


class Strategy(Base, TimestampMixin):
    """Strategy model for trading strategies."""
    __tablename__ = "strategies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    strategy_type = Column(String(50), nullable=False)  # momentum, mean_reversion, arbitrage
    
    # Strategy configuration
    parameters = Column(JSONB, nullable=False, default={})
    is_active = Column(Boolean, default=False, nullable=False)
    
    # Performance metrics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_pnl = Column(Numeric(20, 8), default=0)
    max_drawdown = Column(Numeric(10, 4), default=0)
    sharpe_ratio = Column(Numeric(10, 4))
    
    # Relationships
    user = relationship("User", back_populates="strategies")
    signals = relationship("TradingSignal", back_populates="strategy")
    
    __table_args__ = (
        Index('idx_strategy_user_type', 'user_id', 'strategy_type'),
    )
    
    def __repr__(self):
        return f"<Strategy(name='{self.name}', type='{self.strategy_type}')>"


class TradingSignal(Base, TimestampMixin):
    """Trading signal model for AI-generated signals."""
    __tablename__ = "trading_signals"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey("strategies.id"), nullable=False)
    trading_pair_id = Column(UUID(as_uuid=True), ForeignKey("trading_pairs.id"), nullable=False)
    
    # Signal details
    signal_type = Column(String(10), nullable=False)  # BUY, SELL, HOLD
    confidence = Column(Numeric(5, 4), nullable=False)  # 0.0 to 1.0
    strength = Column(Numeric(5, 4), nullable=False)   # 0.0 to 1.0
    
    # Price levels
    entry_price = Column(Numeric(20, 8))
    stop_loss = Column(Numeric(20, 8))
    take_profit = Column(Numeric(20, 8))
    
    # Signal metadata
    timeframe = Column(String(10), nullable=False)
    indicators_used = Column(JSONB, default={})
    market_conditions = Column(JSONB, default={})
    
    # Status
    status = Column(String(20), default='ACTIVE')  # ACTIVE, EXECUTED, EXPIRED, CANCELLED
    executed_at = Column(DateTime(timezone=True))
    
    # Relationships
    strategy = relationship("Strategy", back_populates="signals")
    trading_pair = relationship("TradingPair")
    orders = relationship("Order", back_populates="signal")
    
    __table_args__ = (
        Index('idx_signal_strategy_pair_timestamp', 'strategy_id', 'trading_pair_id', 'created_at'),
        CheckConstraint('confidence >= 0 AND confidence <= 1', name='check_confidence_range'),
        CheckConstraint('strength >= 0 AND strength <= 1', name='check_strength_range'),
    )
    
    def __repr__(self):
        return f"<TradingSignal(type='{self.signal_type}', confidence={self.confidence})>"


class Order(Base, TimestampMixin):
    """Order model for tracking trading orders."""
    __tablename__ = "orders"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trading_account_id = Column(UUID(as_uuid=True), ForeignKey("trading_accounts.id"), nullable=False)
    trading_pair_id = Column(UUID(as_uuid=True), ForeignKey("trading_pairs.id"), nullable=False)
    signal_id = Column(UUID(as_uuid=True), ForeignKey("trading_signals.id"))
    
    # Order details
    exchange_order_id = Column(String(100), index=True)  # Exchange-specific order ID
    order_type = Column(String(20), nullable=False)  # MARKET, LIMIT, STOP_LOSS
    side = Column(String(10), nullable=False)  # BUY, SELL
    
    # Quantities and prices
    quantity = Column(Numeric(20, 8), nullable=False)
    price = Column(Numeric(20, 8))  # NULL for market orders
    filled_quantity = Column(Numeric(20, 8), default=0)
    average_price = Column(Numeric(20, 8))
    
    # Status and timing
    status = Column(String(20), nullable=False, default='PENDING')  # PENDING, FILLED, CANCELLED, REJECTED
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())
    filled_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))
    
    # Fees
    fee_amount = Column(Numeric(20, 8), default=0)
    fee_currency = Column(String(10))
    
    # Relationships
    trading_account = relationship("TradingAccount", back_populates="orders")
    trading_pair = relationship("TradingPair", back_populates="orders")
    signal = relationship("TradingSignal", back_populates="orders")
    
    __table_args__ = (
        Index('idx_order_account_pair_status', 'trading_account_id', 'trading_pair_id', 'status'),
        Index('idx_order_exchange_id', 'exchange_order_id'),
        CheckConstraint('quantity > 0', name='check_positive_quantity'),
        CheckConstraint('filled_quantity >= 0', name='check_non_negative_filled'),
    )
    
    def __repr__(self):
        return f"<Order(side='{self.side}', quantity={self.quantity}, status='{self.status}')>"


class Position(Base, TimestampMixin):
    """Position model for tracking open positions."""
    __tablename__ = "positions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trading_account_id = Column(UUID(as_uuid=True), ForeignKey("trading_accounts.id"), nullable=False)
    trading_pair_id = Column(UUID(as_uuid=True), ForeignKey("trading_pairs.id"), nullable=False)
    
    # Position details
    side = Column(String(10), nullable=False)  # LONG, SHORT
    quantity = Column(Numeric(20, 8), nullable=False)
    entry_price = Column(Numeric(20, 8), nullable=False)
    current_price = Column(Numeric(20, 8))
    
    # P&L tracking
    unrealized_pnl = Column(Numeric(20, 8), default=0)
    realized_pnl = Column(Numeric(20, 8), default=0)
    
    # Risk management
    stop_loss = Column(Numeric(20, 8))
    take_profit = Column(Numeric(20, 8))
    
    # Status
    status = Column(String(20), default='OPEN')  # OPEN, CLOSED
    opened_at = Column(DateTime(timezone=True), server_default=func.now())
    closed_at = Column(DateTime(timezone=True))
    
    # Relationships
    trading_account = relationship("TradingAccount", back_populates="positions")
    trading_pair = relationship("TradingPair", back_populates="positions")
    
    __table_args__ = (
        Index('idx_position_account_pair_status', 'trading_account_id', 'trading_pair_id', 'status'),
        CheckConstraint('quantity > 0', name='check_positive_position_quantity'),
    )
    
    def __repr__(self):
        return f"<Position(side='{self.side}', quantity={self.quantity}, status='{self.status}')>"


class SystemMetrics(Base, TimestampMixin):
    """System metrics model for monitoring and analytics."""
    __tablename__ = "system_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Numeric(20, 8), nullable=False)
    metric_unit = Column(String(20))
    tags = Column(JSONB, default={})
    
    __table_args__ = (
        Index('idx_metrics_name_timestamp', 'metric_name', 'created_at'),
    )
    
    def __repr__(self):
        return f"<SystemMetrics(name='{self.metric_name}', value={self.metric_value})>"


class NewsArticle(Base, TimestampMixin):
    """News article model for storing cryptocurrency news."""
    __tablename__ = "news_articles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(500), nullable=False)
    content = Column(Text)
    source = Column(String(100), nullable=False)
    url = Column(String(1000), unique=True, nullable=False)
    author = Column(String(200))
    published_at = Column(DateTime(timezone=True), nullable=False)

    # Analysis results
    sentiment_score = Column(Float)  # -1 to 1
    relevance_score = Column(Float)  # 0 to 1
    keywords = Column(Text)  # Comma-separated keywords

    __table_args__ = (
        Index('idx_news_source_published', 'source', 'published_at'),
        Index('idx_news_sentiment_relevance', 'sentiment_score', 'relevance_score'),
    )

    def __repr__(self):
        return f"<NewsArticle(title='{self.title[:50]}...', source='{self.source}')>"


class SocialMediaPost(Base, TimestampMixin):
    """Social media post model for storing tweets, reddit posts, etc."""
    __tablename__ = "social_media_posts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform = Column(String(50), nullable=False)  # twitter, reddit, etc.
    post_id = Column(String(100), nullable=False)  # Platform-specific ID
    content = Column(Text, nullable=False)
    author = Column(String(100), nullable=False)
    url = Column(String(1000))

    # Timestamps
    posted_at = Column(DateTime(timezone=True), nullable=False)  # When post was created on platform

    # Analysis results
    engagement_score = Column(Float)  # Platform-specific engagement metric
    sentiment_score = Column(Float)  # -1 to 1
    influence_score = Column(Float)  # Author influence score
    hashtags = Column(Text)  # Comma-separated hashtags
    mentions = Column(Text)  # Comma-separated mentions

    __table_args__ = (
        UniqueConstraint('platform', 'post_id', name='unique_platform_post'),
        Index('idx_social_platform_posted', 'platform', 'posted_at'),
        Index('idx_social_sentiment_engagement', 'sentiment_score', 'engagement_score'),
    )

    def __repr__(self):
        return f"<SocialMediaPost(platform='{self.platform}', post_id='{self.post_id}')>"
