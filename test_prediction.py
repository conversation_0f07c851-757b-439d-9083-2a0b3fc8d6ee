#!/usr/bin/env python3
"""
Test Price Prediction specifically.

Author: inkbytefo
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.ai.services.ai_service import AIService

async def test_prediction():
    """Test price prediction."""
    print("📈 Price Prediction Test")
    print("=" * 30)
    
    # Initialize settings
    settings = Settings()

    print(f"✅ Prediction Model: {settings.ai.prediction_model}")
    print(f"✅ Analysis Model: {settings.ai.analysis_model}")
    print(f"✅ General Model: {settings.ai.general_model}")

    try:
        # Initialize AI Service
        ai_service = AIService(settings)
        await ai_service.initialize()
        
        print("✅ AI Service initialized successfully")
        
        # Test price prediction
        symbol = "BTC/USDT"
        timeframe = "1h"
        market_data = {
            "current_price": 45000.0,
            "volume_24h": 1000000000,
            "price_change_24h": 0.02,
            "high_24h": 46000.0,
            "low_24h": 44000.0
        }
        
        print(f"\n📈 Testing prediction for: {symbol} ({timeframe})")
        print(f"Current price: ${market_data['current_price']}")
        
        prediction = await ai_service.predict_price(symbol, timeframe, market_data)
        
        print(f"✅ Direction: {prediction.direction}")
        print(f"✅ Confidence: {prediction.confidence:.2f}")
        print(f"✅ Probability: {prediction.probability:.2f}")
        print(f"✅ Predicted Price: ${prediction.predicted_price:.2f}")
        print(f"✅ Reasoning: {prediction.reasoning}")
        
        # Cleanup
        if ai_service.provider:
            await ai_service.provider.cleanup()
        
        print("\n🎉 Prediction test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    success = await test_prediction()
    
    if success:
        print("\n🚀 Price prediction working!")
        return 0
    else:
        print("\n⚠️  Price prediction needs attention")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
