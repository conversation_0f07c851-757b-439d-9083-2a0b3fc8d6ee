#!/usr/bin/env python3
"""
Modern AI System Test for Trading Bot.

This script tests the modernized OpenAI-based AI system
to ensure everything is working correctly.

Author: inkbytefo
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.ai.providers.openai_compatible_provider import ModernOpenAIProvider, ProviderFactory
from src.ai.providers.base_provider import AIRequest, ProviderConfig, ProviderType
from src.ai.services.ai_service import AIService


class AISystemTester:
    """AI system tester."""
    
    def __init__(self):
        self.settings = Settings()
        self.test_results = {}
        self.failed_tests = []
        
    async def run_all_tests(self):
        """Run all AI system tests."""
        print("🤖 Starting AI System Test")
        print("=" * 60)
        
        tests = [
            ("Settings Configuration", self.test_settings),
            ("Modern OpenAI Provider", self.test_modern_provider),
            ("AI Service", self.test_ai_service),
            ("Integration Test", self.test_integration)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 Testing: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = result
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
                    self.failed_tests.append(test_name)
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
                self.failed_tests.append(test_name)
        
        # Print summary
        self.print_summary()
        
        return len(self.failed_tests) == 0
    
    async def test_settings(self):
        """Test AI settings configuration."""
        try:
            # Test AI settings loading
            assert hasattr(self.settings, 'ai'), "AI settings not found"
            
            # Test default values
            assert self.settings.ai.ai_routing_strategy == "health_based"
            assert self.settings.ai.ai_fallback_enabled == True
            assert self.settings.ai.ai_max_retries == 3
            
            print("  ✓ AI settings loaded correctly")
            print(f"  ✓ Routing strategy: {self.settings.ai.ai_routing_strategy}")
            print(f"  ✓ Fallback enabled: {self.settings.ai.ai_fallback_enabled}")
            print(f"  ✓ Max retries: {self.settings.ai.ai_max_retries}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Settings error: {e}")
            return False
    
    async def test_modern_provider(self):
        """Test modern OpenAI provider functionality."""
        try:
            # Create provider configuration
            config = ProviderConfig(
                name="test-openai",
                provider_type=ProviderType.OPENAI,
                api_key=self.settings.ai.openai_api_key or "test-key",
                base_url=self.settings.ai.openai_base_url,
                model_mapping={'test': self.settings.ai.general_model},
                timeout_seconds=30,
                rate_limit_rpm=60,
                rate_limit_tpm=10000
            )

            # Create provider
            provider = ModernOpenAIProvider(config, self.settings)

            # Test configuration
            assert provider.config.name == "test-openai"
            assert provider.config.provider_type == ProviderType.OPENAI

            print("  ✓ Modern OpenAI provider created successfully")
            print(f"  ✓ Provider type: {provider.config.provider_type.value}")
            print(f"  ✓ Base URL: {provider.config.base_url}")
            print(f"  ✓ Model: {provider.config.model_mapping.get('test', 'N/A')}")

            # Test statistics
            stats = provider.get_statistics()
            assert "name" in stats
            assert "total_requests" in stats

            print("  ✓ Provider statistics accessible")

            return True

        except Exception as e:
            print(f"  ❌ Modern provider error: {e}")
            return False
    

    
    async def test_ai_service(self):
        """Test modern AI service functionality."""
        try:
            # Create AI service
            ai_service = AIService(self.settings)

            # Test initialization (will fail without real API keys, but that's OK)
            try:
                await ai_service.initialize()
                print("  ✓ AI service initialized successfully")
            except Exception as e:
                print(f"  ⚠️  AI service initialization failed (expected without API key): {e}")

            # Test basic properties
            assert ai_service.settings is not None
            assert hasattr(ai_service, 'sentiment_cache')
            assert hasattr(ai_service, 'prediction_cache')
            assert hasattr(ai_service, 'analysis_cache')

            print("  ✓ AI service created successfully")
            print("  ✓ Cache systems initialized")
            print(f"  ✓ Cache TTL: {ai_service.cache_ttl.total_seconds()/60} minutes")

            return True

        except Exception as e:
            print(f"  ❌ AI service error: {e}")
            return False
    
    async def test_integration(self):
        """Test modern system integration."""
        try:
            # Test that all components can work together
            ai_service = AIService(self.settings)

            # Create a provider configuration
            config = ProviderConfig(
                name="integration-test",
                provider_type=ProviderType.OPENAI,
                api_key=self.settings.ai.openai_api_key or "test-key",
                base_url=self.settings.ai.openai_base_url,
                model_mapping={'test': self.settings.ai.general_model},
                timeout_seconds=30,
                rate_limit_rpm=60,
                rate_limit_tpm=10000
            )

            provider = ModernOpenAIProvider(config, self.settings)

            # Test configuration consistency
            assert provider.settings == ai_service.settings

            print("  ✓ Component integration working")
            print("  ✓ Configuration consistency verified")
            print("  ✓ Modern AI system ready")

            return True

        except Exception as e:
            print(f"  ❌ Integration error: {e}")
            return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("🤖 AI SYSTEM TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in self.failed_tests:
                print(f"  - {test}")
        
        if failed_tests == 0:
            print("\n🎉 ALL AI TESTS PASSED! System is ready for AI integration!")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please review before proceeding.")
        
        print("\n📝 Next Steps:")
        print("1. Add real API keys to .env file for full functionality")
        print("2. Test with actual AI providers (OpenAI, local models, etc.)")
        print("3. Configure provider priorities and routing strategies")
        print("4. Implement trading-specific AI prompts and models")
        
        print("=" * 60)


async def main():
    """Main test runner."""
    tester = AISystemTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 AI System is ready for integration!")
        return 0
    else:
        print("\n⚠️  AI System has issues that need to be resolved.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
