#!/usr/bin/env python3
"""
Simple AI Test - Test only sentiment analysis.

Author: inkbytefo
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.ai.services.ai_service import AIService

async def test_simple_sentiment():
    """Test simple sentiment analysis."""
    print("🤖 Simple AI Sentiment Test")
    print("=" * 40)
    
    # Initialize settings
    settings = Settings()
    
    # Check if we have AI API keys
    if not settings.ai.openai_api_key or settings.ai.openai_api_key == "your_openai_api_key_here":
        print("❌ No OpenAI API key found in .env file")
        return False

    print(f"✅ OpenAI API Key found: {settings.ai.openai_api_key[:20]}...")
    print(f"✅ OpenAI Base URL: {settings.ai.openai_base_url}")
    print(f"✅ Sentiment Model: {settings.ai.sentiment_model}")

    try:
        # Initialize AI Service
        ai_service = AIService(settings)
        await ai_service.initialize()
        
        print("✅ AI Service initialized successfully")
        
        # Test simple sentiment
        test_text = "Bitcoin reaches new all-time high as institutional adoption increases"
        print(f"\n🔍 Testing sentiment for: {test_text[:50]}...")
        
        sentiment = await ai_service.analyze_sentiment(test_text, context="crypto_news")
        
        print(f"✅ Sentiment: {sentiment.sentiment}")
        print(f"✅ Confidence: {sentiment.confidence:.2f}")
        print(f"✅ Score: {sentiment.score:.2f}")
        print(f"✅ Reasoning: {sentiment.reasoning}")
        print(f"✅ Keywords: {sentiment.keywords}")
        
        # Cleanup
        if ai_service.provider:
            await ai_service.provider.cleanup()
        
        print("\n🎉 Simple test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    success = await test_simple_sentiment()
    
    if success:
        print("\n🚀 AI System basic functionality working!")
        return 0
    else:
        print("\n⚠️  AI System needs attention")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
