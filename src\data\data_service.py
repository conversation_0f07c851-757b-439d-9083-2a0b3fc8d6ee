"""
Data Service for AI Trading Bot System.

This module coordinates all data collection services including market data,
news, social media, and provides a unified interface for data access.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ..config.settings import Settings
from ..exchanges.exchange_manager import ExchangeManager
from .market_data_collector import MarketDataCollector
from .websocket_collector import WebSocketCollector
from .news_collector import NewsCollector
from .social_media_collector import SocialMediaCollector
from .cache_manager import CacheManager
from .data_quality_manager import DataQualityManager
from ..monitoring.metrics import get_metrics_collector


class DataService:
    """
    Unified data service that coordinates all data collection components.
    
    Provides a single interface for starting/stopping data collection,
    accessing cached data, and monitoring data quality.
    """
    
    def __init__(self, settings: Settings, exchange_manager: ExchangeManager):
        self.settings = settings
        self.exchange_manager = exchange_manager
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Data collection components
        self.market_data_collector = MarketDataCollector(settings, exchange_manager)
        self.websocket_collector = WebSocketCollector(settings)
        self.news_collector = NewsCollector(settings)
        self.social_media_collector = SocialMediaCollector(settings)
        self.cache_manager = CacheManager(settings)
        self.data_quality_manager = DataQualityManager(settings)
        
        # Service state
        self.is_running = False
        self.components_initialized = False
        
        # Component status
        self.component_status = {
            'market_data_collector': False,
            'websocket_collector': False,
            'news_collector': False,
            'social_media_collector': False,
            'cache_manager': False,
            'data_quality_manager': False
        }
    
    async def initialize(self):
        """Initialize all data service components."""
        try:
            self.logger.info("🚀 Initializing Data Service...")
            
            # Initialize cache manager first (other components depend on it)
            await self.cache_manager.initialize()
            self.component_status['cache_manager'] = True
            self.logger.info("✅ Cache Manager initialized")
            
            # Initialize market data collector
            await self.market_data_collector.initialize()
            self.component_status['market_data_collector'] = True
            self.logger.info("✅ Market Data Collector initialized")
            
            # Initialize WebSocket collector
            # Note: WebSocket collector doesn't need explicit initialization
            self.component_status['websocket_collector'] = True
            self.logger.info("✅ WebSocket Collector ready")
            
            # Initialize news collector
            await self.news_collector.initialize()
            self.component_status['news_collector'] = True
            self.logger.info("✅ News Collector initialized")
            
            # Initialize social media collector
            await self.social_media_collector.initialize()
            self.component_status['social_media_collector'] = True
            self.logger.info("✅ Social Media Collector initialized")
            
            # Initialize data quality manager
            await self.data_quality_manager.initialize()
            self.component_status['data_quality_manager'] = True
            self.logger.info("✅ Data Quality Manager initialized")
            
            # Register WebSocket data handlers
            self._register_websocket_handlers()
            
            self.components_initialized = True
            self.logger.info("✅ Data Service initialization completed")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Data Service: {e}")
            raise
    
    async def start(self):
        """Start all data collection services."""
        try:
            if not self.components_initialized:
                await self.initialize()
            
            if self.is_running:
                self.logger.warning("Data Service already running")
                return
            
            self.logger.info("🚀 Starting Data Service...")
            self.is_running = True
            
            # Start cache manager (already initialized)
            self.logger.info("✅ Cache Manager running")
            
            # Start market data collection
            await self.market_data_collector.start()
            self.logger.info("✅ Market Data Collector started")
            
            # Start WebSocket data collection
            await self.websocket_collector.start()
            self.logger.info("✅ WebSocket Collector started")
            
            # Start news collection
            await self.news_collector.start()
            self.logger.info("✅ News Collector started")
            
            # Start social media collection
            await self.social_media_collector.start()
            self.logger.info("✅ Social Media Collector started")
            
            # Start data quality monitoring
            await self.data_quality_manager.start()
            self.logger.info("✅ Data Quality Manager started")
            
            # Record service start
            self.metrics.record_custom_metric("data_service_starts", 1)
            
            self.logger.info("✅ Data Service started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Data Service: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """Stop all data collection services."""
        try:
            if not self.is_running:
                self.logger.warning("Data Service not running")
                return
            
            self.logger.info("🛑 Stopping Data Service...")
            self.is_running = False
            
            # Stop data quality monitoring
            await self.data_quality_manager.stop()
            self.logger.info("✅ Data Quality Manager stopped")
            
            # Stop social media collection
            await self.social_media_collector.stop()
            self.logger.info("✅ Social Media Collector stopped")
            
            # Stop news collection
            await self.news_collector.stop()
            self.logger.info("✅ News Collector stopped")
            
            # Stop WebSocket collection
            await self.websocket_collector.stop()
            self.logger.info("✅ WebSocket Collector stopped")
            
            # Stop market data collection
            await self.market_data_collector.stop()
            self.logger.info("✅ Market Data Collector stopped")
            
            # Close cache manager
            await self.cache_manager.close()
            self.logger.info("✅ Cache Manager closed")
            
            self.logger.info("✅ Data Service stopped successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping Data Service: {e}")
    
    def _register_websocket_handlers(self):
        """Register WebSocket data handlers."""
        try:
            # Register handler for ticker data
            self.websocket_collector.register_handler('ticker', self._handle_websocket_ticker)
            
            # Register handler for orderbook data
            self.websocket_collector.register_handler('orderbook', self._handle_websocket_orderbook)
            
            # Register handler for trade data
            self.websocket_collector.register_handler('trades', self._handle_websocket_trades)
            
            # Register handler for kline data
            self.websocket_collector.register_handler('kline', self._handle_websocket_kline)
            
            self.logger.info("WebSocket data handlers registered")
            
        except Exception as e:
            self.logger.error(f"Error registering WebSocket handlers: {e}")
    
    async def _handle_websocket_ticker(self, exchange: str, data_type: str, data: Dict[str, Any]):
        """Handle WebSocket ticker data."""
        try:
            # Cache ticker data for quick access
            if 's' in data:  # Binance format
                symbol = data['s']
                await self.cache_manager.cache_ticker(exchange, symbol, data)
            
            # Record metrics
            self.metrics.record_market_data_update(exchange, 'websocket', 'ticker', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling WebSocket ticker data: {e}")
    
    async def _handle_websocket_orderbook(self, exchange: str, data_type: str, data: Dict[str, Any]):
        """Handle WebSocket orderbook data."""
        try:
            # Cache orderbook data
            if 's' in data:  # Binance format
                symbol = data['s']
                await self.cache_manager.cache_orderbook(exchange, symbol, data)
            
            # Record metrics
            self.metrics.record_market_data_update(exchange, 'websocket', 'orderbook', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling WebSocket orderbook data: {e}")
    
    async def _handle_websocket_trades(self, exchange: str, data_type: str, data: Dict[str, Any]):
        """Handle WebSocket trade data."""
        try:
            # Process trade data (could be stored in database or used for analysis)
            self.logger.debug(f"Received trade data from {exchange}")
            
            # Record metrics
            self.metrics.record_market_data_update(exchange, 'websocket', 'trades', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling WebSocket trade data: {e}")
    
    async def _handle_websocket_kline(self, exchange: str, data_type: str, data: Dict[str, Any]):
        """Handle WebSocket kline/candlestick data."""
        try:
            # Process kline data
            self.logger.debug(f"Received kline data from {exchange}")
            
            # Record metrics
            self.metrics.record_market_data_update(exchange, 'websocket', 'kline', 0.001)
            
        except Exception as e:
            self.logger.error(f"Error handling WebSocket kline data: {e}")
    
    # Data access methods
    
    async def get_latest_ticker(self, exchange: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest ticker data (cached or fresh)."""
        try:
            # Try cache first
            cached_data = await self.cache_manager.get_ticker(exchange, symbol)
            if cached_data:
                return cached_data
            
            # Fallback to exchange API
            return await self.exchange_manager.get_ticker(exchange, symbol)
            
        except Exception as e:
            self.logger.error(f"Error getting ticker data: {e}")
            return None
    
    async def get_latest_orderbook(self, exchange: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest orderbook data (cached or fresh)."""
        try:
            # Try cache first
            cached_data = await self.cache_manager.get_orderbook(exchange, symbol)
            if cached_data:
                return cached_data
            
            # Fallback to exchange API
            return await self.exchange_manager.get_order_book(exchange, symbol)
            
        except Exception as e:
            self.logger.error(f"Error getting orderbook data: {e}")
            return None
    
    async def get_historical_data(self, exchange: str, symbol: str, timeframe: str, 
                                 limit: int = 100) -> List[Dict[str, Any]]:
        """Get historical market data."""
        try:
            return await self.market_data_collector.get_historical_data(
                exchange, symbol, timeframe, limit
            )
            
        except Exception as e:
            self.logger.error(f"Error getting historical data: {e}")
            return []
    
    async def get_recent_news(self, hours: int = 24, min_relevance: float = 0.3) -> List[Dict[str, Any]]:
        """Get recent news articles."""
        try:
            return await self.news_collector.get_recent_news(hours, min_relevance)
            
        except Exception as e:
            self.logger.error(f"Error getting recent news: {e}")
            return []
    
    async def get_market_sentiment(self, hours: int = 24) -> Dict[str, Any]:
        """Get aggregated market sentiment from news and social media."""
        try:
            # Get recent news
            news_articles = await self.get_recent_news(hours)
            
            # Calculate news sentiment
            news_sentiment = 0.0
            news_count = 0
            
            for article in news_articles:
                if article.get('sentiment_score') is not None:
                    news_sentiment += article['sentiment_score']
                    news_count += 1
            
            avg_news_sentiment = news_sentiment / news_count if news_count > 0 else 0.0
            
            # Get social media sentiment (simplified)
            social_sentiment = 0.0  # Would implement actual social sentiment aggregation
            
            return {
                'overall_sentiment': (avg_news_sentiment + social_sentiment) / 2,
                'news_sentiment': avg_news_sentiment,
                'social_sentiment': social_sentiment,
                'news_count': news_count,
                'social_count': 0,  # Would implement actual count
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating market sentiment: {e}")
            return {
                'overall_sentiment': 0.0,
                'news_sentiment': 0.0,
                'social_sentiment': 0.0,
                'news_count': 0,
                'social_count': 0,
                'timestamp': datetime.utcnow()
            }
    
    # Status and monitoring methods
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all components."""
        try:
            health_status = {
                'overall_healthy': True,
                'components': {},
                'timestamp': datetime.utcnow()
            }
            
            # Check cache manager
            cache_healthy = await self.cache_manager.health_check()
            health_status['components']['cache_manager'] = cache_healthy
            
            # Check exchange manager
            exchange_healthy = await self.exchange_manager.health_check()
            health_status['components']['exchange_manager'] = exchange_healthy
            
            # Check other components (simplified)
            health_status['components']['market_data_collector'] = self.market_data_collector.is_running
            health_status['components']['websocket_collector'] = self.websocket_collector.is_running
            health_status['components']['news_collector'] = self.news_collector.is_running
            health_status['components']['social_media_collector'] = self.social_media_collector.is_running
            health_status['components']['data_quality_manager'] = self.data_quality_manager.is_running
            
            # Determine overall health
            health_status['overall_healthy'] = all(health_status['components'].values())
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"Error performing health check: {e}")
            return {
                'overall_healthy': False,
                'components': {},
                'error': str(e),
                'timestamp': datetime.utcnow()
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get current service status."""
        return {
            'is_running': self.is_running,
            'components_initialized': self.components_initialized,
            'component_status': self.component_status,
            'market_data_status': self.market_data_collector.get_collection_status(),
            'websocket_status': self.websocket_collector.get_connection_status(),
            'news_status': self.news_collector.get_collection_status(),
            'social_status': self.social_media_collector.get_collection_status(),
            'cache_stats': self.cache_manager.get_cache_stats(),
            'quality_status': self.data_quality_manager.get_monitoring_status()
        }
    
    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary of collected data."""
        try:
            return {
                'market_data': self.market_data_collector.get_collection_status(),
                'news_data': self.news_collector.get_collection_status(),
                'social_data': self.social_media_collector.get_collection_status(),
                'cache_performance': self.cache_manager.get_cache_stats(),
                'data_quality': self.data_quality_manager.get_quality_summary(),
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting data summary: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.utcnow()
            }


# Global data service instance
data_service: Optional[DataService] = None


def get_data_service() -> DataService:
    """Get the global data service instance."""
    global data_service
    if data_service is None:
        from ..config.settings import Settings
        from ..exchanges.exchange_manager import get_exchange_manager
        settings = Settings()
        exchange_manager = get_exchange_manager()
        data_service = DataService(settings, exchange_manager)
    return data_service
