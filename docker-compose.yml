version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading_bot_postgres
    environment:
      POSTGRES_DB: trading_bot
      POSTGRES_USER: tradingbot
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-tradingbot123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - trading_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tradingbot -d trading_bot"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trading_bot_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - trading_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Trading Bot Application
  trading_bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai_trading_bot
    environment:
      - DATABASE_URL=postgresql://tradingbot:${POSTGRES_PASSWORD:-tradingbot123}@postgres:5432/trading_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/2
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
      - ./strategies:/app/strategies
    ports:
      - "8000:8000"
      - "8501:8501"
    networks:
      - trading_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trading_bot_celery
    command: celery -A src.core.celery_app worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql://tradingbot:${POSTGRES_PASSWORD:-tradingbot123}@postgres:5432/trading_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/2
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
    networks:
      - trading_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trading_bot_beat
    command: celery -A src.core.celery_app beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DATABASE_URL=postgresql://tradingbot:${POSTGRES_PASSWORD:-tradingbot123}@postgres:5432/trading_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/2
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    networks:
      - trading_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: trading_bot_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - trading_network
    restart: unless-stopped

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: trading_bot_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    networks:
      - trading_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: trading_bot_nginx
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - trading_network
    depends_on:
      - trading_bot
      - grafana
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  trading_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
