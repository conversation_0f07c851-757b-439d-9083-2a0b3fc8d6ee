"""
Strategy Management Module for AI Trading Bot System.

This module manages trading strategies and their execution.

Author: inkbytefo
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from abc import ABC, abstractmethod

from ..config.settings import Settings


class BaseStrategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(self, name: str, settings: Settings):
        self.name = name
        self.settings = settings
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self.is_active = True
        
    @abstractmethod
    async def analyze(self, symbol: str, market_data: Dict[str, Any],
                     technical_analysis: Dict[str, Any],
                     sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market conditions and generate signals."""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        pass


class MomentumStrategy(BaseStrategy):
    """Momentum-based trading strategy."""
    
    def __init__(self, settings: Settings):
        super().__init__("momentum", settings)
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.macd_threshold = 0.001
        
    async def analyze(self, symbol: str, market_data: Dict[str, Any],
                     technical_analysis: Dict[str, Any],
                     sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze using momentum indicators."""
        try:
            indicators = technical_analysis.get("indicators", {})
            trend = technical_analysis.get("trend", {})
            
            signal = "hold"
            confidence = 0.0
            reasons = []
            
            # RSI analysis
            rsi = indicators.get("rsi", 50)
            if rsi < self.rsi_oversold:
                signal = "buy"
                confidence += 0.3
                reasons.append(f"RSI oversold ({rsi:.1f})")
            elif rsi > self.rsi_overbought:
                signal = "sell"
                confidence += 0.3
                reasons.append(f"RSI overbought ({rsi:.1f})")
            
            # MACD analysis
            macd = indicators.get("macd", 0)
            macd_signal = indicators.get("macd_signal", 0)
            if macd > macd_signal + self.macd_threshold:
                if signal != "sell":
                    signal = "buy"
                confidence += 0.2
                reasons.append("MACD bullish crossover")
            elif macd < macd_signal - self.macd_threshold:
                if signal != "buy":
                    signal = "sell"
                confidence += 0.2
                reasons.append("MACD bearish crossover")
            
            # Trend analysis
            trend_direction = trend.get("direction", "neutral")
            if trend_direction == "bullish" and signal != "sell":
                signal = "buy"
                confidence += 0.2
                reasons.append("Bullish trend")
            elif trend_direction == "bearish" and signal != "buy":
                signal = "sell"
                confidence += 0.2
                reasons.append("Bearish trend")
            
            return {
                "strategy": self.name,
                "signal": signal,
                "confidence": min(confidence, 1.0),
                "reasons": reasons,
                "parameters_used": self.get_parameters()
            }
            
        except Exception as e:
            self.logger.error(f"Momentum analysis failed: {e}")
            return {
                "strategy": self.name,
                "signal": "hold",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get momentum strategy parameters."""
        return {
            "rsi_oversold": self.rsi_oversold,
            "rsi_overbought": self.rsi_overbought,
            "macd_threshold": self.macd_threshold
        }


class MeanReversionStrategy(BaseStrategy):
    """Mean reversion trading strategy."""
    
    def __init__(self, settings: Settings):
        super().__init__("mean_reversion", settings)
        self.bb_threshold = 0.02  # 2% outside Bollinger Bands
        self.rsi_extreme_oversold = 20
        self.rsi_extreme_overbought = 80
        
    async def analyze(self, symbol: str, market_data: Dict[str, Any],
                     technical_analysis: Dict[str, Any],
                     sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze using mean reversion indicators."""
        try:
            indicators = technical_analysis.get("indicators", {})
            current_price = market_data.get("current_price", 0)
            
            signal = "hold"
            confidence = 0.0
            reasons = []
            
            # Bollinger Bands analysis
            bb_upper = indicators.get("bb_upper", 0)
            bb_lower = indicators.get("bb_lower", 0)
            bb_middle = indicators.get("bb_middle", 0)
            
            if current_price > 0 and bb_upper > 0:
                if current_price > bb_upper * (1 + self.bb_threshold):
                    signal = "sell"
                    confidence += 0.4
                    reasons.append("Price above upper Bollinger Band")
                elif current_price < bb_lower * (1 - self.bb_threshold):
                    signal = "buy"
                    confidence += 0.4
                    reasons.append("Price below lower Bollinger Band")
            
            # Extreme RSI analysis
            rsi = indicators.get("rsi", 50)
            if rsi < self.rsi_extreme_oversold:
                signal = "buy"
                confidence += 0.3
                reasons.append(f"Extreme RSI oversold ({rsi:.1f})")
            elif rsi > self.rsi_extreme_overbought:
                signal = "sell"
                confidence += 0.3
                reasons.append(f"Extreme RSI overbought ({rsi:.1f})")
            
            # Volume confirmation
            volume_analysis = technical_analysis.get("volume_analysis", {})
            if volume_analysis.get("strength") == "high":
                confidence += 0.1
                reasons.append("High volume confirmation")
            
            return {
                "strategy": self.name,
                "signal": signal,
                "confidence": min(confidence, 1.0),
                "reasons": reasons,
                "parameters_used": self.get_parameters()
            }
            
        except Exception as e:
            self.logger.error(f"Mean reversion analysis failed: {e}")
            return {
                "strategy": self.name,
                "signal": "hold",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get mean reversion strategy parameters."""
        return {
            "bb_threshold": self.bb_threshold,
            "rsi_extreme_oversold": self.rsi_extreme_oversold,
            "rsi_extreme_overbought": self.rsi_extreme_overbought
        }


class StrategyManager:
    """
    Manages multiple trading strategies and combines their signals.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Initialize strategies
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_weights: Dict[str, float] = {}
        
        # Strategy performance tracking
        self.strategy_performance: Dict[str, Dict[str, Any]] = {}
        
    async def initialize(self):
        """Initialize all trading strategies."""
        try:
            self.logger.info("Initializing Strategy Manager...")
            
            # Initialize strategies
            self.strategies["momentum"] = MomentumStrategy(self.settings)
            self.strategies["mean_reversion"] = MeanReversionStrategy(self.settings)
            
            # Set default weights
            self.strategy_weights = {
                "momentum": 0.6,
                "mean_reversion": 0.4
            }
            
            # Initialize performance tracking
            for strategy_name in self.strategies.keys():
                self.strategy_performance[strategy_name] = {
                    "total_signals": 0,
                    "successful_signals": 0,
                    "win_rate": 0.0,
                    "avg_confidence": 0.0
                }
            
            self.logger.info("✅ Strategy Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Strategy Manager: {e}")
            raise
    
    async def analyze_all_strategies(self, symbol: str, market_data: Dict[str, Any],
                                   technical_analysis: Dict[str, Any],
                                   sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Run analysis on all active strategies."""
        try:
            strategy_results = {}
            
            # Run each strategy
            for name, strategy in self.strategies.items():
                if strategy.is_active:
                    result = await strategy.analyze(
                        symbol, market_data, technical_analysis, sentiment_analysis
                    )
                    strategy_results[name] = result
            
            # Combine strategy signals
            combined_signal = self._combine_strategy_signals(strategy_results)
            
            return {
                "symbol": symbol,
                "timestamp": datetime.utcnow().isoformat(),
                "individual_strategies": strategy_results,
                "combined_signal": combined_signal,
                "strategy_weights": self.strategy_weights
            }
            
        except Exception as e:
            self.logger.error(f"Strategy analysis failed for {symbol}: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "combined_signal": {
                    "signal": "hold",
                    "confidence": 0.0
                }
            }
    
    def _combine_strategy_signals(self, strategy_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Combine signals from multiple strategies."""
        try:
            if not strategy_results:
                return {"signal": "hold", "confidence": 0.0, "reasoning": "No strategy results"}
            
            # Convert signals to numeric scores
            total_score = 0.0
            total_weight = 0.0
            contributing_strategies = []
            
            for strategy_name, result in strategy_results.items():
                if "error" in result:
                    continue
                
                signal = result.get("signal", "hold")
                confidence = result.get("confidence", 0.0)
                weight = self.strategy_weights.get(strategy_name, 0.0)
                
                # Convert signal to score (-1 to 1)
                if signal == "buy":
                    score = confidence
                elif signal == "sell":
                    score = -confidence
                else:
                    score = 0.0
                
                weighted_score = score * weight
                total_score += weighted_score
                total_weight += weight
                
                if abs(score) > 0.1:  # Only include significant signals
                    contributing_strategies.append({
                        "strategy": strategy_name,
                        "signal": signal,
                        "confidence": confidence,
                        "weight": weight,
                        "reasons": result.get("reasons", [])
                    })
            
            # Determine final signal
            if total_weight > 0:
                normalized_score = total_score / total_weight
            else:
                normalized_score = 0.0
            
            if normalized_score > 0.3:
                final_signal = "buy"
            elif normalized_score < -0.3:
                final_signal = "sell"
            else:
                final_signal = "hold"
            
            final_confidence = min(abs(normalized_score), 1.0)
            
            # Generate reasoning
            if contributing_strategies:
                strategy_names = [s["strategy"] for s in contributing_strategies]
                reasoning = f"Combined signal from {', '.join(strategy_names)}"
            else:
                reasoning = "No strong signals from strategies"
            
            return {
                "signal": final_signal,
                "confidence": final_confidence,
                "reasoning": reasoning,
                "contributing_strategies": contributing_strategies,
                "total_score": normalized_score
            }
            
        except Exception as e:
            self.logger.error(f"Failed to combine strategy signals: {e}")
            return {
                "signal": "hold",
                "confidence": 0.0,
                "reasoning": f"Signal combination error: {str(e)}"
            }
    
    def update_strategy_weights(self, new_weights: Dict[str, float]):
        """Update strategy weights based on performance."""
        try:
            # Normalize weights to sum to 1
            total_weight = sum(new_weights.values())
            if total_weight > 0:
                self.strategy_weights = {
                    name: weight / total_weight 
                    for name, weight in new_weights.items()
                    if name in self.strategies
                }
                self.logger.info(f"Updated strategy weights: {self.strategy_weights}")
            
        except Exception as e:
            self.logger.error(f"Failed to update strategy weights: {e}")
    
    def get_strategy_performance(self) -> Dict[str, Dict[str, Any]]:
        """Get performance metrics for all strategies."""
        return self.strategy_performance.copy()
    
    def record_strategy_outcome(self, strategy_name: str, was_successful: bool, confidence: float):
        """Record the outcome of a strategy signal."""
        try:
            if strategy_name in self.strategy_performance:
                perf = self.strategy_performance[strategy_name]
                perf["total_signals"] += 1
                
                if was_successful:
                    perf["successful_signals"] += 1
                
                perf["win_rate"] = perf["successful_signals"] / perf["total_signals"]
                
                # Update average confidence
                current_avg = perf["avg_confidence"]
                total_signals = perf["total_signals"]
                perf["avg_confidence"] = ((current_avg * (total_signals - 1)) + confidence) / total_signals
                
        except Exception as e:
            self.logger.error(f"Failed to record strategy outcome: {e}")
    
    def get_active_strategies(self) -> List[str]:
        """Get list of active strategy names."""
        return [name for name, strategy in self.strategies.items() if strategy.is_active]
    
    def enable_strategy(self, strategy_name: str):
        """Enable a strategy."""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].is_active = True
            self.logger.info(f"Enabled strategy: {strategy_name}")
    
    def disable_strategy(self, strategy_name: str):
        """Disable a strategy."""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].is_active = False
            self.logger.info(f"Disabled strategy: {strategy_name}")
