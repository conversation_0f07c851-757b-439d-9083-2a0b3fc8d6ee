"""
Health checking and monitoring system for AI Trading Bot.

This module provides comprehensive health monitoring for all system components,
including database connectivity, exchange APIs, and system resources.

Author: inkbytefo
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..config.settings import Settings


@dataclass
class HealthStatus:
    """Health status data class."""
    component: str
    status: str  # "healthy", "warning", "critical", "unknown"
    message: str
    timestamp: datetime
    response_time: Optional[float] = None
    details: Optional[Dict[str, Any]] = None


class HealthChecker:
    """
    Comprehensive health checker for all system components.
    
    Monitors database connectivity, exchange APIs, system resources,
    and application-specific health metrics.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Health status storage
        self.health_status: Dict[str, HealthStatus] = {}
        self.is_running = False
        
        # Health check intervals (seconds)
        self.check_intervals = {
            "system": 30,      # System resources
            "database": 60,    # Database connectivity
            "exchanges": 120,  # Exchange APIs
            "services": 180,   # Application services
        }
        
        # Health thresholds
        self.thresholds = {
            "cpu_usage": 80.0,      # CPU usage percentage
            "memory_usage": 85.0,   # Memory usage percentage
            "disk_usage": 90.0,     # Disk usage percentage
            "response_time": 5.0,   # Maximum response time in seconds
        }
    
    async def start(self):
        """Start the health checking system."""
        try:
            self.logger.info("Starting Health Checker...")
            self.is_running = True
            
            # Start health check tasks
            asyncio.create_task(self._system_health_loop())
            asyncio.create_task(self._database_health_loop())
            asyncio.create_task(self._exchange_health_loop())
            asyncio.create_task(self._service_health_loop())
            
            self.logger.info("✅ Health Checker started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Health Checker: {e}")
            raise
    
    async def stop(self):
        """Stop the health checking system."""
        try:
            self.logger.info("Stopping Health Checker...")
            self.is_running = False
            self.logger.info("✅ Health Checker stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping Health Checker: {e}")
    
    async def _system_health_loop(self):
        """Monitor system resources continuously."""
        while self.is_running:
            try:
                await self._check_system_health()
                await asyncio.sleep(self.check_intervals["system"])
            except Exception as e:
                self.logger.error(f"System health check error: {e}")
                await asyncio.sleep(10)
    
    async def _database_health_loop(self):
        """Monitor database connectivity continuously."""
        while self.is_running:
            try:
                await self._check_database_health()
                await asyncio.sleep(self.check_intervals["database"])
            except Exception as e:
                self.logger.error(f"Database health check error: {e}")
                await asyncio.sleep(30)
    
    async def _exchange_health_loop(self):
        """Monitor exchange API connectivity continuously."""
        while self.is_running:
            try:
                await self._check_exchange_health()
                await asyncio.sleep(self.check_intervals["exchanges"])
            except Exception as e:
                self.logger.error(f"Exchange health check error: {e}")
                await asyncio.sleep(60)
    
    async def _service_health_loop(self):
        """Monitor application services continuously."""
        while self.is_running:
            try:
                await self._check_service_health()
                await asyncio.sleep(self.check_intervals["services"])
            except Exception as e:
                self.logger.error(f"Service health check error: {e}")
                await asyncio.sleep(90)
    
    async def _check_system_health(self):
        """Check system resource health."""
        try:
            start_time = time.time()
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Network I/O
            network = psutil.net_io_counters()
            
            response_time = time.time() - start_time
            
            # Determine status
            status = "healthy"
            messages = []
            
            if cpu_percent > self.thresholds["cpu_usage"]:
                status = "warning" if cpu_percent < 95 else "critical"
                messages.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory_percent > self.thresholds["memory_usage"]:
                status = "warning" if memory_percent < 95 else "critical"
                messages.append(f"High memory usage: {memory_percent:.1f}%")
            
            if disk_percent > self.thresholds["disk_usage"]:
                status = "warning" if disk_percent < 98 else "critical"
                messages.append(f"High disk usage: {disk_percent:.1f}%")
            
            message = "; ".join(messages) if messages else "System resources normal"
            
            self.health_status["system"] = HealthStatus(
                component="system",
                status=status,
                message=message,
                timestamp=datetime.utcnow(),
                response_time=response_time,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "disk_percent": disk_percent,
                    "network_bytes_sent": network.bytes_sent,
                    "network_bytes_recv": network.bytes_recv,
                }
            )
            
        except Exception as e:
            self.health_status["system"] = HealthStatus(
                component="system",
                status="critical",
                message=f"System health check failed: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_database_health(self):
        """Check database connectivity and performance."""
        try:
            start_time = time.time()
            
            # This would be implemented with actual database connection
            # For now, we'll simulate the check
            await asyncio.sleep(0.1)  # Simulate database query
            
            response_time = time.time() - start_time
            
            status = "healthy"
            if response_time > self.thresholds["response_time"]:
                status = "warning"
            
            self.health_status["database"] = HealthStatus(
                component="database",
                status=status,
                message="Database connection healthy",
                timestamp=datetime.utcnow(),
                response_time=response_time,
                details={
                    "connection_pool_size": 10,  # Would be actual value
                    "active_connections": 3,     # Would be actual value
                }
            )
            
        except Exception as e:
            self.health_status["database"] = HealthStatus(
                component="database",
                status="critical",
                message=f"Database health check failed: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_exchange_health(self):
        """Check exchange API connectivity."""
        try:
            start_time = time.time()
            
            # This would check actual exchange APIs
            # For now, we'll simulate the check
            await asyncio.sleep(0.2)  # Simulate API call
            
            response_time = time.time() - start_time
            
            status = "healthy"
            if response_time > self.thresholds["response_time"]:
                status = "warning"
            
            self.health_status["exchanges"] = HealthStatus(
                component="exchanges",
                status=status,
                message="Exchange APIs healthy",
                timestamp=datetime.utcnow(),
                response_time=response_time,
                details={
                    "binance_status": "connected",
                    "coinbase_status": "connected",
                    "kraken_status": "connected",
                }
            )
            
        except Exception as e:
            self.health_status["exchanges"] = HealthStatus(
                component="exchanges",
                status="critical",
                message=f"Exchange health check failed: {e}",
                timestamp=datetime.utcnow()
            )
    
    async def _check_service_health(self):
        """Check application service health."""
        try:
            start_time = time.time()
            
            # Check various application services
            services_status = {
                "trading_engine": "healthy",
                "data_collector": "healthy",
                "ai_agent": "healthy",
                "risk_manager": "healthy",
            }
            
            response_time = time.time() - start_time
            
            # Determine overall status
            status = "healthy"
            failed_services = [k for k, v in services_status.items() if v != "healthy"]
            
            if failed_services:
                status = "critical"
                message = f"Failed services: {', '.join(failed_services)}"
            else:
                message = "All services healthy"
            
            self.health_status["services"] = HealthStatus(
                component="services",
                status=status,
                message=message,
                timestamp=datetime.utcnow(),
                response_time=response_time,
                details=services_status
            )
            
        except Exception as e:
            self.health_status["services"] = HealthStatus(
                component="services",
                status="critical",
                message=f"Service health check failed: {e}",
                timestamp=datetime.utcnow()
            )
    
    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        if not self.health_status:
            return {
                "status": "unknown",
                "message": "No health data available",
                "timestamp": datetime.utcnow(),
                "components": {}
            }
        
        # Determine overall status
        statuses = [health.status for health in self.health_status.values()]
        
        if "critical" in statuses:
            overall_status = "critical"
        elif "warning" in statuses:
            overall_status = "warning"
        elif "unknown" in statuses:
            overall_status = "unknown"
        else:
            overall_status = "healthy"
        
        # Count status types
        status_counts = {}
        for status in statuses:
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "status": overall_status,
            "message": f"System health: {overall_status}",
            "timestamp": datetime.utcnow(),
            "status_counts": status_counts,
            "components": {
                name: {
                    "status": health.status,
                    "message": health.message,
                    "timestamp": health.timestamp,
                    "response_time": health.response_time
                }
                for name, health in self.health_status.items()
            }
        }
    
    def get_component_health(self, component: str) -> Optional[HealthStatus]:
        """Get health status for a specific component."""
        return self.health_status.get(component)
    
    def is_healthy(self) -> bool:
        """Check if the overall system is healthy."""
        overall_health = self.get_overall_health()
        return overall_health["status"] in ["healthy", "warning"]
