"""
Exchange Manager for AI Trading Bot System.

This module provides a unified interface for interacting with multiple
cryptocurrency exchanges using the ccxt library.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta, timezone
from decimal import Decimal

import ccxt.async_support as ccxt  # type: ignore
from ..config.settings import Settings
from ..config.security import SecurityManager
from ..monitoring.metrics import get_metrics_collector


class ExchangeManager:
    """
    Unified exchange manager for multiple cryptocurrency exchanges.
    
    Provides a consistent interface for trading operations across
    different exchanges using the ccxt library.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.security_manager = SecurityManager(settings)
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Exchange instances
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.exchange_configs = {}

        # Rate limiting and connection management
        self.rate_limits = {}
        self.last_requests = {}

        # Supported exchanges (Updated for 2025)
        self.supported_exchanges = {
            'binance_spot': ccxt.binance,      # Binance Spot Trading
            'binance_futures': ccxt.binance,   # Binance Perpetual Futures
            'mexc': ccxt.mexc                  # MEXC Global Exchange
        }
    
    async def initialize(self):
        """Initialize exchange connections."""
        try:
            self.logger.info("Initializing Exchange Manager...")

            # Initialize configured exchanges with timeout
            self.logger.info("🔄 Starting exchange initialization process...")
            await asyncio.wait_for(self._initialize_exchanges(), timeout=60.0)

            # Test connections with timeout
            self.logger.info("🔄 Testing exchange connections...")
            await asyncio.wait_for(self._test_connections(), timeout=30.0)

            self.logger.info("✅ Exchange Manager initialized successfully")

        except asyncio.TimeoutError:
            self.logger.error("❌ Exchange Manager initialization timed out")
            # Don't raise, continue with whatever exchanges were initialized
            if self.exchanges:
                self.logger.warning(f"⚠️ Continuing with {len(self.exchanges)} partially initialized exchanges")
            else:
                self.logger.error("❌ No exchanges were successfully initialized")
                raise RuntimeError("Exchange initialization failed - no exchanges available")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Exchange Manager: {e}")
            raise
    
    async def _initialize_exchanges(self):
        """Initialize individual exchange connections."""
        exchange_settings = self.settings.exchanges
        initialized_exchanges = []
        failed_exchanges = []

        # Binance Spot Trading
        if exchange_settings.binance_api_key and exchange_settings.binance_secret_key:
            self.logger.info("🔄 Attempting to initialize Binance Spot...")
            try:
                await asyncio.wait_for(self._setup_binance_spot(), timeout=30.0)
                initialized_exchanges.append("Binance Spot")
            except asyncio.TimeoutError:
                self.logger.error("❌ Binance Spot initialization timed out (30s)")
                failed_exchanges.append("Binance Spot (timeout)")
            except Exception as e:
                self.logger.error(f"❌ Binance Spot initialization failed: {e}")
                failed_exchanges.append(f"Binance Spot ({str(e)[:50]})")

        # Binance Perpetual Futures
        if exchange_settings.binance_api_key and exchange_settings.binance_secret_key:
            self.logger.info("🔄 Attempting to initialize Binance Futures...")
            try:
                await asyncio.wait_for(self._setup_binance_futures(), timeout=30.0)
                initialized_exchanges.append("Binance Futures")
            except asyncio.TimeoutError:
                self.logger.error("❌ Binance Futures initialization timed out (30s)")
                failed_exchanges.append("Binance Futures (timeout)")
            except Exception as e:
                self.logger.error(f"❌ Binance Futures initialization failed: {e}")
                failed_exchanges.append(f"Binance Futures ({str(e)[:50]})")

        # MEXC Global Exchange
        if hasattr(exchange_settings, 'mexc_api_key') and exchange_settings.mexc_api_key and exchange_settings.mexc_secret_key:
            self.logger.info("🔄 Attempting to initialize MEXC...")
            try:
                await asyncio.wait_for(self._setup_mexc(), timeout=30.0)
                initialized_exchanges.append("MEXC")
            except asyncio.TimeoutError:
                self.logger.error("❌ MEXC initialization timed out (30s)")
                failed_exchanges.append("MEXC (timeout)")
            except Exception as e:
                self.logger.error(f"❌ MEXC initialization failed: {e}")
                failed_exchanges.append(f"MEXC ({str(e)[:50]})")

        # Report results
        if not self.exchanges:
            self.logger.warning("⚠️ No exchange credentials configured or all initializations failed")
            if failed_exchanges:
                self.logger.error(f"❌ Failed exchanges: {', '.join(failed_exchanges)}")
        else:
            self.logger.info(f"✅ Successfully initialized exchanges: {', '.join(initialized_exchanges)}")
            if failed_exchanges:
                self.logger.warning(f"⚠️ Failed exchanges: {', '.join(failed_exchanges)}")
            self.logger.info(f"📊 Total active exchanges: {len(self.exchanges)}")
    
    async def _setup_binance_spot(self):
        """Setup Binance Spot trading connection (2025 optimized)."""
        try:
            self.logger.info("🔧 Creating Binance Spot exchange instance...")
            config = {
                'apiKey': self.settings.exchanges.binance_api_key,
                'secret': self.settings.exchanges.binance_secret_key,
                'sandbox': self.settings.exchanges.binance_testnet,
                'enableRateLimit': True,
                'rateLimit': 1200,  # 1200 requests per minute (2025 limit)
                'timeout': 15000,   # 15 seconds timeout
                'options': {
                    'defaultType': 'spot',  # Explicitly set to spot trading
                    'adjustForTimeDifference': True,
                    'recvWindow': 5000,     # 5 second receive window
                    'timeDifference': 0,
                    'synchronizeTime': True,
                }
            }

            exchange = ccxt.binance(config)
            self.logger.info("✅ Binance Spot exchange instance created")

            # Load markets and sync time
            try:
                self.logger.info("🔄 Loading Binance Spot markets...")
                await asyncio.wait_for(exchange.load_markets(), timeout=15.0)
                self.logger.info("✅ Binance Spot markets loaded successfully")

                self.logger.info("🔄 Syncing Binance Spot server time...")
                server_time = await asyncio.wait_for(exchange.fetch_time(), timeout=10.0)
                local_time = exchange.milliseconds()
                time_diff = server_time - local_time

                exchange.options['timeDifference'] = time_diff
                self.logger.info(f"✅ Binance Spot time difference: {time_diff}ms")

                if abs(time_diff) > 1000:
                    self.logger.warning(f"⚠️ Large time difference detected: {time_diff}ms")

            except asyncio.TimeoutError:
                self.logger.warning("⚠️ Binance Spot time sync timed out, using defaults")
                exchange.options['timeDifference'] = 0
            except Exception as time_error:
                self.logger.warning(f"⚠️ Binance Spot time sync failed: {time_error}")
                exchange.options['timeDifference'] = 0

            self.exchanges['binance_spot'] = exchange
            self.exchange_configs['binance_spot'] = config

            self.logger.info("✅ [OK] Binance Spot exchange configured successfully")

        except Exception as e:
            self.logger.error(f"❌ [ERROR] Failed to setup Binance Spot: {e}")
            raise

    async def _setup_binance_futures(self):
        """Setup Binance Perpetual Futures trading connection (2025 optimized)."""
        try:
            self.logger.info("🔧 Creating Binance Futures exchange instance...")
            config = {
                'apiKey': self.settings.exchanges.binance_api_key,
                'secret': self.settings.exchanges.binance_secret_key,
                'sandbox': self.settings.exchanges.binance_testnet,
                'enableRateLimit': True,
                'rateLimit': 2400,  # 2400 requests per minute for futures (2025 limit)
                'timeout': 15000,   # 15 seconds timeout
                'options': {
                    'defaultType': 'future',  # Set to perpetual futures
                    'adjustForTimeDifference': True,
                    'recvWindow': 5000,       # 5 second receive window
                    'timeDifference': 0,
                    'synchronizeTime': True,
                }
            }

            exchange = ccxt.binance(config)
            self.logger.info("✅ Binance Futures exchange instance created")

            # Load markets and sync time
            try:
                self.logger.info("🔄 Loading Binance Futures markets...")
                await asyncio.wait_for(exchange.load_markets(), timeout=15.0)
                self.logger.info("✅ Binance Futures markets loaded successfully")

                self.logger.info("🔄 Syncing Binance Futures server time...")
                server_time = await asyncio.wait_for(exchange.fetch_time(), timeout=10.0)
                local_time = exchange.milliseconds()
                time_diff = server_time - local_time

                exchange.options['timeDifference'] = time_diff
                self.logger.info(f"✅ Binance Futures time difference: {time_diff}ms")

                if abs(time_diff) > 1000:
                    self.logger.warning(f"⚠️ Large time difference detected: {time_diff}ms")

            except asyncio.TimeoutError:
                self.logger.warning("⚠️ Binance Futures time sync timed out, using defaults")
                exchange.options['timeDifference'] = 0
            except Exception as time_error:
                self.logger.warning(f"⚠️ Binance Futures time sync failed: {time_error}")
                exchange.options['timeDifference'] = 0

            self.exchanges['binance_futures'] = exchange
            self.exchange_configs['binance_futures'] = config

            self.logger.info("✅ [OK] Binance Futures exchange configured successfully")

        except Exception as e:
            self.logger.error(f"❌ [ERROR] Failed to setup Binance Futures: {e}")
            raise
    


    async def _setup_mexc(self):
        """Setup MEXC Global exchange connection (2025 optimized)."""
        try:
            self.logger.info("🔧 Creating MEXC Global exchange instance...")
            config = {
                'apiKey': self.settings.exchanges.mexc_api_key,
                'secret': self.settings.exchanges.mexc_secret_key,
                'sandbox': getattr(self.settings.exchanges, 'mexc_testnet', False),
                'enableRateLimit': True,
                'rateLimit': 1000,  # 1000 requests per minute (2025 limit)
                'timeout': 15000,   # 15 seconds timeout
                'options': {
                    'defaultType': 'spot',  # MEXC spot trading
                    'adjustForTimeDifference': True,
                    'recvWindow': 5000,     # 5 second receive window
                    'timeDifference': 0,
                }
            }

            exchange = ccxt.mexc(config)
            self.logger.info("✅ MEXC Global exchange instance created")

            # Load markets and sync time
            try:
                self.logger.info("🔄 Loading MEXC Global markets...")
                await asyncio.wait_for(exchange.load_markets(), timeout=15.0)
                self.logger.info("✅ MEXC Global markets loaded successfully")

                self.logger.info("🔄 Syncing MEXC Global server time...")
                server_time = await asyncio.wait_for(exchange.fetch_time(), timeout=10.0)
                local_time = exchange.milliseconds()
                time_diff = server_time - local_time

                exchange.options['timeDifference'] = time_diff
                self.logger.info(f"✅ MEXC Global time difference: {time_diff}ms")

                if abs(time_diff) > 1000:
                    self.logger.warning(f"⚠️ Large time difference detected: {time_diff}ms")

            except asyncio.TimeoutError:
                self.logger.warning("⚠️ MEXC Global time sync timed out, using defaults")
                exchange.options['timeDifference'] = 0
            except Exception as time_error:
                self.logger.warning(f"⚠️ MEXC Global time sync failed: {time_error}")
                exchange.options['timeDifference'] = 0

            self.exchanges['mexc'] = exchange
            self.exchange_configs['mexc'] = config

            self.logger.info("✅ [OK] MEXC Global exchange configured successfully")

        except Exception as e:
            self.logger.error(f"❌ [ERROR] Failed to setup MEXC Global: {e}")
            raise


    
    async def _test_connections(self):
        """Test exchange connections."""
        successful_tests = []
        failed_tests = []

        for exchange_name, exchange in self.exchanges.items():
            try:
                self.logger.info(f"🔄 Testing {exchange_name} connection...")
                # Test connection by fetching exchange status with timeout
                status = await asyncio.wait_for(exchange.fetch_status(), timeout=10.0)

                if status.get('status') == 'ok':
                    self.logger.info(f"✅ {exchange_name} connection successful")
                    successful_tests.append(exchange_name)
                else:
                    self.logger.warning(f"⚠️ {exchange_name} status: {status}")
                    failed_tests.append(f"{exchange_name} (status: {status.get('status', 'unknown')})")

            except asyncio.TimeoutError:
                self.logger.error(f"❌ {exchange_name} connection test timed out")
                failed_tests.append(f"{exchange_name} (timeout)")
            except Exception as e:
                self.logger.error(f"❌ {exchange_name} connection failed: {e}")
                failed_tests.append(f"{exchange_name} ({str(e)[:50]})")

        # Report test results
        if successful_tests:
            self.logger.info(f"✅ Connection tests passed: {', '.join(successful_tests)}")
        if failed_tests:
            self.logger.warning(f"⚠️ Connection tests failed: {', '.join(failed_tests)}")

        self.logger.info(f"📊 Connection test summary: {len(successful_tests)} passed, {len(failed_tests)} failed")
    
    async def get_markets(self, exchange_name: str) -> Dict[str, Any]:
        """Get available markets for an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            markets = await exchange.load_markets()
            
            self.logger.debug(f"Loaded {len(markets)} markets from {exchange_name}")
            return markets
            
        except Exception as e:
            self.logger.error(f"Failed to get markets from {exchange_name}: {e}")
            return {}
    
    async def get_ticker(self, exchange_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get ticker data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")

            # Kraken için sembol dönüştürme
            if exchange_name == 'kraken':
                symbol = self._convert_symbol_for_kraken(symbol)

            exchange = self.exchanges[exchange_name]
            ticker = await exchange.fetch_ticker(symbol)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, "ticker", 0.1
            )
            
            return ticker
            
        except Exception as e:
            self.logger.error(f"Failed to get ticker {symbol} from {exchange_name}: {e}")
            return None
    
    async def get_ohlcv(self, exchange_name: str, symbol: str,
                       timeframe: str = '1m', limit: int = 100) -> List[List]:
        """Get OHLCV data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")

            # Kraken için sembol dönüştürme
            if exchange_name == 'kraken':
                symbol = self._convert_symbol_for_kraken(symbol)

            exchange = self.exchanges[exchange_name]
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, timeframe, 0.1
            )
            
            return ohlcv
            
        except Exception as e:
            self.logger.error(f"Failed to get OHLCV {symbol} from {exchange_name}: {e}")
            return []
    
    async def get_order_book(self, exchange_name: str, symbol: str, 
                           limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get order book data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            order_book = await exchange.fetch_order_book(symbol, limit)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, "orderbook", 0.1
            )
            
            return order_book
            
        except Exception as e:
            self.logger.error(f"Failed to get order book {symbol} from {exchange_name}: {e}")
            return None
    
    async def get_balance(self, exchange_name: str) -> Optional[Dict[str, Any]]:
        """Get account balance for an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            balance = await exchange.fetch_balance()
            
            return balance
            
        except Exception as e:
            self.logger.error(f"Failed to get balance from {exchange_name}: {e}")
            return None
    
    async def place_order(self, exchange_name: str, symbol: str, order_type: str,
                         side: str, amount: float, price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """Place an order on an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            if self.settings.PAPER_TRADING:
                self.logger.info(f"[PAPER] Paper trading: {side} {amount} {symbol} at {price}")
                return {
                    'id': f'paper_{datetime.now(timezone.utc).timestamp()}',
                    'symbol': symbol,
                    'type': order_type,
                    'side': side,
                    'amount': amount,
                    'price': price,
                    'status': 'closed',
                    'filled': amount,
                    'timestamp': datetime.now(timezone.utc).timestamp() * 1000
                }
            
            exchange = self.exchanges[exchange_name]
            
            # Place the order
            if order_type == 'market':
                order = await exchange.create_market_order(symbol, side, amount)
            else:
                order = await exchange.create_limit_order(symbol, side, amount, price)
            
            # Record metrics
            self.metrics.record_order(exchange_name, symbol, side, 'placed')
            
            self.logger.info(f"✅ Order placed: {order['id']}")
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to place order on {exchange_name}: {e}")
            self.metrics.record_order(exchange_name, symbol, side, 'failed')
            return None
    
    async def get_order_status(self, exchange_name: str, order_id: str, 
                              symbol: str) -> Optional[Dict[str, Any]]:
        """Get order status."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            order = await exchange.fetch_order(order_id, symbol)
            
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to get order status from {exchange_name}: {e}")
            return None
    
    async def cancel_order(self, exchange_name: str, order_id: str, 
                          symbol: str) -> bool:
        """Cancel an order."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            await exchange.cancel_order(order_id, symbol)
            
            self.logger.info(f"✅ Order cancelled: {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order on {exchange_name}: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check health of all exchange connections."""
        try:
            healthy_exchanges = 0
            
            for exchange_name, exchange in self.exchanges.items():
                try:
                    status = await exchange.fetch_status()
                    if status.get('status') == 'ok':
                        healthy_exchanges += 1
                except Exception as e:
                    self.logger.warning(f"Health check failed for {exchange_name}: {e}")
            
            return healthy_exchanges > 0
            
        except Exception as e:
            self.logger.error(f"Exchange health check error: {e}")
            return False
    
    def get_supported_exchanges(self) -> List[str]:
        """Get list of supported exchanges."""
        return list(self.supported_exchanges.keys())
    
    def get_active_exchanges(self) -> List[str]:
        """Get list of active exchanges."""
        return list(self.exchanges.keys())
    
    async def close(self):
        """Close all exchange connections."""
        try:
            for exchange_name, exchange in self.exchanges.items():
                await exchange.close()
                self.logger.info(f"Closed connection to {exchange_name}")
                
        except Exception as e:
            self.logger.error(f"Error closing exchange connections: {e}")


# Global exchange manager instance
exchange_manager: Optional[ExchangeManager] = None


def get_exchange_manager() -> ExchangeManager:
    """Get the global exchange manager instance."""
    global exchange_manager
    if exchange_manager is None:
        from ..config.settings import Settings
        settings = Settings()
        exchange_manager = ExchangeManager(settings)
    return exchange_manager
