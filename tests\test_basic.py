"""
Basic tests for AI Trading Bot System.

These tests verify that the basic components are working correctly.

Author: inkbytefo
"""

import pytest
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_imports():
    """Test that all main modules can be imported."""
    try:
        from src.config.settings import Settings
        from src.config.database import DatabaseManager
        from src.config.security import SecurityManager
        from src.monitoring.logger import setup_logging
        from src.monitoring.health_checker import HealthChecker
        from src.monitoring.metrics import MetricsCollector
        from src.monitoring.alerts import AlertManager
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


def test_settings_creation():
    """Test that settings can be created with defaults."""
    try:
        # This should work with default values even without .env file
        from src.config.settings import Settings
        
        # Create settings with minimal required values
        import os
        os.environ["SECRET_KEY"] = "test_secret_key_for_testing"
        os.environ["ENCRYPTION_KEY"] = "test_encryption_key_for_testing"
        os.environ["DATABASE_URL"] = "postgresql://test:test@localhost:5432/test"
        
        settings = Settings()
        
        assert settings.VERSION == "1.0.0"
        assert settings.SECRET_KEY == "test_secret_key_for_testing"
        assert settings.PAPER_TRADING is True  # Should default to True
        
    except Exception as e:
        pytest.fail(f"Settings creation failed: {e}")


def test_security_manager():
    """Test basic security manager functionality."""
    try:
        from src.config.settings import Settings
        from src.config.security import SecurityManager
        
        import os
        os.environ["SECRET_KEY"] = "test_secret_key_for_testing"
        os.environ["ENCRYPTION_KEY"] = "test_encryption_key_for_testing"
        os.environ["DATABASE_URL"] = "postgresql://test:test@localhost:5432/test"
        
        settings = Settings()
        security_manager = SecurityManager(settings)
        
        # Test encryption/decryption
        test_data = "sensitive_api_key_12345"
        encrypted = security_manager.encrypt_data(test_data)
        decrypted = security_manager.decrypt_data(encrypted)
        
        assert decrypted == test_data
        assert encrypted != test_data
        
        # Test password hashing
        password = "test_password"
        hashed, salt = security_manager.hash_password(password)
        
        assert security_manager.verify_password(password, hashed, salt)
        assert not security_manager.verify_password("wrong_password", hashed, salt)
        
    except Exception as e:
        pytest.fail(f"Security manager test failed: {e}")


def test_metrics_collector():
    """Test basic metrics collector functionality."""
    try:
        from src.config.settings import Settings
        from src.monitoring.metrics import MetricsCollector
        
        import os
        os.environ["SECRET_KEY"] = "test_secret_key_for_testing"
        os.environ["ENCRYPTION_KEY"] = "test_encryption_key_for_testing"
        os.environ["DATABASE_URL"] = "postgresql://test:test@localhost:5432/test"
        
        settings = Settings()
        metrics_collector = MetricsCollector(settings)
        
        # Test recording metrics
        metrics_collector.record_order("binance", "BTC/USDT", "BUY", "FILLED", 0.5)
        metrics_collector.record_ai_signal("momentum", "BUY", "BTC/USDT", 0.8)
        
        # Test exporting metrics
        metrics_output = metrics_collector.export_metrics()
        assert isinstance(metrics_output, (str, bytes))
        
        # Test metrics summary
        summary = metrics_collector.get_metrics_summary()
        assert "timestamp" in summary
        assert "system" in summary
        assert "trading" in summary
        
    except Exception as e:
        pytest.fail(f"Metrics collector test failed: {e}")


def test_alert_manager():
    """Test basic alert manager functionality."""
    try:
        from src.config.settings import Settings
        from src.monitoring.alerts import AlertManager, AlertLevel, AlertType
        
        import os
        os.environ["SECRET_KEY"] = "test_secret_key_for_testing"
        os.environ["ENCRYPTION_KEY"] = "test_encryption_key_for_testing"
        os.environ["DATABASE_URL"] = "postgresql://test:test@localhost:5432/test"
        
        settings = Settings()
        alert_manager = AlertManager(settings)
        
        # Test alert creation (without actually sending)
        alert_id = "test_alert_123"
        
        # Test getting alert stats
        stats = alert_manager.get_alert_stats()
        assert "active_alerts" in stats
        assert "by_level" in stats
        assert "by_type" in stats
        
    except Exception as e:
        pytest.fail(f"Alert manager test failed: {e}")


def test_project_structure():
    """Test that the project structure is correct."""
    project_root = Path(__file__).parent.parent
    
    # Check main directories exist
    required_dirs = [
        "src",
        "src/core",
        "src/config",
        "src/monitoring",
        "tests",
        "scripts",
        "docker",
        "logs",
        "models"
    ]
    
    for directory in required_dirs:
        dir_path = project_root / directory
        assert dir_path.exists(), f"Required directory missing: {directory}"
        assert dir_path.is_dir(), f"Path is not a directory: {directory}"
    
    # Check main files exist
    required_files = [
        "main.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml",
        ".env.example",
        "README.md"
    ]
    
    for file_name in required_files:
        file_path = project_root / file_name
        assert file_path.exists(), f"Required file missing: {file_name}"
        assert file_path.is_file(), f"Path is not a file: {file_name}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
