# AI Trading Bot System

A comprehensive AI-powered cryptocurrency trading bot system with advanced analytics, risk management, and portfolio optimization capabilities.

## 🚀 Features

### Core Trading Capabilities
- **Multi-Exchange Support**: Binance, Coinbase Pro, Kraken integration
- **24/7 Automated Trading**: Continuous market monitoring and execution
- **AI-Powered Decision Making**: Modern OpenAI-based system for intelligent market analysis
- **Real-time Data Processing**: Live market data, news, and social media sentiment
- **Interactive Chat Interface**: Communicate with the bot while it continues trading

### Advanced Analytics
- **Technical Analysis**: RSI, MACD, Bollinger Bands, and 50+ indicators
- **Sentiment Analysis**: News and social media sentiment tracking
- **AI Models**: OpenAI GPT-4o and compatible models for advanced market analysis
- **Multi-timeframe Analysis**: Comprehensive market view across different time horizons

### Risk Management
- **Dynamic Position Sizing**: Intelligent position management based on risk tolerance
- **Stop-Loss Management**: Automated loss prevention mechanisms
- **Portfolio Optimization**: Advanced portfolio balancing and diversification
- **Drawdown Control**: Automatic risk reduction during adverse market conditions

### Security & Monitoring
- **Secure API Management**: Encrypted storage of exchange credentials
- **Real-time Monitoring**: Comprehensive system health and performance tracking
- **Emergency Controls**: Instant stop mechanisms and position closure
- **Detailed Logging**: Complete audit trail of all trading activities

## 🏗️ System Architecture

```
ai-trading-bot/
├── src/
│   ├── core/                    # Main trading engine
│   ├── data/                    # Data collection services
│   ├── analysis/                # Analysis engines
│   ├── risk/                    # Risk management
│   ├── portfolio/               # Portfolio management
│   ├── exchanges/               # Exchange connectors
│   ├── chat/                    # Chat interface
│   ├── monitoring/              # Monitoring and logging
│   ├── config/                  # Configuration management
│   └── backtesting/             # Backtesting engine
├── models/                      # ML models and strategies
├── strategies/                  # Trading strategies
├── tests/                       # Test suites
├── docs/                        # Documentation
├── scripts/                     # Utility scripts
├── docker/                      # Docker configurations
└── web/                         # Web interface
```

## 🛠️ Technology Stack

- **Backend**: Python 3.11+, FastAPI, SQLAlchemy
- **Database**: PostgreSQL, Redis
- **ML/AI**: TensorFlow, PyTorch, scikit-learn
- **Exchange APIs**: ccxt library
- **Real-time**: WebSocket connections
- **Containerization**: Docker, Docker Compose
- **Monitoring**: Prometheus, Grafana
- **Web Interface**: Streamlit

## 📋 Development Phases

### Phase 1: Infrastructure Setup ⏳
- [x] System architecture design
- [/] Project structure creation
- [ ] Configuration system
- [ ] Database schema
- [ ] Logging infrastructure
- [ ] Docker containerization

### Phase 2: Data Collection Services
- [ ] Exchange API integrations
- [ ] Real-time market data collection
- [ ] News and social media data gathering
- [ ] Data storage and caching

### Phase 3: Analysis Engines
- [ ] Technical analysis indicators
- [ ] Sentiment analysis system
- [ ] ML model infrastructure
- [ ] Price prediction models

### Phase 4: Trading Engine
- [ ] AI agent decision system
- [ ] Risk management implementation
- [ ] Portfolio management
- [ ] Order execution system

### Phase 5: User Interface
- [ ] Interactive chat interface
- [ ] Web dashboard
- [ ] Monitoring panels
- [ ] Configuration interface

### Phase 6: Testing & Optimization
- [ ] Backtesting system
- [ ] Unit and integration tests
- [ ] Performance optimization
- [ ] Security auditing

## 🚦 Getting Started

### Prerequisites
- Python 3.11+
- Docker and Docker Compose
- PostgreSQL
- Redis

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ai-trading-bot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Configure AI Provider
# Add your OpenAI API key to .env:
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_BASE_URL=https://api.openai.com/v1  # or compatible provider

# Test the AI system
python test_ai_system.py
```

## 🤖 AI Configuration

### Supported Providers

The system uses a modern OpenAI-based architecture that supports multiple providers:

- **OpenAI Official**: `https://api.openai.com/v1`
- **OpenRouter**: `https://openrouter.ai/api/v1` (Access to multiple models)
- **Azure OpenAI**: `https://your-resource.openai.azure.com/`
- **Local Models**:
  - Ollama: `http://localhost:11434/v1`
  - LM Studio: `http://localhost:1234/v1`
- **Other Compatible Providers**: Any OpenAI-compatible API

### Configuration

Set your provider in `.env`:

```bash
# Primary OpenAI Configuration
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# Model Selection for Different Tasks
AI_SENTIMENT_MODEL=gpt-4o-mini
AI_PREDICTION_MODEL=gpt-4o
AI_ANALYSIS_MODEL=gpt-4o
AI_GENERAL_MODEL=gpt-4o-mini

# Start services with Docker
docker-compose up -d

# Run database migrations
python scripts/migrate.py

# Start the trading bot
python main.py
```

## 📖 Documentation

- [System Architecture](docs/architecture.md)
- [Configuration Guide](docs/configuration.md)
- [API Documentation](docs/api.md)
- [Trading Strategies](docs/strategies.md)
- [Risk Management](docs/risk-management.md)
- [Deployment Guide](docs/deployment.md)

## ⚠️ Disclaimer

This trading bot is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Always test strategies thoroughly with paper trading before using real funds. The authors are not responsible for any financial losses.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**inkbytefo** - AI Trading Bot System Developer

---

*Built with ❤️ for the crypto trading community*
