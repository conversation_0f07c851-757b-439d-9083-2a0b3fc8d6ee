"""
Logging configuration and setup for AI Trading Bot System.

This module provides structured logging with JSON format, file rotation,
and different log levels for various components.

Author: inkbytefo
"""

import logging
import logging.handlers
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# import structlog
# from structlog.stdlib import LoggerFactory

from ..config.settings import Settings


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        if hasattr(record, "extra_fields"):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class TradingBotLogger:
    """Custom logger class for the trading bot."""
    
    def __init__(self, name: str, settings: Settings):
        self.name = name
        self.settings = settings
        self.logger = logging.getLogger(name)
        
    def info(self, message: str, **kwargs):
        """Log info message with extra fields."""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.info(message, extra=extra)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with extra fields."""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.warning(message, extra=extra)
    
    def error(self, message: str, **kwargs):
        """Log error message with extra fields."""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.error(message, extra=extra)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with extra fields."""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.critical(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with extra fields."""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.debug(message, extra=extra)


def setup_logging(settings: Settings) -> None:
    """
    Setup logging configuration for the entire application.
    
    Args:
        settings: Application settings containing logging configuration
    """
    # Create logs directory if it doesn't exist
    log_file_path = Path(settings.logging.file_path)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.logging.level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.logging.level.upper()))
    
    if settings.logging.format.lower() == "json":
        console_formatter = JSONFormatter()
    else:
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logging.file_path,
        maxBytes=_parse_size(settings.logging.rotation_size),
        backupCount=settings.logging.retention_days,
        encoding="utf-8"
    )
    file_handler.setLevel(getattr(logging, settings.logging.level.upper()))
    
    if settings.logging.format.lower() == "json":
        file_formatter = JSONFormatter()
    else:
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Configure basic logging (structlog disabled)
    # if settings.logging.format.lower() == "json":
    #     structlog.configure(
    #         processors=[
    #             structlog.stdlib.filter_by_level,
    #             structlog.stdlib.add_logger_name,
    #             structlog.stdlib.add_log_level,
    #             structlog.stdlib.PositionalArgumentsFormatter(),
    #             structlog.processors.TimeStamper(fmt="iso"),
    #             structlog.processors.StackInfoRenderer(),
    #             structlog.processors.format_exc_info,
    #             structlog.processors.UnicodeDecoder(),
    #             structlog.processors.JSONRenderer()
    #         ],
    #         context_class=dict,
    #         logger_factory=LoggerFactory(),
    #         wrapper_class=structlog.stdlib.BoundLogger,
    #         cache_logger_on_first_use=True,
    #     )
    
    # Set specific log levels for external libraries
    logging.getLogger("ccxt").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("websocket").setLevel(logging.WARNING)
    
    # Create application-specific loggers
    trading_logger = logging.getLogger("trading")
    analysis_logger = logging.getLogger("analysis")
    risk_logger = logging.getLogger("risk")
    portfolio_logger = logging.getLogger("portfolio")
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging system initialized",
        level=settings.logging.level,
        format=settings.logging.format,
        file_path=settings.logging.file_path
    )


def _parse_size(size_str: str) -> int:
    """
    Parse size string like '100MB' to bytes.
    
    Args:
        size_str: Size string (e.g., '100MB', '1GB')
        
    Returns:
        Size in bytes
    """
    size_str = size_str.upper()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_logger(name: str, settings: Settings = None) -> TradingBotLogger:
    """
    Get a logger instance for the trading bot.
    
    Args:
        name: Logger name
        settings: Application settings
        
    Returns:
        TradingBotLogger instance
    """
    if settings is None:
        # Use default settings if not provided
        from ..config.settings import Settings
        settings = Settings()
    
    return TradingBotLogger(name, settings)


class LogContext:
    """Context manager for adding context to log messages."""
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_factory = None
    
    def __enter__(self):
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            if not hasattr(record, "extra_fields"):
                record.extra_fields = {}
            record.extra_fields.update(self.context)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)
