"""
Order Management Module for AI Trading Bot System.

This module manages order execution, tracking, and lifecycle.

Author: inkbytefo
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..config.settings import Settings


class OrderType(Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderStatus(Enum):
    """Order status."""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class OrderSide(Enum):
    """Order side."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class Order:
    """Represents a trading order."""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float]
    status: OrderStatus
    created_at: datetime
    filled_quantity: float = 0.0
    filled_price: Optional[float] = None
    filled_at: Optional[datetime] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    client_order_id: Optional[str] = None
    exchange_order_id: Optional[str] = None
    error_message: Optional[str] = None


class OrderManager:
    """
    Manages order execution and tracking for the trading system.
    
    Handles order placement, monitoring, and lifecycle management
    across different exchanges.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Order tracking
        self.orders: Dict[str, Order] = {}
        self.order_counter = 0
        
        # Order execution settings
        self.max_order_age = timedelta(hours=24)  # Cancel orders after 24 hours
        self.retry_attempts = 3
        self.retry_delay = 5  # seconds
        
    async def initialize(self):
        """Initialize the order manager."""
        try:
            self.logger.info("Initializing Order Manager...")
            
            # Start order monitoring task
            asyncio.create_task(self._monitor_orders())
            
            self.logger.info("✅ Order Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Order Manager: {e}")
            raise
    
    def create_order(self, symbol: str, side: str, order_type: str,
                    quantity: float, price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    time_in_force: str = "GTC") -> str:
        """
        Create a new order.
        
        Args:
            symbol: Trading pair symbol
            side: Order side ('buy' or 'sell')
            order_type: Order type ('market', 'limit', 'stop_loss', 'take_profit')
            quantity: Order quantity
            price: Order price (for limit orders)
            stop_price: Stop price (for stop orders)
            time_in_force: Time in force
            
        Returns:
            Order ID
        """
        try:
            # Generate order ID
            self.order_counter += 1
            order_id = f"order_{self.order_counter}_{int(datetime.utcnow().timestamp())}"
            
            # Create order object
            order = Order(
                id=order_id,
                symbol=symbol,
                side=OrderSide(side.lower()),
                type=OrderType(order_type.lower()),
                quantity=quantity,
                price=price,
                status=OrderStatus.PENDING,
                created_at=datetime.utcnow(),
                stop_price=stop_price,
                time_in_force=time_in_force,
                client_order_id=order_id
            )
            
            # Store order
            self.orders[order_id] = order
            
            self.logger.info(f"Created order {order_id}: {side} {quantity} {symbol} @ {price}")
            return order_id
            
        except Exception as e:
            self.logger.error(f"Failed to create order: {e}")
            raise
    
    async def submit_order(self, order_id: str, exchange_manager) -> bool:
        """
        Submit an order to the exchange.
        
        Args:
            order_id: Order ID
            exchange_manager: Exchange manager instance
            
        Returns:
            True if order submitted successfully
        """
        try:
            if order_id not in self.orders:
                self.logger.error(f"Order {order_id} not found")
                return False
            
            order = self.orders[order_id]
            
            # Validate order
            if not self._validate_order(order):
                order.status = OrderStatus.REJECTED
                order.error_message = "Order validation failed"
                return False
            
            # Submit to exchange
            exchange_order_id = await self._submit_to_exchange(order, exchange_manager)
            
            if exchange_order_id:
                order.exchange_order_id = exchange_order_id
                order.status = OrderStatus.OPEN
                self.logger.info(f"Order {order_id} submitted successfully: {exchange_order_id}")
                return True
            else:
                order.status = OrderStatus.REJECTED
                order.error_message = "Exchange submission failed"
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to submit order {order_id}: {e}")
            if order_id in self.orders:
                self.orders[order_id].status = OrderStatus.REJECTED
                self.orders[order_id].error_message = str(e)
            return False
    
    async def _submit_to_exchange(self, order: Order, exchange_manager) -> Optional[str]:
        """Submit order to exchange with retry logic."""
        for attempt in range(self.retry_attempts):
            try:
                if self.settings.PAPER_TRADING:
                    # Simulate order execution for paper trading
                    return await self._simulate_order_execution(order)
                else:
                    # Submit to real exchange
                    return await exchange_manager.place_order(
                        symbol=order.symbol,
                        side=order.side.value,
                        type=order.type.value,
                        quantity=order.quantity,
                        price=order.price,
                        stop_price=order.stop_price,
                        time_in_force=order.time_in_force
                    )
                    
            except Exception as e:
                self.logger.warning(f"Order submission attempt {attempt + 1} failed: {e}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    self.logger.error(f"All order submission attempts failed for {order.id}")
                    return None
        
        return None
    
    async def _simulate_order_execution(self, order: Order) -> str:
        """Simulate order execution for paper trading."""
        try:
            # Generate fake exchange order ID
            exchange_order_id = f"sim_{order.id}"
            
            # Simulate immediate execution for market orders
            if order.type == OrderType.MARKET:
                # Simulate small delay
                await asyncio.sleep(0.1)
                
                # Mark as filled
                order.status = OrderStatus.FILLED
                order.filled_quantity = order.quantity
                order.filled_price = order.price or 0.0  # Would use current market price
                order.filled_at = datetime.utcnow()
                
                self.logger.info(f"Simulated order execution: {order.id}")
            
            return exchange_order_id
            
        except Exception as e:
            self.logger.error(f"Order simulation failed: {e}")
            return None
    
    def _validate_order(self, order: Order) -> bool:
        """Validate order parameters."""
        try:
            # Check quantity
            if order.quantity <= 0:
                self.logger.error(f"Invalid quantity: {order.quantity}")
                return False
            
            # Check price for limit orders
            if order.type == OrderType.LIMIT and (not order.price or order.price <= 0):
                self.logger.error(f"Invalid price for limit order: {order.price}")
                return False
            
            # Check stop price for stop orders
            if order.type in [OrderType.STOP_LOSS, OrderType.TAKE_PROFIT]:
                if not order.stop_price or order.stop_price <= 0:
                    self.logger.error(f"Invalid stop price: {order.stop_price}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Order validation error: {e}")
            return False
    
    async def cancel_order(self, order_id: str, exchange_manager) -> bool:
        """
        Cancel an order.
        
        Args:
            order_id: Order ID
            exchange_manager: Exchange manager instance
            
        Returns:
            True if order cancelled successfully
        """
        try:
            if order_id not in self.orders:
                self.logger.error(f"Order {order_id} not found")
                return False
            
            order = self.orders[order_id]
            
            if order.status not in [OrderStatus.OPEN, OrderStatus.PENDING]:
                self.logger.warning(f"Cannot cancel order {order_id} with status {order.status}")
                return False
            
            # Cancel on exchange
            if order.exchange_order_id and not self.settings.PAPER_TRADING:
                success = await exchange_manager.cancel_order(
                    order.symbol, order.exchange_order_id
                )
                if not success:
                    return False
            
            # Update order status
            order.status = OrderStatus.CANCELLED
            
            self.logger.info(f"Order {order_id} cancelled successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """Get order by ID."""
        return self.orders.get(order_id)
    
    def get_orders(self, symbol: Optional[str] = None, 
                  status: Optional[OrderStatus] = None) -> List[Order]:
        """
        Get orders with optional filtering.
        
        Args:
            symbol: Filter by symbol
            status: Filter by status
            
        Returns:
            List of orders
        """
        orders = list(self.orders.values())
        
        if symbol:
            orders = [o for o in orders if o.symbol == symbol]
        
        if status:
            orders = [o for o in orders if o.status == status]
        
        return orders
    
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get all open orders."""
        return self.get_orders(symbol=symbol, status=OrderStatus.OPEN)
    
    def get_order_summary(self) -> Dict[str, Any]:
        """Get order summary statistics."""
        try:
            total_orders = len(self.orders)
            status_counts = {}
            
            for status in OrderStatus:
                count = len([o for o in self.orders.values() if o.status == status])
                status_counts[status.value] = count
            
            filled_orders = [o for o in self.orders.values() if o.status == OrderStatus.FILLED]
            total_volume = sum(o.filled_quantity * (o.filled_price or 0) for o in filled_orders)
            
            return {
                "total_orders": total_orders,
                "status_breakdown": status_counts,
                "filled_orders": len(filled_orders),
                "total_volume": total_volume,
                "open_orders": status_counts.get("open", 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get order summary: {e}")
            return {}
    
    async def _monitor_orders(self):
        """Monitor orders for expiration and status updates."""
        while True:
            try:
                current_time = datetime.utcnow()
                
                # Check for expired orders
                for order_id, order in list(self.orders.items()):
                    if order.status == OrderStatus.OPEN:
                        age = current_time - order.created_at
                        if age > self.max_order_age:
                            self.logger.info(f"Order {order_id} expired, cancelling...")
                            order.status = OrderStatus.EXPIRED
                
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                self.logger.error(f"Order monitoring error: {e}")
                await asyncio.sleep(60)
    
    def cleanup_old_orders(self, days: int = 7):
        """Clean up old orders from memory."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            orders_to_remove = []
            for order_id, order in self.orders.items():
                if order.created_at < cutoff_date and order.status in [
                    OrderStatus.FILLED, OrderStatus.CANCELLED, 
                    OrderStatus.REJECTED, OrderStatus.EXPIRED
                ]:
                    orders_to_remove.append(order_id)
            
            for order_id in orders_to_remove:
                del self.orders[order_id]
            
            if orders_to_remove:
                self.logger.info(f"Cleaned up {len(orders_to_remove)} old orders")
                
        except Exception as e:
            self.logger.error(f"Order cleanup failed: {e}")
